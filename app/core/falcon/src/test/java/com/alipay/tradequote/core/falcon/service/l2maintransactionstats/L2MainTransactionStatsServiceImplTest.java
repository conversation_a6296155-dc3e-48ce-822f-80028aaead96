package com.alipay.tradequote.core.falcon.service.l2maintransactionstats;

import com.alipay.quot.commons.falcon.inner.Level2Base;
import com.alipay.quote.hulk.cache.impl.HulkCacheImpl;
import com.alipay.quote.hulk.cache.meta.CacheMeta;
import com.alipay.quote.hulk.cache.provider.GuavaCacheProvider;
import com.alipay.quote.hulk.cache.provider.HbaseStorageProvider;
import com.alipay.quote.hulk.cache.provider.HulkProvider;
import com.alipay.quote.hulk.cache.provider.ProviderResolver;
import com.alipay.quote.hulk.drm.HulkCacheDrmModel;
import com.alipay.quote.hulk.enums.HashStrategyEnum;
import com.alipay.quote.hulk.model.CacheContext;
import com.alipay.quote.hulk.model.HulkCacheKey;
import com.alipay.quote.hulk.thread.DefaultCoordinator;
import com.alipay.tradequote.core.falcon.engine.context.FalconQuotEngineContext;
import com.alipay.tradequote.core.falcon.model.KeyWrapper;
import com.alipay.tradequote.core.falcon.service.snapshot.SnapshotSerialize;
import com.alipay.tradequote.core.falcon.storage.client.StorageClient;
import com.alipay.tradequote.core.falcon.storage.client.StorageClientImpl;
import com.alipay.zdal.hbase.client.impl.HbaseDataSource;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.alipay.quot.commons.models.Event.DataSource.DATA_L2_PRICE_BY_VOLUME_SNAPSHOT;

public class L2MainTransactionStatsServiceImplTest {
    private L2MainTransactionStatsServiceImpl L2MainTransactionStatsService = new L2MainTransactionStatsServiceImpl();

    private FalconQuotEngineContext engineContext;

    private HulkCacheImpl hulkCache = new HulkCacheImpl();

    private StorageClient storageClient;

    @Before
    public void setUp() throws Exception {
        engineContext = FalconQuotEngineContext.builder().build();
        GuavaCacheProvider guavaCacheProvider = new GuavaCacheProvider();
        guavaCacheProvider.setCacheStrategy(HashStrategyEnum.ASYNC_LOADALL_CACHE);
        guavaCacheProvider.setSerializeHelper(new SnapshotSerialize());
        guavaCacheProvider.setCoordinator(new DefaultCoordinator(10, 100));
        guavaCacheProvider.setDuration(100);
        guavaCacheProvider.setWrite(true);
        guavaCacheProvider.setHulkDrm(new HulkCacheDrmModel());
        HbaseStorageProvider hbaseStorageProvider = new HbaseStorageProvider();
        hbaseStorageProvider.setDataSource(new HbaseDataSource());
        hbaseStorageProvider.setStoreName("a");
        hbaseStorageProvider.setTableName("b");
        guavaCacheProvider.setNextLayer(hbaseStorageProvider);
        guavaCacheProvider.init();
        Map<String, HulkProvider> providerMap = Maps.newConcurrentMap();
        ProviderResolver providerResolver = new ProviderResolver();
        providerMap.put("L2MAINTRANSACTIONSTATS_KV", guavaCacheProvider);
        providerResolver.setProviderMap(providerMap);
        hulkCache.setProviderResolver(providerResolver);
        storageClient = new StorageClientImpl();
        ((StorageClientImpl) storageClient).setHulkCache(hulkCache);
        L2MainTransactionStatsService.setStorageClient(storageClient);
        L2MainTransactionStatsService.setStorageKey("L2MAINTRANSACTIONSTATS_KV");
    }

    @Test
    public void mgetLatestL2MainTransactionStats() throws Exception {
        //标的为空
        List<String> symbols = Lists.newArrayList();
        List<Level2Base.L2MainTransactionStats> L2MainTransactionStatsList = L2MainTransactionStatsService.mgetLatestL2MainTransactionStats(
                symbols);
        Assert.assertEquals(L2MainTransactionStatsList.size(), 0);
        //拿不到缓存
        symbols = Lists.newArrayList("v2.000002.SH.SH.L2.20210111+0800");
        L2MainTransactionStatsList = L2MainTransactionStatsService.mgetLatestL2MainTransactionStats(symbols);
        Assert.assertEquals(L2MainTransactionStatsList.size(), 0);

        //缓存里没任何数据
        L2MainTransactionStatsList = L2MainTransactionStatsService.mgetLatestL2MainTransactionStats(symbols);
        Assert.assertEquals(L2MainTransactionStatsList.size(), 0);

        addBatch();
        L2MainTransactionStatsList = L2MainTransactionStatsService.mgetLatestL2MainTransactionStats(symbols);
        Assert.assertEquals(L2MainTransactionStatsList.get(0).getSymbol(), "000002.SH");
    }

    @Test
    public void addBatch() throws Exception {
        L2MainTransactionStatsService.addBatch(Arrays.asList(query()));
        HulkCacheKey rowKey = new KeyWrapper(DATA_L2_PRICE_BY_VOLUME_SNAPSHOT.getNumber(),
                "v2." + "000002.SH.SH.L2" + "." + "20210111+0800");
        CacheContext context = CacheContext.builder()
                .cacheMeta(CacheMeta.builder().group("L2MAINTRANSACTIONSTATS_KV").build()).build();

        hulkCache.get(context, rowKey);

        L2MainTransactionStatsService.addBatch(Arrays.asList(query(), null));
    }

    @Test
    public void forceWrite() throws Exception {
        ((StorageClientImpl) storageClient).setHulkCache(hulkCache);
        L2MainTransactionStatsService.forceWrite(Arrays.asList(query()));
        Assert.assertTrue(L2MainTransactionStatsService.addBatch(new ArrayList<>()));
        ((StorageClientImpl) storageClient).setHulkCache(hulkCache);

        L2MainTransactionStatsService.addBatch(Arrays.asList(query(), null));
    }

    @Test
    public void setStorageClient() {
        L2MainTransactionStatsService.setStorageClient(storageClient);
        Assert.assertNotNull(L2MainTransactionStatsService.getStorageClient());
    }

    @Test
    public void setStorageKey() {
        L2MainTransactionStatsService.setStorageKey(null);
        Assert.assertNotNull(L2MainTransactionStatsService.getStorageClient());
    }

    @Test
    public void getStorageClient() {
        Assert.assertNotNull(L2MainTransactionStatsService.getStorageClient());
    }

    @Test
    public void getStorageKey() {
        L2MainTransactionStatsService.setStorageKey("1");
        Assert.assertNotNull(L2MainTransactionStatsService.getStorageKey());
    }

    private Level2Base.L2MainTransactionStats query() {
        return Level2Base.L2MainTransactionStats.newBuilder().setSymbol("000002.SH")
                .setChannelExchange("SH").setChannelLevel("L2").setDate(1610361000833L).build();
    }
}