package com.alipay.tradequote.core.falcon.service.l2maintransaction;

import com.alipay.quot.commons.falcon.inner.Level2Base;
import com.alipay.tradequote.core.falcon.model.DataWrapper;
import com.alipay.tradequote.core.falcon.model.KeyWrapper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class L2MainTransactionSerializeTest {
    L2MainTransactionSerialize serialize;

    @Before
    public void setUp() throws Exception {
        serialize = new L2MainTransactionSerialize();
    }

    @Test
    public void serializeK() {
        KeyWrapper keyWrapper = new KeyWrapper(1, "600570.SH.SH.L1");
        Assert.assertNotNull(serialize.serializeK(keyWrapper, KeyWrapper.class));

    }

    @Test
    public void deserializeK() {
        KeyWrapper keyWrapper = new KeyWrapper(1, "600570.SH.SH.L1");
        Assert.assertNotNull(serialize.deserializeK(keyWrapper.getBytes(), KeyWrapper.class));
    }

    @Test
    public void serializeV() {
        DataWrapper dataWrapper = makeDataWraper();
        Assert.assertNotNull(serialize.serializeV(dataWrapper, DataWrapper.class));
        Assert.assertNotNull(serialize.serializeV(null, DataWrapper.class));
    }

    @Test
    public void deserializeV() {
        DataWrapper dataWrapper = makeDataWraper();
        Assert.assertNotNull(serialize.deserializeV(dataWrapper.getData(), DataWrapper.class));
        Assert.assertNull(serialize.deserializeV(null, DataWrapper.class));
        Assert.assertNull(serialize.deserializeV(new byte[0], DataWrapper.class));
    }

    private DataWrapper makeDataWraper() {
        return DataWrapper.builder()
                .data(makeDO().toByteArray())
                .build();
    }

    private Level2Base.L2MainTransaction makeDO() {
        return Level2Base.L2MainTransaction.newBuilder()
                .setDate(1000)
                .build();
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme