/*
 * Ant Group
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.alipay.nimitzquote.prod.common.dal.support;

import org.apache.ibatis.session.ExecutorType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 注解
 * <AUTHOR>
 * @version TableWithoutEnvIsolation.java, v 0.1 2022年10月18日 3:51 下午 suchengfei
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface TableWithoutEnvIsolation {

    /**
     * 数据源枚举
     */
    enum DataSource {
        //quotstore库
        QUOTSTORE,
        //默认兜底值
        UNKNOWN
    }

    /**
     * 数据源枚举
     */
    enum TableName {
        //码表发号表
        SYMBOL_VID,
        //默认兜底值
        UNKNOWN
    }



    /**
     * 数据源名称
     * @return 数据源名称
     */
    DataSource dataSource() default DataSource.UNKNOWN;

    TableName tableName() default TableName.UNKNOWN;;

    ExecutorType executorType() default ExecutorType.SIMPLE;;
}