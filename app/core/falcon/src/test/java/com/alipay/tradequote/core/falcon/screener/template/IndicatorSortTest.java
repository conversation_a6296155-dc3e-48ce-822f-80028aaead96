/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.template;

import com.alipay.quot.commons.facade.model.querycond.PageCond;
import com.alipay.quot.commons.facade.model.querycond.PercentCond;
import com.alipay.quot.commons.facade.model.querycond.RoundModeEnum;
import com.alipay.quot.commons.facade.model.querycond.SortCond;
import com.alipay.tradequote.core.falcon.screener.AbstractScreenerTest;
import com.alipay.tradequote.core.falcon.screener.config.ScreenConfigServiceImpl;
import com.alipay.tradequote.core.falcon.screener.config.ScreenDrm;
import com.alipay.tradequote.core.falcon.screener.model.template.Context;
import com.alipay.tradequote.core.falcon.screener.model.template.ScreenObj;
import com.alipay.tradequote.core.falcon.screener.model.template.SortRequest;
import com.alipay.tradequote.core.falcon.screener.model.template.SortResult;
import com.alipay.tradequote.core.falcon.screener.template.impl.IndicatorSort;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 */
public class IndicatorSortTest extends AbstractScreenerTest {
    // context
    Context context = Context.builder().build();

    @Test
    public void test1() {
        try {
            IndicatorSort sort = new IndicatorSort();
            sort.execute(context, SortRequest.builder().build());
        } catch (Exception e) {
            assertTrue(e instanceof IllegalArgumentException);
        }
    }

    @Test
    public void test2() throws Exception {
        List<ScreenObj> objs = getScreenObjs("objs.json");
        assertNotNull(objs);
        SortRequest request = SortRequest.builder()
                .objs(objs)
                .sortConds(Lists.newArrayList())
                .pageCond(PageCond.builder()
                        .offset(0)
                        .limit(4)
                        .build())
                .build();
        IndicatorSort sort = new IndicatorSort();
        ScreenDrm screenDrm = new ScreenDrm();
        ScreenConfigServiceImpl screenConfigService = new ScreenConfigServiceImpl();
        screenConfigService.setScreenDrm(screenDrm);
        sort.setScreenConfigService(screenConfigService);
        SortResult result = sort.execute(context, request);
        assertNotNull(request);
        assertEquals(4, result.getObjs().size());
        assertSameOrder(result.getObjs(), Lists.newArrayList("000848.SZ", "002304.SZ", "002646.SZ", "600197.SH"));
    }

    @Test
    public void test3() throws Exception {
        List<ScreenObj> objs = getScreenObjs("objs.json");
        assertNotNull(objs);
        SortRequest request = SortRequest.builder()
                .objs(objs)
                .sortConds(Lists.newArrayList(SortCond.builder()
                        .field("CHANGE_PERCENT")
                        .desc(true)
                        .build()))
                .pageCond(PageCond.builder()
                        .offset(0)
                        .limit(4)
                        .build())
                .build();
        IndicatorSort sort = new IndicatorSort();
        ScreenDrm screenDrm = new ScreenDrm();
        ScreenConfigServiceImpl screenConfigService = new ScreenConfigServiceImpl();
        screenConfigService.setScreenDrm(screenDrm);
        sort.setScreenConfigService(screenConfigService);
        SortResult result = sort.execute(context, request);
        assertNotNull(request);
        assertEquals(4, result.getObjs().size());
        assertSameOrder(result.getObjs(), Lists.newArrayList("002646.SZ", "600197.SH", "603198.SH", "603369.SH"));
    }

    @Test
    public void test4() throws Exception {
        List<ScreenObj> objs = getScreenObjs("objs.json");
        assertNotNull(objs);
        SortRequest request = SortRequest.builder()
                .objs(objs)
                .sortConds(Lists.newArrayList(
                        SortCond.builder()
                                .field("CHANGE_PERCENT")
                                .desc(true)
                                .build(),
                        SortCond.builder()
                                .field("BELONG_TO")
                                .desc(false)
                                .build()))
                .pageCond(PageCond.builder()
                        .offset(0)
                        .limit(4)
                        .build())
                .build();
        IndicatorSort sort = new IndicatorSort();
        ScreenDrm screenDrm = new ScreenDrm();
        ScreenConfigServiceImpl screenConfigService = new ScreenConfigServiceImpl();
        screenConfigService.setScreenDrm(screenDrm);
        sort.setScreenConfigService(screenConfigService);
        SortResult result = sort.execute(context, request);
        assertNotNull(request);
        assertEquals(4, result.getObjs().size());
        assertSameOrder(result.getObjs(), Lists.newArrayList("002646.SZ", "600197.SH", "603198.SH", "603369.SH"));
    }

    private void assertSameOrder(List<ScreenObj> objs, List<String> syms) {
        Preconditions.checkArgument(objs != null);
        Preconditions.checkArgument(syms != null);
        Preconditions.checkArgument(objs.size() == syms.size());

        for (int i = 0; i < objs.size(); i++) {
            ScreenObj obj = objs.get(i);
            String sym = syms.get(i);
            assertEquals(sym, obj.getObjId());
        }
    }

    /**
     * 开启新排序，回归验证
     *
     * @throws Exception
     */
    @Test
    public void test5() throws Exception {
        List<ScreenObj> objs = getScreenObjs("objs.json");
        assertNotNull(objs);
        SortRequest request = SortRequest.builder()
                .objs(objs)
                .sortConds(Lists.newArrayList(
                        SortCond.builder()
                                .field("CHANGE_PERCENT")
                                .desc(true)
                                .build(),
                        SortCond.builder()
                                .field("BELONG_TO")
                                .desc(false)
                                .build()))
                .pageCond(PageCond.builder()
                        .offset(0)
                        .limit(4)
                        .build())
                .build();
        IndicatorSort sort = new IndicatorSort();
        ScreenDrm screenDrm = new ScreenDrm();
        ScreenConfigServiceImpl screenConfigService = new ScreenConfigServiceImpl();
        screenConfigService.setScreenDrm(screenDrm);
        screenDrm.setOpenNewComparator(true);
        sort.setScreenConfigService(screenConfigService);
        SortResult result = sort.execute(context, request);
        assertNotNull(request);
        assertEquals(4, result.getObjs().size());
        assertSameOrder(result.getObjs(), Lists.newArrayList("002646.SZ", "600197.SH", "603198.SH", "603369.SH"));
    }

    @Test
    public void test6() throws Exception {
        List<ScreenObj> objs = getScreenObjs("objs.json");
        assertNotNull(objs);
        SortRequest request = SortRequest.builder()
                .objs(objs)
                .sortConds(Lists.newArrayList(SortCond.builder()
                        .field("CHANGE_PERCENT")
                        .desc(true)
                        .build()))
                .pageCond(PageCond.builder()
                        .offset(0)
                        .limit(4)
                        .build())
                .build();
        IndicatorSort sort = new IndicatorSort();
        ScreenDrm screenDrm = new ScreenDrm();
        ScreenConfigServiceImpl screenConfigService = new ScreenConfigServiceImpl();
        screenConfigService.setScreenDrm(screenDrm);
        screenDrm.setOpenNewComparator(true);
        sort.setScreenConfigService(screenConfigService);
        SortResult result = sort.execute(context, request);
        assertNotNull(request);
        assertEquals(4, result.getObjs().size());
        assertSameOrder(result.getObjs(), Lists.newArrayList("002646.SZ", "600197.SH", "603198.SH", "603369.SH"));
    }

    @Test
    public void test7() throws Exception {
        List<ScreenObj> objs = getScreenObjs("comparatorObjs.json");
        assertNotNull(objs);
        SortRequest request = SortRequest.builder()
                .objs(objs)
                .sortConds(Lists.newArrayList(
                        SortCond.builder()
                                .field("CHANGE_PERCENT")
                                .desc(false)
                                .build(),
                        SortCond.builder()
                                .field("BELONG_TO")
                                .desc(false)
                                .build()))
                .pageCond(PageCond.builder()
                        .offset(0)
                        .limit(4)
                        .build())
                .build();
        IndicatorSort sort = new IndicatorSort();
        ScreenDrm screenDrm = new ScreenDrm();
        ScreenConfigServiceImpl screenConfigService = new ScreenConfigServiceImpl();
        screenConfigService.setScreenDrm(screenDrm);
        screenDrm.setOpenNewComparator(true);
        sort.setScreenConfigService(screenConfigService);
        SortResult result = sort.execute(context, request);
        assertNotNull(request);
        assertEquals(4, result.getObjs().size());
        assertSameOrder(result.getObjs(), Lists.newArrayList("600199.SH", "600702.SH", "603919.SH", "603589.SH"));
    }

    @Test
    public void test8() throws Exception {
        List<ScreenObj> objs = getScreenObjs("comparatorObjs.json");
        assertNotNull(objs);
        SortRequest request = SortRequest.builder()
                .objs(objs)
                .sortConds(Lists.newArrayList(
                        SortCond.builder()
                                .field("CHANGE_PERCENT")
                                .desc(true)
                                .build(),
                        SortCond.builder()
                                .field("BELONG_TO")
                                .desc(true)
                                .build()))
                .pageCond(PageCond.builder()
                        .offset(0)
                        .limit(4)
                        .build())
                .build();
        IndicatorSort sort = new IndicatorSort();
        ScreenDrm screenDrm = new ScreenDrm();
        ScreenConfigServiceImpl screenConfigService = new ScreenConfigServiceImpl();
        screenConfigService.setScreenDrm(screenDrm);
        screenDrm.setOpenNewComparator(true);
        sort.setScreenConfigService(screenConfigService);
        SortResult result = sort.execute(context, request);
        assertNotNull(request);
        assertEquals(4, result.getObjs().size());
        assertSameOrder(result.getObjs(), Lists.newArrayList("002646.SZ", "600197.SH", "603198.SH", "603369.SH"));
    }

    @Test
    public void test9() throws Exception {
        List<ScreenObj> objs = getScreenObjs("comparatorObjs.json");
        assertNotNull(objs);
        SortRequest request = SortRequest.builder()
                .objs(objs)
                .sortConds(Lists.newArrayList(
                        SortCond.builder()
                                .field("CHANGE_PERCENT")
                                .desc(true)
                                .build(),
                        SortCond.builder()
                                .field("BELONG_TO")
                                .desc(true)
                                .build()))
                .pageCond(PageCond.builder()
                        .offset(0)
                        .limit(4)
                        .build())
                .percentCond(PercentCond.builder()
                        .percent(70)
                        .roundMode(RoundModeEnum.ROUND_DOWN)
                        .build())
                .build();
        IndicatorSort sort = new IndicatorSort();
        ScreenDrm screenDrm = new ScreenDrm();
        ScreenConfigServiceImpl screenConfigService = new ScreenConfigServiceImpl();
        screenConfigService.setScreenDrm(screenDrm);
        screenDrm.setOpenNewComparator(true);
        screenDrm.setOpenPercentCond(true);
        sort.setScreenConfigService(screenConfigService);
        SortResult result = sort.execute(context, request);
        assertNotNull(request);
        assertEquals(2, result.getObjs().size());
        assertSameOrder(result.getObjs(), Lists.newArrayList("002646.SZ", "600197.SH"));
    }

    @Test
    public void test10() throws Exception {
        List<ScreenObj> objs = getScreenObjs("comparatorObjs.json");
        assertNotNull(objs);
        SortRequest request = SortRequest.builder()
                .objs(objs)
                .sortConds(Lists.newArrayList(
                        SortCond.builder()
                                .field("CHANGE_PERCENT")
                                .desc(true)
                                .build(),
                        SortCond.builder()
                                .field("BELONG_TO")
                                .desc(true)
                                .build()))
                .pageCond(PageCond.builder()
                        .offset(0)
                        .limit(4)
                        .build())
                .percentCond(PercentCond.builder()
                        .percent(70)
                        .roundMode(RoundModeEnum.ROUND_UP)
                        .build())
                .build();
        IndicatorSort sort = new IndicatorSort();
        ScreenDrm screenDrm = new ScreenDrm();
        ScreenConfigServiceImpl screenConfigService = new ScreenConfigServiceImpl();
        screenConfigService.setScreenDrm(screenDrm);
        screenDrm.setOpenNewComparator(true);
        screenDrm.setOpenPercentCond(true);
        sort.setScreenConfigService(screenConfigService);
        SortResult result = sort.execute(context, request);
        assertNotNull(request);
        assertEquals(3, result.getObjs().size());
        assertSameOrder(result.getObjs(), Lists.newArrayList("002646.SZ", "600197.SH", "603198.SH"));
    }
}
