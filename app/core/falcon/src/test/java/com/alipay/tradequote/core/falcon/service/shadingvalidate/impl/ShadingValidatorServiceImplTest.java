/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.service.shadingvalidate.impl;

import com.alipay.tradequote.core.falcon.service.shadingvalidate.impl.ShadingValidatorServiceImpl;
import com.alipay.tradequote.core.falcon.service.shadingvalidate.config.ShadingConfigService;
import com.alipay.tradequote.core.falcon.service.shadingvalidate.model.DailyTimeValidator;
import com.alipay.tradequote.core.falcon.service.shadingvalidate.model.ValidateDescriptor;
import com.alipay.tradequote.meta.service.impl.trading.TradingService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doReturn;

/**
 * <AUTHOR>
 * @version ShadingValidatorTest.java, v 0.1 2023年06月12日 11:54 lzt
 */
public class ShadingValidatorServiceImplTest {


    @InjectMocks
    ShadingValidatorServiceImpl shadingValidatorServiceImpl;

    @Mock
    private ShadingConfigService shadingConfigService;

    @Mock
    private TradingService tradingService;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        shadingValidatorServiceImpl = new ShadingValidatorServiceImpl();
        shadingValidatorServiceImpl.setShadingConfigService(shadingConfigService);
        shadingValidatorServiceImpl.setTradingService(tradingService);
    }

    @Test
    public void preCheck() {
        List<DailyTimeValidator> validators = Lists.newArrayList(getDailyTimeValidator("00:00:00.000000","03:00:00.999999"),
                getDailyTimeValidator("16:00:00.000000","23:59:59.999999"));
        ValidateDescriptor descriptor = getValidateDescriptor("rankId", "invalidZoneId", true, true, "1", validators);
        doReturn(Sets.newHashSet()).when(shadingConfigService).getRankIdSet();
        boolean result = shadingValidatorServiceImpl.preCheck(descriptor);
        Assert.assertFalse(result);

        ValidateDescriptor descriptor1 = getValidateDescriptor("rankId", "invalidZoneId", true, true, "1", validators);
        doReturn(Sets.newHashSet("rankId")).when(shadingConfigService).getRankIdSet();
        boolean result1 = shadingValidatorServiceImpl.preCheck(descriptor1);
        Assert.assertFalse(result1);

        ValidateDescriptor descriptor2 = getValidateDescriptor("rankId", "invalidZoneId", true, false, "1", validators);
        boolean result2 = shadingValidatorServiceImpl.preCheck(descriptor2);
        Assert.assertFalse(result2);

        ValidateDescriptor descriptor3 = getValidateDescriptor("rankId", "Asia/Shanghai", true, false, "1", validators);
        boolean result3 = shadingValidatorServiceImpl.preCheck(descriptor3);
        Assert.assertFalse(result3);

        ValidateDescriptor descriptor4 = getValidateDescriptor("rankId", "Asia/Shanghai", true, false, "1", validators);
        doReturn(Sets.newHashSet("1")).when(shadingConfigService).getTradingTimeIdSet();
        boolean result4 = shadingValidatorServiceImpl.preCheck(descriptor4);
        Assert.assertTrue(result4);
    }

    @Test
    public void validate(){
        List<DailyTimeValidator> validators = Lists.newArrayList(getDailyTimeValidator("00:00:00.000000","03:00:00.999999"),
                getDailyTimeValidator("16:00:00.000000","23:59:59.999999"));

        //2023-06-12 14:36:24
        ValidateDescriptor descriptor = getValidateDescriptor("rankId", "Asia/Shanghai", true, false, "1", validators);
        doReturn(true).when(tradingService).isTradingDay(anyLong(),any());
        boolean validate = shadingValidatorServiceImpl.validate(descriptor, 1686551784425L);
        Assert.assertFalse(validate);


        //2023-06-12 00:36:24
        boolean validate1 = shadingValidatorServiceImpl.validate(descriptor, 1686501384000L);
        Assert.assertTrue(validate1);

        //2023-06-12 21:36:24
        boolean validate2 = shadingValidatorServiceImpl.validate(descriptor, 1686576984000L);
        Assert.assertTrue(validate2);

        //2023-06-12 14:36:24
        doReturn(false).when(tradingService).isTradingDay(anyLong(),any());
        boolean validate3 = shadingValidatorServiceImpl.validate(descriptor, 1686551784425L);
        Assert.assertTrue(validate3);

        ValidateDescriptor descriptor1 = getValidateDescriptor("rankId", "Asia/Shanghai", true,
                false, "1", null);
        boolean validate4 = shadingValidatorServiceImpl.validate(descriptor1, 1686551784425L);
        Assert.assertTrue(validate4);

        doReturn(true).when(tradingService).isTradingDay(anyLong(),any());
        boolean validate5 = shadingValidatorServiceImpl.validate(descriptor1, 1686551784425L);
        Assert.assertFalse(validate5);

    }



    private ValidateDescriptor getValidateDescriptor(String rankId, String zoneId, boolean isTradingDay,
                                                     boolean stop, String tradingTimeId, List<DailyTimeValidator> timeValidators) {

        return ValidateDescriptor.builder()
                .rankId(rankId)
                .isTradingDayValidate(isTradingDay)
                .stop(stop)
                .tradingTimeId(tradingTimeId)
                .dailyTimeValidators(timeValidators)
                .zoneId(zoneId)
                .build();
    }

    private DailyTimeValidator getDailyTimeValidator(String startTime,String endTime){
        return DailyTimeValidator.builder().startTime(startTime).endTime(endTime).build();
    }
}