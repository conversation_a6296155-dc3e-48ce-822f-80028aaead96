/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.sched;

import com.alipay.quot.commons.facade.request.NimitzDatasetRequest;
import com.alipay.quot.commons.facade.request.NimitzQueryContext;
import com.alipay.quot.commons.models.Symbol;
import com.alipay.quot.commons.push.models.base.NimitzTablePb;
import com.alipay.quot.commons.push.models.base.RsdataPb;
import com.alipay.tradequote.core.falcon.nimitz.service.NimitzDataEngine;
import com.alipay.tradequote.core.falcon.screener.AbstractScreenerTest;
import com.alipay.tradequote.core.falcon.screener.indicator.IndicatorFrameService;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorDescriptor;
import com.alipay.tradequote.core.falcon.screener.model.JobDescriptor;
import com.alipay.tradequote.core.falcon.screener.sched.job.DsIndicatorViewJob;
import com.alipay.tradequote.meta.service.SymbolGqlService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.nio.charset.StandardCharsets.ISO_8859_1;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @version DsIndicatorViewJobTest.java, v 0.1 2023年05月23日 16:36 lzt
 */
public class DsIndicatorViewJobTest extends AbstractScreenerTest {

    @InjectMocks
    DsIndicatorViewJob dsIndicatorViewJob;

    @Mock
    SymbolGqlService symbolGqlService;

    @Mock
    NimitzDataEngine nimitzDataEngine;

    @Mock
    IndicatorFrameService indicatorFrameService;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        dsIndicatorViewJob = new DsIndicatorViewJob();
        dsIndicatorViewJob.setSymbolGqlService(symbolGqlService);
        dsIndicatorViewJob.setNimitzDataEngine(nimitzDataEngine);
        dsIndicatorViewJob.setIndicatorFrameService(indicatorFrameService);
    }

    @Test
    public void jobNameTest() {
        Assert.assertEquals("DS_INDICATOR_VIEW_JOB", dsIndicatorViewJob.getName());
    }

    @Test
    public void test() {
        List<String> symbols = Lists.newArrayList("600519.SH",
                "000002.SZ",
                "601318.SH");

        ScreenJobContext screenJobContext = getScreenJobContext(symbols);
        List<Symbol.SymbolDO> objSymbols = makeObjSymbols(symbols);
        doReturn(objSymbols)
                .when(symbolGqlService)
                .querySymbol(anyString());
        doReturn(Lists.newArrayList(
                RsdataPb.ObjRsData.newBuilder()
                        .setData(RsdataPb.RsData.newBuilder()
                                .setRsDataset("INDICATOR")
                                .setData(getIndicatorTable())
                                .build())
                        .setObjDimKey("DimKeys")
                        .build()))
                .when(nimitzDataEngine)
                .queryDatasetPb(any(NimitzDatasetRequest.class), any(NimitzQueryContext.class));

        doNothing()
                .when(indicatorFrameService)
                .putFrame(anyString(), anyString(), any());
        dsIndicatorViewJob.execute(screenJobContext);
    }

    @Test
    public void testReturnNull() {
        List<String> symbols = Lists.newArrayList("600519.SH",
                "000002.SZ",
                "601318.SH");

        ScreenJobContext screenJobContext = getScreenJobContext(symbols);
        List<Symbol.SymbolDO> objSymbols = makeObjSymbols(symbols);
        doReturn(objSymbols)
                .when(symbolGqlService)
                .querySymbol(anyString());
        doReturn(Lists.newArrayList())
                .when(nimitzDataEngine)
                .queryDatasetPb(any(NimitzDatasetRequest.class), any(NimitzQueryContext.class));

        doNothing()
                .when(indicatorFrameService)
                .putFrame(anyString(), anyString(), any());
        dsIndicatorViewJob.execute(screenJobContext);

        verify(indicatorFrameService, Mockito.times(3)).putFrame(anyString(), anyString(), any());
    }

    @Test
    public void testThrowException() {
        List<String> symbols = Lists.newArrayList("600519.SH",
                "000002.SZ",
                "601318.SH");

        ScreenJobContext screenJobContext = getScreenJobContext(symbols);
        List<Symbol.SymbolDO> objSymbols = makeObjSymbols(symbols);
        doReturn(objSymbols)
                .when(symbolGqlService)
                .querySymbol(anyString());
        doThrow(new RuntimeException())
                .when(nimitzDataEngine)
                .queryDatasetPb(any(NimitzDatasetRequest.class), any(NimitzQueryContext.class));

        doNothing()
                .when(indicatorFrameService)
                .putFrame(anyString(), anyString(), any());
        dsIndicatorViewJob.execute(screenJobContext);

        verify(indicatorFrameService, Mockito.times(0)).putFrame(anyString(), anyString(), any());
    }

    private ScreenJobContext getScreenJobContext(List<String> symbols) {
        String gql = "gql=" + String.join(",", symbols);
        JobDescriptor jobDsp = new JobDescriptor();
        jobDsp.setName("DS_INDICATOR_VIEW_JOB");
        jobDsp.setGql(gql);

        IndicatorDescriptor i1 = new IndicatorDescriptor();
        i1.setName("PE_TTM");
        i1.setField("1");
        IndicatorDescriptor i2 = new IndicatorDescriptor();
        i2.setName("MKT_CAP");
        i2.setField("2");
        jobDsp.setIndicators(Lists.newArrayList(i1, i2));

        Map<String, Object> map = new HashMap<>();
        map.put("rsDataSet", "INDICATOR");
        map.put("dimKeys", "INDICATOR");
        jobDsp.setExtProps(map);

        return ScreenJobContext.builder()
                .jobDescriptor(jobDsp)
                .listTypes(listTypes())
                .build();
    }

    private static NimitzTablePb.NimitzTable getIndicatorTable() {
        return NimitzTablePb.NimitzTable.newBuilder()
                .addAllColumns(Lists.newArrayList(
                        getColumn("value", "KString", Lists.newArrayList(NimitzTablePb.NimitzCell.newBuilder()
                                .setSVal(getNimitzTable().toByteString().toString(ISO_8859_1))
                                .build()))))
                .build();
    }


    private static NimitzTablePb.NimitzTable getNimitzTable() {
        return NimitzTablePb.NimitzTable.newBuilder()
                .addAllColumns(Lists.newArrayList(
                        getColumn("1", "KString", Lists.newArrayList(NimitzTablePb.NimitzCell.newBuilder()
                                .setSVal("123")
                                .build())),
                        getColumn("2", "KInt64", Lists.newArrayList(NimitzTablePb.NimitzCell.newBuilder()
                                .setI64Val(123L)
                                .build()))))
                .build();
    }

    private static NimitzTablePb.NimitzColumn getColumn(String name, String kind, List<NimitzTablePb.NimitzCell> cells) {
        NimitzTablePb.NimitzColumn.Builder builder = NimitzTablePb.NimitzColumn.newBuilder()
                .setName(name)
                .setKind(kind);
        if (CollectionUtils.isNotEmpty(cells)) {
            builder.addAllCells(cells);
        }
        return builder.build();
    }
}