/*
 * Ant Group
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.template;

import com.alipay.quote.nerve.qsql.lib.value.Value;
import com.alipay.quote.nerve.qsql.lib.value.primitive.DoubleValue;
import com.alipay.quote.nerve.qsql.lib.value.primitive.NullValue;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorFrame;
import com.alipay.tradequote.core.falcon.screener.model.template.ScreenObj;
import com.alipay.tradequote.core.falcon.screener.template.impl.NullScreenObjIndicatorComparator;
import com.google.common.collect.Maps;
import org.junit.Test;

import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR>
 * @version NullScreenObjIndicatorComparatorTest.java, v 0.1 2024年02月04日 11:19 lzt
 */
public class NullScreenObjIndicatorComparatorTest {

    @Test
    public void test() {
        NullScreenObjIndicatorComparator comparator = new NullScreenObjIndicatorComparator(false, "amount");
        ScreenObj obj1 = getScreenObj("600519.SH", DoubleValue.of(1.3d));
        ScreenObj obj2 = getScreenObj("600510.SH", DoubleValue.of(1.5d));
        ScreenObj obj3 = getScreenObj("600512.SH", DoubleValue.of(Double.NaN));
        ScreenObj obj4 = getScreenObj("600511.SH", new NullValue());
        ScreenObj obj5 = getScreenObj("600518.SH", DoubleValue.of(1.3d));
        ScreenObj obj6 = getScreenObj("600512.SH", DoubleValue.of(Double.NaN));
        ScreenObj obj7 = getScreenObj("600511.SH", new NullValue());

        assertEquals(-1, comparator.compare(obj1, obj2));
        assertEquals(-1, comparator.compare(obj1, obj3));
        assertEquals(-1, comparator.compare(obj1, obj4));
        assertEquals(0, comparator.compare(obj1, obj5));
        assertEquals(-1, comparator.compare(obj2, obj3));
        assertEquals(1, comparator.compare(obj3, obj2));
        assertEquals(-1, comparator.compare(obj3, obj4));
        assertEquals(0, comparator.compare(obj3, obj6));
        assertEquals(0, comparator.compare(obj4, obj7));

        Comparator<ScreenObj> reversedComparator = comparator.reversedComparator();

        assertEquals(1, reversedComparator.compare(obj1, obj2));
        assertEquals(-1, reversedComparator.compare(obj1, obj3));
        assertEquals(-1, reversedComparator.compare(obj1, obj4));
        assertEquals(0, reversedComparator.compare(obj1, obj5));
        assertEquals(-1, reversedComparator.compare(obj2, obj3));
        assertEquals(1, reversedComparator.compare(obj3, obj2));
        assertEquals(-1, reversedComparator.compare(obj3, obj4));
        assertEquals(0, reversedComparator.compare(obj3, obj6));
        assertEquals(0, reversedComparator.compare(obj4, obj7));
    }

    @Test
    public void test2() {
        ScreenObj obj1 = getScreenObj("600519.SH", DoubleValue.of(1.3d));
        ScreenObj obj2 = getEmptyScreenObj("600518.SH");
        NullScreenObjIndicatorComparator comparator = new NullScreenObjIndicatorComparator(false, "amount");

        assertEquals(-1, comparator.compare(obj1, obj2));
    }

    private ScreenObj getScreenObj(String symbol, Value value) {
        ScreenObj screenObj = new ScreenObj(symbol, "view_screener");
        Map<String, Value> data = new HashMap<>();
        data.put("amount", value);
        screenObj.setFrame(new IndicatorFrame(data));
        return screenObj;
    }

    private ScreenObj getEmptyScreenObj(String symbol) {
        ScreenObj screenObj = new ScreenObj(symbol, "view_screener");
        screenObj.setFrame(new IndicatorFrame(Maps.newConcurrentMap()));
        return screenObj;
    }
}