/*
 * Ant Group
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener;
import com.alipay.tradequote.core.falcon.screener.indicator.IndicatorFrameServiceImpl;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ScreenCache;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ObjIdKeyWrapper;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.IndicatorDataWrapper;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorFrame;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.when;
/**
 * <AUTHOR>
 * @version IndicatorFrameServiceTest.java, v 0.1 2024年02月22日 19:00 lzt
 */
public class IndicatorFrameServiceTest {

    @InjectMocks
    private IndicatorFrameServiceImpl indicatorFrameServiceImpl;

    @Mock
    private ScreenCache screenCache;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetFrame() {
        IndicatorFrame view = new IndicatorFrame();
        IndicatorDataWrapper wrapper = new IndicatorDataWrapper(view);
        when(screenCache.get(new ObjIdKeyWrapper("dataType", "objId"))).thenReturn(wrapper);

        IndicatorFrame frame = indicatorFrameServiceImpl.getFrame("dataType", "objId");
        assertNotNull(frame);
    }
}