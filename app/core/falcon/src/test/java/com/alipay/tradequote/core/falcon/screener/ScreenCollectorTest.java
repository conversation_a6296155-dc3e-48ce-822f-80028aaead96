/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener;

import com.alipay.tradequote.core.falcon.screener.util.ScreenCollectors;
import com.google.common.collect.Lists;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.Assert.*;


/**
 * <AUTHOR>
 */
public class ScreenCollectorTest {

    @Test
    public void test1() {
        List<Integer> origin = Lists.newArrayList(1, 2, 3);

        List<Integer> l1 = new ArrayList<>(origin);
        List<Integer> l2 = origin.stream().collect(ScreenCollectors.toList(origin.size()));
        assertEquals(l1.size(), l2.size());
        for (int i = 0; i < origin.size(); i++) {
            assertEquals(l1.get(i), l2.get(i));
        }
    }
}
