/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.service;

import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerJobDescriptor;
import com.alipay.screenerquote.scannerprod.core.scanner.scheduler.model.ScannerTaskItem;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version ScannerTaskService.java, v 0.1 2025年02月21日 17:24 lzt
 */
public interface ScannerJobManager {

    /**
     * 基于expectedTriggerTime,获取所有待执行task
     */
    List<ScannerJobDescriptor> getTriggeredJob(Date expectedTriggerTime);

    /**
     * add task
     */
    void addJob(ScannerTaskItem item);
}