/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.model;

import com.alipay.screenerquote.prod.model.ScreenerObjDTO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version ScannerPortfolioResult.java, v 0.1 2025年02月25日 16:37 lzt
 */
@Getter
@Setter
@ToString
@Builder
public class ScannerPortfolio implements Serializable {
    private static final long serialVersionUID = 1493400028748657529L;

    /**
     * 策略id
     */
    private String strategyId;

    /**
     * 起始标的
     */
    private String srcId;

    /**
     * 归档时间
     */
    private long beginDate;

    /**
     * 业务时间
     */
    private long bizDate;

    /**
     * 版本号
     */
    private long snapshotDate;

    /**
     * 对应环境
     */
    private String env;

    /**
     * 标的列表
     */
    private List<ScreenerObjDTO> objs;

    /**
     * 筛选结果数量
     */
    private int count;

    /**
     * 总筛选标的数量
     */
    private int objCount;
}