/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener;

import com.alipay.quot.commons.facade.model.SymbolDTO;
import com.alipay.tradequote.core.falcon.screener.util.SymbolGQl;
import com.google.common.collect.Sets;
import org.junit.Before;
import org.junit.Test;

import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.Assert.*;


/**
 * <AUTHOR>
 */
public class SymbolGQlTest extends AbstractScreenerTest {

    SymbolGQl gql;

    List<SymbolDTO> symbols;

    @Before
    public void init() throws Exception {
        gql = new SymbolGQl();
        gql.init();
        symbols = getObjs("symbol_list_2.csv");
    }

    @Test
    public void test1() {
        SymbolGQl.ExprPredicate predicate = gql.getExprObj("mkt=SH,SZ");
        List<SymbolDTO> actual = symbols.stream().filter(predicate).collect(Collectors.toList());
        List<SymbolDTO> expected = symbols.stream().filter(s -> s.getMarket().equals("SH") || s.getMarket().equals("SZ")).collect(Collectors.toList());
        assertSymbolListEquals(expected, actual);
    }

    @Test
    public void test2() {
        SymbolGQl.ExprPredicate predicate = gql.getExprObj("mkt=SH,SZ with type=ES");
        List<SymbolDTO> actual = symbols.stream().filter(predicate).collect(Collectors.toList());
        List<SymbolDTO> expected = symbols.stream().filter(s -> {
                    return (s.getMarket().equals("SH") || s.getMarket().equals("SZ"))
                            && s.getType().equals("ES");
                })
                .collect(Collectors.toList());
        assertSymbolListEquals(expected, actual);
    }

    @Test
    public void test3() {
        SymbolGQl.ExprPredicate predicate = gql.getExprObj("mkt=SH with type=ES");
        List<SymbolDTO> actual = symbols.stream().filter(predicate).collect(Collectors.toList());
        List<SymbolDTO> expected = symbols.stream().filter(s -> {
                    return (s.getMarket().equals("SH"))
                            && s.getType().equals("ES");
                })
                .collect(Collectors.toList());
        assertSymbolListEquals(expected, actual);
    }

    @Test
    public void test4() {
        SymbolGQl.ExprPredicate predicate = gql.getExprObj("mkt=SH,SZ with type=ES,MRI");
        List<SymbolDTO> actual = symbols.stream().filter(predicate).collect(Collectors.toList());
        List<SymbolDTO> expected = symbols.stream().filter(s -> {
                    return (s.getMarket().equals("SH") || s.getMarket().equals("SZ"))
                            && (s.getType().equals("ES") || s.getType().equals("MRI"));
                })
                .collect(Collectors.toList());
        assertSymbolListEquals(expected, actual);
    }

    @Test
    public void test5() {
        SymbolGQl.ExprPredicate predicate = gql.getExprObj("mkt=SH,SZ with type=MRI");
        List<SymbolDTO> actual = symbols.stream().filter(predicate).collect(Collectors.toList());
        List<SymbolDTO> expected = symbols.stream().filter(s -> {
                    return (s.getMarket().equals("SH") || s.getMarket().equals("SZ"))
                            && (s.getType().equals("MRI"));
                })
                .collect(Collectors.toList());
        assertSymbolListEquals(expected, actual);
    }

    @Test
    public void test6() {
        SymbolGQl.ExprPredicate predicate = gql.getExprObj("mkt=SH with type=ES with subType=ASH,KSH");
        List<SymbolDTO> actual = symbols.stream().filter(predicate).collect(Collectors.toList());
        List<SymbolDTO> expected = symbols.stream().filter(s -> {
                    return (s.getMarket().equals("SH"))
                            && (s.getType().equals("ES"))
                            && (s.getSubType().equals("ASH") || s.getSubType().equals("KSH"));
                })
                .collect(Collectors.toList());
        assertSymbolListEquals(expected, actual);
    }

    @Test
    public void test7() {
        SymbolGQl.ExprPredicate predicate = gql.getExprObj("mkt=SH,SZ with type=ES with subType=ASH,KSH,GEM");
        List<SymbolDTO> actual = symbols.stream().filter(predicate).collect(Collectors.toList());
        List<SymbolDTO> expected = symbols.stream().filter(s -> {
                    return (s.getMarket().equals("SH") || s.getMarket().equals("SZ"))
                            && (s.getType().equals("ES"))
                            && (s.getSubType().equals("ASH") || s.getSubType().equals("KSH") || s.getSubType().equals("GEM"));
                })
                .collect(Collectors.toList());
        assertSymbolListEquals(expected, actual);
    }

    @Test
    public void test8() {
        SymbolGQl.ExprPredicate predicate = gql.getExprObj("obj=600519.SH,688981.SH,1A0001.SH");
        List<SymbolDTO> actual = symbols.stream().filter(predicate).collect(Collectors.toList());
        List<SymbolDTO> expected = symbols.stream().filter(s -> {
                    return Sets.newHashSet("1A0001.SH", "600519.SH", "688981.SH").contains(s.getSymbol());
                })
                .collect(Collectors.toList());
        assertSymbolListEquals(expected, actual);
    }

    @Test
    public void test9() {
        SymbolGQl.ExprPredicate predicate = gql.getExprObj("mkt=SH with type=MRI with subType=ASH");
        List<SymbolDTO> actual = symbols.stream().filter(predicate).collect(Collectors.toList());
        assertTrue(actual.isEmpty());
    }


    /**
     * assert
     */
    public void assertSymbolListEquals(List<SymbolDTO> expected, List<SymbolDTO> actual) {
        assertEquals(expected.size(), actual.size());
        Set<String> s1 = expected.stream().map(SymbolDTO::getSymbol).collect(Collectors.toSet());
        Set<String> s2 = actual.stream().map(SymbolDTO::getSymbol).collect(Collectors.toSet());
        Set<String> diff = Sets.difference(s1, s2);
        assertTrue(diff.isEmpty());
    }
}
