<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>screenerquote-scanner-prod</artifactId>
        <groupId>com.alipay.scannerprod</groupId>
        <version>1.0.0</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>screenerquote-scanner-prod-core-model</artifactId>
    <packaging>jar</packaging>

    <dependencies>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>commons-model</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>commons-push-model</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>quote-log</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.common</groupId>
            <artifactId>alipay-common-error</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.alipay.tradequote</groupId>
            <artifactId>tradequote-common-util</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>