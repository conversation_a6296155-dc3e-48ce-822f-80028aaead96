package com.alipay.tradequote.core.falcon.service.quotationevent;

import com.alibaba.common.lang.StringUtil;
import com.alipay.quot.commons.facade.result.Result;
import com.alipay.quot.commons.facade.result.Status;
import com.alipay.quot.commons.models.QuotationEvent;
import com.alipay.quot.commons.push.models.QuotationEventPb;
import com.alipay.quote.search.QuoteSearch;
import com.alipay.quotstore.busv2.client.vbus.impl.sofamq.SofamqPubProvider;
import com.alipay.quotstore.busv2.client.vbus.model.SendResult;
import com.alipay.tradequote.common.dal.mapper.quotscene.QuotationEventMapper;
import com.alipay.tradequote.common.dal.model.quotstore.meta.QuotationEventPO;
import com.alipay.tradequote.core.falcon.drm.QuotationEventCacheConfig;
import com.alipay.tradequote.core.falcon.drm.QuotationEventPushDrm;
import com.alipay.tradequote.core.falcon.drm.QuotationEventQueryDrm;
import com.alipay.tradequote.core.falcon.drm.QuotationEventWriteDrm;
import com.alipay.tradequote.core.falcon.model.SearchSource;
import com.alipay.tradequote.core.falcon.util.QuotationEventConvertor;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;
import org.springframework.dao.DuplicateKeyException;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
public class QuotationEventServiceImplTest {
    @InjectMocks
    QuotationEventServiceImpl service = new QuotationEventServiceImpl();

    @Mock
    QuoteSearch quoteSearch;

    @Mock
    QuotationEventMapper quotationEventMapper;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 走旧链路
     */
    @Test
    public void test1() {
        QuotationEventWriteDrm writeDrm = Mockito.mock(QuotationEventWriteDrm.class);
        service.setQuotationEventWriteDrm(writeDrm);
        when(writeDrm.getIsNewWrite()).thenReturn("false");
        service.addEvents(new ArrayList<>());
        Mockito.verify(writeDrm, Mockito.times(0)).getSourceBlist();
    }

    @Test
    public void test2() {
        QuotationEventWriteDrm writeDrm = Mockito.mock(QuotationEventWriteDrm.class);
        service.setQuotationEventWriteDrm(writeDrm);
        when(writeDrm.getIsNewWrite()).thenReturn("true");

        try {
            service.addEventsFixed(null);
        } catch (Exception e) {
            assertTrue(e instanceof IllegalArgumentException);
        }

        QuotationEventPb.QuotationEvent e1 = QuotationEventPb.QuotationEvent.newBuilder()
                .setSource("blocked_source")
                .setType("type")
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("000001.SH")
                        .build())
                .build();
        QuotationEventPb.QuotationEvent e2 = QuotationEventPb.QuotationEvent.newBuilder()
                .setSource("source")
                .setType("blocked_type")
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("000002.SH")
                        .build())
                .build();
        QuotationEventPb.QuotationEvent e3 = QuotationEventPb.QuotationEvent.newBuilder()
                .setSource("source")
                .setType("type")
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("600519.SH")
                        .build())
                .build();

        when(writeDrm.getSourceBlist()).thenReturn(Sets.newHashSet("blocked_source"));
        when(writeDrm.getTypeBlist()).thenReturn(Sets.newHashSet("blocked_type"));
        when(writeDrm.getRelationIndexBlist()).thenReturn(Sets.newHashSet("600519.SH"));
        service.addEvents(Lists.newArrayList(e1, e2, e3));
        Mockito.verify(writeDrm, Mockito.times(3)).getSourceBlist();
        Mockito.verify(writeDrm, Mockito.times(2)).getTypeBlist();
        Mockito.verify(writeDrm, Mockito.times(1)).getRelationIndexBlist();
    }

    @Test
    public void test3() {
        QuotationEventWriteDrm writeDrm = Mockito.mock(QuotationEventWriteDrm.class);
        service.setQuotationEventWriteDrm(writeDrm);
        when(writeDrm.getIsNewWrite()).thenReturn("true");

        QuotationEventPb.QuotationEvent e1 = QuotationEventPb.QuotationEvent.newBuilder()
                .setSource("blocked_source")
                .setType("type")
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("000001.SH")
                        .build())
                .build();
        QuotationEventPb.QuotationEvent e2 = QuotationEventPb.QuotationEvent.newBuilder()
                .setSource("source")
                .setType("blocked_type")
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("000002.SH")
                        .build())
                .build();
        QuotationEventPb.QuotationEvent e3 = QuotationEventPb.QuotationEvent.newBuilder()
                .setSource("source")
                .setType("type")
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("600519.SH")
                        .build())
                .build();
        QuotationEventPb.QuotationEvent e4 = QuotationEventPb.QuotationEvent.newBuilder()
                .setId("up_down@1234@1")
                .setSource("hornet")
                .setType("up_down")
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("1234")
                        .build())
                .putExtensions("duplicateType", "ignore")
                .build();
        QuotationEventPb.QuotationEvent e5 = QuotationEventPb.QuotationEvent.newBuilder()
                .setId("high_low@5678@1")
                .setSource("hornet")
                .setType("high_low")
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("5678")
                        .build())
                .build();

        when(writeDrm.getSourceBlist()).thenReturn(Sets.newHashSet("blocked_source"));
        when(writeDrm.getTypeBlist()).thenReturn(Sets.newHashSet("blocked_type"));
        when(writeDrm.getRelationIndexBlist()).thenReturn(Sets.newHashSet("600519.SH"));
        doThrow(new IllegalStateException()).when(quotationEventMapper).insert(anyList());
        doThrow(new IllegalStateException()).when(quotationEventMapper).insertOrUpdate(anyList());
        service.addEvents(Lists.newArrayList(e1, e2, e3, e4, e5));
        Mockito.verify(quotationEventMapper, Mockito.times(1)).insertOrUpdate(anyList());
        Mockito.verify(quotationEventMapper, Mockito.times(1)).insert(anyList());
    }

    @Test
    @SuppressWarnings("unchecked")
    public void test4() {
        QuotationEventWriteDrm writeDrm = Mockito.mock(QuotationEventWriteDrm.class);
        service.setQuotationEventWriteDrm(writeDrm);
        when(writeDrm.getIsNewWrite()).thenReturn("true");

        QuotationEventPb.QuotationEvent e1 = QuotationEventPb.QuotationEvent.newBuilder()
                .setSource("blocked_source")
                .setType("type")
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("000001.SH")
                        .build())
                .build();
        QuotationEventPb.QuotationEvent e2 = QuotationEventPb.QuotationEvent.newBuilder()
                .setSource("source")
                .setType("blocked_type")
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("000002.SH")
                        .build())
                .build();
        QuotationEventPb.QuotationEvent e3 = QuotationEventPb.QuotationEvent.newBuilder()
                .setSource("source")
                .setType("type")
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("600519.SH")
                        .build())
                .build();
        QuotationEventPb.QuotationEvent e4 = QuotationEventPb.QuotationEvent.newBuilder()
                .setId("up_down@1234@1")
                .setSource("hornet")
                .setType("up_down")
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("1234")
                        .build())
                .putExtensions("duplicateType", "ignore")
                .build();
        QuotationEventPb.QuotationEvent e5 = QuotationEventPb.QuotationEvent.newBuilder()
                .setId("high_low@5678@1")
                .setSource("hornet")
                .setType("high_low")
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("5678")
                        .build())
                .build();

        when(writeDrm.getSourceBlist()).thenReturn(Sets.newHashSet("blocked_source"));
        when(writeDrm.getTypeBlist()).thenReturn(Sets.newHashSet("blocked_type"));
        when(writeDrm.getRelationIndexBlist()).thenReturn(Sets.newHashSet("600519.SH"));
        doThrow(new DuplicateKeyException("duplicate")).when(quotationEventMapper).insert(anyList());
        doThrow(new IllegalStateException()).when(quotationEventMapper).insertOrUpdate(anyList());
        Result<String> zsFailed = new Result<>();
        zsFailed.setStatus(Status.ERROR);
        zsFailed.setData("1023");
        doReturn(zsFailed).when(quoteSearch).index(anyList());
        try {
            service.addEvents(Lists.newArrayList(e1, e2, e3, e4, e5));
        } catch (Exception e) {
            assertEquals(zsFailed.toString(), e.getMessage());
        }
    }

    @Test
    @SuppressWarnings("unchecked")
    public void test5() {
        QuotationEventWriteDrm writeDrm = Mockito.mock(QuotationEventWriteDrm.class);
        service.setQuotationEventWriteDrm(writeDrm);
        when(writeDrm.getIsNewWrite()).thenReturn("true");

        // duplicate事件，这一条会发送
        QuotationEventPb.QuotationEvent d1 = QuotationEventPb.QuotationEvent.newBuilder()
                .setId("up_down@1234@1")
                .setSource("hornet")
                .setType("up_down")
                .setTime(1L)
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("1234")
                        .setStartTime(1L)
                        .build())
                .putExtensions("duplicateType", "ignore")
                .build();
        // duplicate事件，这一条不会发送
        QuotationEventPb.QuotationEvent d2 = QuotationEventPb.QuotationEvent.newBuilder()
                .setId("up_down@1234@1")
                .setSource("hornet")
                .setType("up_down")
                .setTime(2L)
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("1234")
                        .setStartTime(2L)
                        .build())
                .putExtensions("duplicateType", "ignore")
                .build();
        // duplicate事件，写db失败
        QuotationEventPb.QuotationEvent d3 = QuotationEventPb.QuotationEvent.newBuilder()
                .setId("up_down@1234@1")
                .setSource("hornet")
                .setType("up_down")
                .setTime(3L)
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("1234")
                        .setStartTime(3L)
                        .build())
                .putExtensions("duplicateType", "ignore")
                .build();
        // 其他事件发送
        QuotationEventPb.QuotationEvent e1 = QuotationEventPb.QuotationEvent.newBuilder()
                .setId("high_low@5678@1")
                .setSource("hornet")
                .setType("high_low")
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("5678")
                        .setStartTime(1L)
                        .build())
                .setTime(1L)
                .build();
        // 其他事件发送
        QuotationEventPb.QuotationEvent e2 = QuotationEventPb.QuotationEvent.newBuilder()
                .setId("high_low@5678@1")
                .setSource("hornet")
                .setType("high_low")
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("5678")
                        .setStartTime(2L)
                        .build())
                .setTime(2L)
                .build();
        // 其他事件写db异常
        QuotationEventPb.QuotationEvent e3 = QuotationEventPb.QuotationEvent.newBuilder()
                .setId("high_low@5678@1")
                .setSource("hornet")
                .setType("high_low")
                .setData(QuotationEventPb.EventData.newBuilder()
                        .addRelationIndex("5678")
                        .setStartTime(3L)
                        .build())
                .setTime(3L)
                .build();

        // duplicate第一条写入
        doNothing().when(quotationEventMapper).insert(argThat(new EventMatcher(d1)));
        // duplicate第二条不写入
        doThrow(new DuplicateKeyException("duplicate")).when(quotationEventMapper).insert(argThat(new EventMatcher(d2)));
        // duplicate第三条异常
        doThrow(new RuntimeException()).when(quotationEventMapper).insert(argThat(new EventMatcher(d3)));
        // 其他事件写入
        doNothing().when(quotationEventMapper).insertOrUpdate(argThat(new EventMatcher(e1)));
        doNothing().when(quotationEventMapper).insertOrUpdate(argThat(new EventMatcher(e2)));
        // 其他事件写db异常
        doThrow(new RuntimeException()).when(quotationEventMapper).insertOrUpdate(argThat(new EventMatcher(e3)));

        // zs全部写入
        Result<String> zsOk = new Result<>();
        zsOk.setStatus(Status.SUCCESS);
        // 会有4条记录写入zsearch
        // 不符合的情况会报错
        doReturn(zsOk).when(quoteSearch).index(argThat(new IndexSourceMatcher(4)));

        // 设置发送的drm和pubProvider
        SofamqPubProvider pubProvider = Mockito.mock(SofamqPubProvider.class);
        QuotationEventPushDrm pushDrm = new QuotationEventPushDrm();
        service.setQuotationEventPushDrm(pushDrm);
        service.setQuotationEventPubProvider(pubProvider);

        doReturn(SendResult.SUCCESS).when(pubProvider).send(any(), any(), any(), any());
        service.addEvents(Lists.newArrayList(e1, e2, e3, d1, d2, d3));
        // 接受到6条事件，zsearch写入的事件
        List<SearchSource> ss = Lists.newArrayList(e1, e2, d1, d2)
                .stream()
                .map(QuotationEventConvertor::DO2JsonSearchSource)
                .collect(Collectors.toList());
        Mockito.verify(quoteSearch).index(ss);
        Mockito.verify(quoteSearch, Mockito.times(1)).index(ss);
        // 接受到6条事件，最后发送3条事件
        Mockito.verify(pubProvider, Mockito.times(3)).send(any(), any(), any(), any());
    }

    @Test
    public void getByEventIds() {
        QuotationEventQueryDrm queryDrm = Mockito.mock(QuotationEventQueryDrm.class);
        service.setQuotationEventQueryDrm(queryDrm);
        QuotationEventCacheConfig config = new QuotationEventCacheConfig();
        config.setEnable(true);
        config.setRefreshAfterWrite(3);
        config.setExpireAfterWrite(15);
        config.setMaximumSize(100000);
        when(queryDrm.getEventCacheConfig()).thenReturn(config);
        when(queryDrm.isOpenKeySizeLog()).thenReturn(true);
        service.init();
        List<String> ridList = Lists.newArrayList(
                "hornet@<EMAIL>@1690254000000@1",
                "hornet@<EMAIL>@1690254000000@1",
                "hornet@<EMAIL>@1635904500000@1",
                "hornet@<EMAIL>@1635905100000@1");

        QuotationEvent.QuotationEventRequest request = QuotationEvent.QuotationEventRequest.newBuilder()
                .addAllEventIds(ridList)
                .build();

        service.queryEvents(request);
    }

    @Test
    public void init() {
        QuotationEventQueryDrm queryDrm = Mockito.mock(QuotationEventQueryDrm.class);
        service.setQuotationEventQueryDrm(queryDrm);
        QuotationEventCacheConfig config = new QuotationEventCacheConfig();
        config.setEnable(true);
        config.setRefreshAfterWrite(3);
        config.setExpireAfterWrite(15);
        config.setMaximumSize(100000);
        when(queryDrm.getEventCacheConfig()).thenReturn(config);
        service.init();
    }


    @Test
    public void initWithNewCache() {
        QuotationEventQueryDrm queryDrm = Mockito.mock(QuotationEventQueryDrm.class);
        service.setQuotationEventQueryDrm(queryDrm);
        QuotationEventCacheConfig config = new QuotationEventCacheConfig();
        config.setEnable(true);
        config.setRefreshAfterWrite(3);
        config.setExpireAfterWrite(15);
        config.setMaximumSize(100000);
        config.setNotify(true);
        when(queryDrm.getEventCacheConfig()).thenReturn(config);
        service.init();
    }

    @AllArgsConstructor
    static class EventMatcher implements ArgumentMatcher<List<QuotationEventPO>> {
        private final QuotationEventPb.QuotationEvent left;

        @Override
        public boolean matches(List<QuotationEventPO> right) {
            Preconditions.checkArgument(right != null && right.size() == 1);
            return left.getTime() == right.get(0).getTime().getTime()
                    && StringUtil.equals(left.getId(), right.get(0).getRid());
        }
    }

    @AllArgsConstructor
    static class IndexSourceMatcher implements ArgumentMatcher<List<SearchSource>> {
        private final int count;

        @Override
        public boolean matches(List<SearchSource> searchSources) {
            Preconditions.checkArgument(searchSources != null);
            Preconditions.checkArgument(count == searchSources.size());
            return true;
        }
    }
}