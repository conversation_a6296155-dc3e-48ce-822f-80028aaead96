<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:sofa="http://img.alipay.net/dtd/schema/service"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://img.alipay.net/dtd/schema/service http://img.alipay.net/dtd/schema/service/sofa-service.xsd
        http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd"
       default-autowire="byName">

    <bean id="scannerJobManager"
          class="com.alipay.screenerquote.scannerprod.core.scanner.service.impl.ScannerJobManagerImpl"
          init-method="init">
        <property name="jobs">
            <util:list>
                <ref bean="scannerStrategyJob"/>
            </util:list>
        </property>
    </bean>

    <bean id="scannerJobTrigger"
          class="com.alipay.screenerquote.scannerprod.core.scanner.service.impl.ScannerJobTrigger"/>
    <bean id="schedulerScannerTask"
          class="com.alipay.screenerquote.scannerprod.core.scanner.scheduler.SchedulerScannerTask"/>

    <bean id="scannerDrm"
          class="com.alipay.screenerquote.scannerprod.core.scanner.service.impl.ScannerDrm"
          init-method="init"/>
    <sofa:service ref="scannerDrm"
                  interface="com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerConfigService"/>


    <bean id="jobCoordinator" class="com.alipay.screenerquote.scannerprod.core.scanner.util.DefaultSceneCoordinator">
        <!--线程数量-->
        <constructor-arg value="2"/>
        <!--队列数量-->
        <constructor-arg value="1000"/>
        <!-- 名称 -->
        <constructor-arg value="_JobCoordinator"/>
    </bean>

    <bean id="scannerStrategyJob"
          class="com.alipay.screenerquote.scannerprod.core.scanner.service.job.ScannerStrategyJob"/>

    <bean id="scannerWriteService"
          class="com.alipay.screenerquote.scannerprod.core.scanner.service.impl.ScannerWriteServiceImpl"/>
    <sofa:service ref="scannerWriteService"
                  interface="com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerWriteService"/>

    <bean id="scannerMngService"
          class="com.alipay.screenerquote.scannerprod.core.scanner.service.impl.ScannerMngServiceImpl"/>
    <sofa:service ref="scannerMngService"
                  interface="com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerMngService"/>

    <bean id="scannerQueryService"
          class="com.alipay.screenerquote.scannerprod.core.scanner.service.impl.ScannerQueryServiceImpl"/>
    <sofa:service ref="scannerQueryService"
                  interface="com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerQueryService"/>


</beans>