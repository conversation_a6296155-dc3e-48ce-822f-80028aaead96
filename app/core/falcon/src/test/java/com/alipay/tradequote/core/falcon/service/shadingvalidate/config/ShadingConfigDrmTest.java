/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.service.shadingvalidate.config;

import com.google.common.collect.Sets;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.util.Set;

import static org.junit.Assert.assertEquals;


/**
 * <AUTHOR>
 * @version ShadingConfigDrmTest.java, v 0.1 2023年06月12日 11:56 lzt
 */
public class ShadingConfigDrmTest {

    @InjectMocks
    ShadingConfigDrm shadingConfigDrm;

    @Before
    public void init(){
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void toSet(){
        String input = "A,B,C";
        Set<String> expectedOutput = Sets.newHashSet("A","B","C");
        shadingConfigDrm.setShadingRankList(input);
        shadingConfigDrm.update("shadingRankList" , new Object());
        assertEquals(expectedOutput, shadingConfigDrm.getShadingRankSet());

        String input1 = "1,2,3";
        Set<String> expectedOutput1 = Sets.newHashSet("1","2","3");
        shadingConfigDrm.setTradingTimeIdList(input1);
        shadingConfigDrm.update("tradingTimeIdList" , new Object());
        assertEquals(expectedOutput1, shadingConfigDrm.getTradingTimeIdSet());

        String input2 = "1";
        Set<String> expectedOutput2 = Sets.newHashSet("1");
        shadingConfigDrm.setTradingTimeIdList(input2);
        shadingConfigDrm.update("tradingTimeIdList" , new Object());
        assertEquals(expectedOutput2, shadingConfigDrm.getTradingTimeIdSet());

        String input3 = "stock_shading_3day_main_capital_rising_rank,stock_shading_change_percent_over3_rank," +
                "plate_shading_change_percent_over3_rank,stock_shading_3day_rising_rank";
        Set<String> expectedOutput3 = Sets.newHashSet("stock_shading_3day_main_capital_rising_rank","stock_shading_change_percent_over3_rank",
                "plate_shading_change_percent_over3_rank","stock_shading_3day_rising_rank");
        shadingConfigDrm.setShadingRankList(input3);
        shadingConfigDrm.update("shadingRankList" , new Object());
        assertEquals(expectedOutput3, shadingConfigDrm.getShadingRankSet());


        String input4 = "shading_continuous_rise,shading_change_percent_over3";
        Set<String> expectedOutput4 = Sets.newHashSet("shading_continuous_rise","shading_change_percent_over3");
        shadingConfigDrm.setShadingTagList(input4);
        shadingConfigDrm.update("shadingTagList", new Object());
        assertEquals(expectedOutput4, shadingConfigDrm.getShadingTagSet());

        String input5 = "600519.SH,000001.SZ,300750.SZ";
        Set<String> expectedOutput5 = Sets.newHashSet("600519.SH","000001.SZ","300750.SZ");
        shadingConfigDrm.setTagObjIdBlackList(input5);
        shadingConfigDrm.update("tagObjIdBlackList", new Object());
        assertEquals(expectedOutput5, shadingConfigDrm.getTagObjIdBlackSet());
    }


    @Test
    public void initTest(){
        shadingConfigDrm.setAppName("quotewrite");
        shadingConfigDrm.init();
    }
}