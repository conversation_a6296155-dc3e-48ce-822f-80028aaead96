/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.service.shadingvalidate.scheduler;

import com.alipay.antschedulerclient.serverless.model.ServerlessSimpleJobExecuteContext;
import com.alipay.antschedulerclient.serverless.result.ServerlessCommonResult;
import com.alipay.tradequote.core.falcon.service.shadingvalidate.ShadingValidatorService;
import com.alipay.tradequote.core.falcon.service.shadingvalidate.ShadingWriteService;
import com.alipay.tradequote.core.falcon.service.shadingvalidate.config.ShadingConfigService;
import com.alipay.tradequote.core.falcon.service.shadingvalidate.model.DailyTimeValidator;
import com.alipay.tradequote.core.falcon.service.shadingvalidate.model.ValidateDescriptor;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @version ShadingTimeValidateTaskTest.java, v 0.1 2023年06月12日 11:55 lzt
 */
public class ShadingTimeValidateTaskTest {

    @InjectMocks
    private ShadingTimeValidateTask shadingTimeValidateTask;

    @Mock
    ShadingConfigService shadingConfigService;

    @Mock
    ShadingWriteService shadingWriteService;

    @Mock
    ShadingValidatorService shadingValidatorService;

    @Before
    public void init(){
        MockitoAnnotations.initMocks(this);
        shadingTimeValidateTask.setShadingValidatorService(shadingValidatorService);
        shadingTimeValidateTask.setShadingWriteService(shadingWriteService);
        shadingTimeValidateTask.setShadingConfigService(shadingConfigService);
    }

    @Test
    public void execute(){
        Set<String> expectedOutput = Sets.newHashSet("A", "B", "C");
        ServerlessSimpleJobExecuteContext context = new ServerlessSimpleJobExecuteContext();
        doReturn(expectedOutput).when(shadingConfigService).getRankIdSet();
        doReturn(null).when(shadingConfigService).getDescriptorByRankId(anyString());
        ServerlessCommonResult result = shadingTimeValidateTask.execute(context);
        Assert.assertTrue(result.isSuccess());
        verify(shadingValidatorService, times(0)).preCheck(any());
        verify(shadingValidatorService, times(0)).validate(any(),anyLong());
        verify(shadingWriteService, times(0)).updateStatus(anyString(),anyLong(),anyString());

        List<DailyTimeValidator> validators = Lists.newArrayList(getDailyTimeValidator("00:00:00.000000","03:00:00.999999"),
                getDailyTimeValidator("16:00:00.000000","23:59:59.999999"));
        ValidateDescriptor descriptor = getValidateDescriptor("rankId", "Asia/Shanghai", true, false, "1L", validators);

        doReturn(descriptor).when(shadingConfigService).getDescriptorByRankId(anyString());
        doReturn(false).when(shadingValidatorService).preCheck(any());
        ServerlessCommonResult result1 = shadingTimeValidateTask.execute(context);
        Assert.assertTrue(result1.isSuccess());
        verify(shadingValidatorService, times(3)).preCheck(any());
        verify(shadingValidatorService, times(0)).validate(any(),anyLong());
        verify(shadingWriteService, times(0)).updateStatus(anyString(),anyLong(),anyString());

        doReturn(true).when(shadingValidatorService).preCheck(any());
        doReturn(false).when(shadingValidatorService).validate(any(),anyLong());
        ServerlessCommonResult result2 = shadingTimeValidateTask.execute(context);
        Assert.assertTrue(result2.isSuccess());
        verify(shadingValidatorService, times(6)).preCheck(any());
        verify(shadingValidatorService, times(3)).validate(any(),anyLong());
        verify(shadingWriteService, times(0)).updateStatus(anyString(),anyLong(),anyString());

        doReturn(true).when(shadingValidatorService).validate(any(),anyLong());
        ServerlessCommonResult result3 = shadingTimeValidateTask.execute(context);
        Assert.assertTrue(result3.isSuccess());
        verify(shadingValidatorService, times(9)).preCheck(any());
        verify(shadingValidatorService, times(6)).validate(any(),anyLong());
        verify(shadingWriteService, times(3)).updateStatus(anyString(),anyLong(),anyString());

        doThrow(new RuntimeException("testThrow")).when(shadingWriteService).updateStatus(anyString(),anyLong(),anyString());
        ServerlessCommonResult result4 = shadingTimeValidateTask.execute(context);
        Assert.assertFalse(result4.isSuccess());
        verify(shadingValidatorService, times(10)).preCheck(any());
        verify(shadingValidatorService, times(7)).validate(any(),anyLong());
        verify(shadingWriteService, times(4)).updateStatus(anyString(),anyLong(),anyString());
    }

    private ValidateDescriptor getValidateDescriptor(String rankId, String zoneId, boolean isTradingDay,
                                                     boolean stop, String tradingTimeId, List<DailyTimeValidator> timeValidators) {

        return ValidateDescriptor.builder()
                .rankId(rankId)
                .isTradingDayValidate(isTradingDay)
                .stop(stop)
                .tradingTimeId(tradingTimeId)
                .dailyTimeValidators(timeValidators)
                .zoneId(zoneId)
                .build();
    }

    private DailyTimeValidator getDailyTimeValidator(String startTime,String endTime){
        return DailyTimeValidator.builder().startTime(startTime).endTime(endTime).build();
    }

}