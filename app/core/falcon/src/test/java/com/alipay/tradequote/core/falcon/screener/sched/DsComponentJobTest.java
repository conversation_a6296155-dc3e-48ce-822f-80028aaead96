/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.sched;

import com.alipay.quot.commons.falcon.api.Component;
import com.alipay.quot.commons.models.Symbol;
import com.alipay.tradequote.common.dal.model.quotresearch.IndexComponentPO;
import com.alipay.tradequote.common.dal.model.quotstore.meta.AliasPO;
import com.alipay.tradequote.core.falcon.nimitz.service.AliasService;
import com.alipay.tradequote.core.falcon.nimitz.service.IndexComponentService;
import com.alipay.tradequote.core.falcon.screener.AbstractScreenerTest;
import com.alipay.tradequote.core.falcon.screener.config.ScreenConfigService;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.IndicatorDataWrapper;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ObjIdKeyWrapper;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ScreenCacheImpl;
import com.alipay.tradequote.core.falcon.screener.model.DataType;
import com.alipay.tradequote.core.falcon.screener.model.JobDescriptor;
import com.alipay.tradequote.core.falcon.screener.sched.job.DsComponentJob;
import com.alipay.tradequote.core.falcon.service.plateindex.ComponentService;
import com.alipay.tradequote.meta.service.SymbolGqlService;
import com.google.common.collect.ImmutableSetMultimap;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.alipay.tradequote.core.falcon.screener.util.Constants.*;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
public class DsComponentJobTest extends AbstractScreenerTest {
    @InjectMocks
    DsComponentJob dsComponentJob;

    @Mock
    SymbolGqlService symbolGqlService;

    @Mock
    IndexComponentService indexComponentService;

    @Mock
    AliasService aliasService;

    ScreenCacheImpl screenCache;

    @Mock
    private ScreenConfigService screenConfigService;

    @Mock
    private ComponentService componentService;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        dsComponentJob = new DsComponentJob();
        dsComponentJob.setSymbolGqlService(symbolGqlService);
        dsComponentJob.setIndexComponentService(indexComponentService);
        dsComponentJob.setAliasService(aliasService);
        screenCache = new ScreenCacheImpl();
        screenCache.init();
        dsComponentJob.setScreenCache(screenCache);
        dsComponentJob.setScreenConfigService(screenConfigService);
        dsComponentJob.setComponentService(componentService);
    }

    @Test
    public void test1() {
        assertEquals("DS_COMPONENT_JOB", dsComponentJob.getName());
    }

    @Test
    @SuppressWarnings("unchecked")
    public void test2() {
        String cnsSym1 = "812204.CNS";
        String cnsSym2 = "813704.CNS";
        String indexSym1 = "1B0300.SH";
        String indexSym1Alias = "000300.CSI";
        String errorSym1 = "000500.SH";
        AliasPO indexAlias1 = new AliasPO();
        indexAlias1.setSymbol(indexSym1);
        indexAlias1.setRelatedSymbol(indexSym1Alias);

        // 板块
        List<String> sectorSyms = Lists.newArrayList(cnsSym1, cnsSym2);
        List<Symbol.SymbolDO> sectorObjSyms = makeObjSymbols(sectorSyms);
        String sectorGql = "gql=" + String.join(",", sectorSyms);
        // 指数
        List<String> indexSyms = Lists.newArrayList(indexSym1, errorSym1);
        List<Symbol.SymbolDO> indexObjSyms = makeObjSymbols(indexSyms);
        String indexGql = "gql=" + String.join(",", indexSyms);

        doReturn(false).when(screenConfigService).isPlateGraySymbol(anyString());
        doReturn(sectorObjSyms).when(symbolGqlService).querySymbol(sectorGql);
        doReturn(indexObjSyms).when(symbolGqlService).querySymbol(indexGql);
        doReturn(indexAlias1).when(aliasService).queryRelatedSymbol(indexSym1, null);
        doReturn(null).when(aliasService).queryRelatedSymbol(errorSym1, null);
        doReturn(makeCompos(cnsSym1, Lists.newArrayList("300384.SZ", "600346.SH")))
                .when(indexComponentService)
                .queryIndexComponent(cnsSym1, true, null);
        doReturn(makeCompos(cnsSym2, Lists.newArrayList("300937.SZ", "605266.SH")))
                .when(indexComponentService)
                .queryIndexComponent(cnsSym2, true, null);
        doReturn(makeCompos(indexSym1, Lists.newArrayList("600519.SH", "601318.SH")))
                .when(indexComponentService)
                .queryIndexComponent(indexSym1Alias, true, null);

        JobDescriptor sectorSched = new JobDescriptor();
        sectorSched.setJobName(dsComponentJob.getName());
        sectorSched.setGql(sectorGql);
        Map<String, Object> props = new HashMap<>();
        props.put("relType", SECTOR_COMP_LIST);
        sectorSched.setExtProps(props);
        ScreenJobContext context = ScreenJobContext.builder()
                .jobDescriptor(sectorSched)
                .listTypes(listTypes())
                .build();
        // 执行板块任务
        dsComponentJob.execute(context);

        JobDescriptor indexSched = new JobDescriptor();
        indexSched.setJobName(dsComponentJob.getName());
        indexSched.setGql(indexGql);
        props = new HashMap<>();
        props.put("relType", INDEX_COMP_LIST);
        indexSched.setExtProps(props);
        context = ScreenJobContext.builder()
                .jobDescriptor(indexSched)
                .listTypes(listTypes())
                .build();
        // 执行指数任务
        dsComponentJob.execute(context);

        // 验证依赖服务的调用情况
        verify(symbolGqlService, Mockito.times(1)).querySymbol(sectorGql);
        verify(symbolGqlService, Mockito.times(1)).querySymbol(indexGql);
        verify(aliasService, Mockito.times(1)).queryRelatedSymbol(indexSym1, null);
        verify(aliasService, Mockito.times(1)).queryRelatedSymbol(errorSym1, null);
        verify(indexComponentService, Mockito.times(1)).queryIndexComponent(cnsSym1, true, null);
        verify(indexComponentService, Mockito.times(1)).queryIndexComponent(cnsSym2, true, null);
        verify(indexComponentService, Mockito.times(0)).queryIndexComponent(indexSym1Alias, true, null);

        // 验证数据
        IndicatorDataWrapper sectorWrapper = screenCache.get(new ObjIdKeyWrapper(DataType.DS_SECTOR_COMP_LIST, DataType.DS_SECTOR_COMP_LIST));
        IndicatorDataWrapper indexWrapper = screenCache.get(new ObjIdKeyWrapper(DataType.DS_INDEX_COMP_LIST, DataType.DS_INDEX_COMP_LIST));
        assertNotNull(sectorWrapper);
        assertNotNull(sectorWrapper.getValue());
        assertNotNull(indexWrapper);
        assertNotNull(indexWrapper.getValue());
        ImmutableSetMultimap<String, String> sectorCompList = (ImmutableSetMultimap<String, String>) sectorWrapper.getValue();
        ImmutableSetMultimap<String, String> indexCompList = (ImmutableSetMultimap<String, String>) indexWrapper.getValue();
        assertFalse(sectorCompList.isEmpty());
        assertTrue(sectorCompList.containsKey(cnsSym1));
        assertEquals(2, sectorCompList.get(cnsSym1).size());
        assertTrue(sectorCompList.get(cnsSym1).containsAll(Lists.newArrayList("300384.SZ", "600346.SH")));
        assertTrue(sectorCompList.containsKey(cnsSym2));
        assertEquals(2, sectorCompList.get(cnsSym2).size());
        assertTrue(sectorCompList.get(cnsSym2).containsAll(Lists.newArrayList("300937.SZ", "605266.SH")));
        assertFalse(indexCompList.containsKey(indexSym1));
        assertEquals(0, indexCompList.get(indexSym1).size());
        assertFalse(indexCompList.get(indexSym1).containsAll(Lists.newArrayList("600519.SH", "601318.SH")));
    }

    @Test
    @SuppressWarnings("unchecked")
    public void test3() {
        String sectorSym1 = "812204.CNS";
        String esSym1 = "300384.SZ";
        String esSym2 = "600346.SH";

        String sectorSym2 = "813704.CNS";
        String esSym3 = "300937.SZ";
        String esSym4 = "605266.SH";

        List<String> sectorSyms = Lists.newArrayList(sectorSym1, sectorSym2);
        List<Symbol.SymbolDO> sectorObjSyms = makeObjSymbols(sectorSyms);
        String sectorGql = "gql=" + String.join(",", sectorSyms);

        doReturn(false).when(screenConfigService).isPlateGraySymbol(anyString());
        doReturn(sectorObjSyms).when(symbolGqlService).querySymbol(sectorGql);
        doReturn(makeCompos(sectorSym1, Lists.newArrayList(esSym1, esSym2)))
                .when(indexComponentService)
                .queryIndexComponent(sectorSym1, true, null);
        doReturn(makeCompos(sectorSym2, Lists.newArrayList(esSym3, esSym4)))
                .when(indexComponentService)
                .queryIndexComponent(sectorSym2, true, null);

        JobDescriptor belongSched = new JobDescriptor();
        belongSched.setJobName(dsComponentJob.getName());
        belongSched.setGql(sectorGql);
        Map<String, Object> props = new HashMap<>();
        props.put("relType", BELONG_SECTOR_LIST);
        belongSched.setExtProps(props);
        ScreenJobContext context = ScreenJobContext.builder()
                .jobDescriptor(belongSched)
                .listTypes(listTypes())
                .build();
        // 执行任务
        dsComponentJob.execute(context);

        // 验证服务执行
        verify(symbolGqlService, Mockito.times(1)).querySymbol(sectorGql);
        verify(indexComponentService, Mockito.times(1)).queryIndexComponent(sectorSym1, true, null);
        verify(indexComponentService, Mockito.times(1)).queryIndexComponent(sectorSym2, true, null);

        // 验证数据
        IndicatorDataWrapper wrapper = screenCache.get(new ObjIdKeyWrapper(DataType.DS_OBJ_BELONG_LIST, DataType.DS_OBJ_BELONG_LIST));
        assertNotNull(wrapper);
        assertNotNull(wrapper.getValue());
        ImmutableSetMultimap<String, String> compList = (ImmutableSetMultimap<String, String>) wrapper.getValue();
        assertFalse(compList.isEmpty());
        assertTrue(compList.containsKey(esSym1));
        assertTrue(compList.containsKey(esSym2));
        assertTrue(compList.containsKey(esSym3));
        assertTrue(compList.containsKey(esSym4));
        assertTrue(compList.get(esSym1).contains(sectorSym1));
        assertTrue(compList.get(esSym2).contains(sectorSym1));
        assertTrue(compList.get(esSym3).contains(sectorSym2));
        assertTrue(compList.get(esSym4).contains(sectorSym2));
    }

    @Test
    @SuppressWarnings("unchecked")
    public void test4() {
        String sectorSym1 = "812204.CNS";
        String esSym1 = "300384.SZ";
        String esSym2 = "600346.SH";

        String sectorSym2 = "813704.CNS";
        String esSym3 = "300937.SZ";
        String esSym4 = "605266.SH";

        List<String> sectorSyms = Lists.newArrayList(sectorSym1, sectorSym2);
        List<Symbol.SymbolDO> sectorObjSyms = makeObjSymbols(sectorSyms);
        String sectorGql = "gql=" + String.join(",", sectorSyms);

        doReturn(false).when(screenConfigService).isPlateGraySymbol(anyString());
        doReturn(sectorObjSyms).when(symbolGqlService).querySymbol(sectorGql);
        doReturn(makeCompos(sectorSym1, Lists.newArrayList(esSym1, esSym2)))
                .when(indexComponentService)
                .queryIndexComponent(sectorSym1, true, null);
        doThrow(new RuntimeException())
                .when(indexComponentService)
                .queryIndexComponent(sectorSym2, true, null);

        JobDescriptor belongSched = new JobDescriptor();
        belongSched.setJobName(dsComponentJob.getName());
        belongSched.setGql(sectorGql);
        Map<String, Object> props = new HashMap<>();
        props.put("relType", SECTOR_COMP_LIST);
        belongSched.setExtProps(props);
        ScreenJobContext context = ScreenJobContext.builder()
                .jobDescriptor(belongSched)
                .listTypes(listTypes())
                .build();
        // 执行任务
        dsComponentJob.execute(context);

        IndicatorDataWrapper wrapper = screenCache.get(new ObjIdKeyWrapper(DataType.DS_SECTOR_COMP_LIST, DataType.DS_SECTOR_COMP_LIST));
        assertNotNull(wrapper);
        assertNotNull(wrapper.getValue());
        ImmutableSetMultimap<String, String> compList = (ImmutableSetMultimap<String, String>) wrapper.getValue();
        assertFalse(compList.isEmpty());
        assertTrue(compList.containsKey(sectorSym1));
        assertFalse(compList.containsKey(sectorSym2));
        assertTrue(compList.get(sectorSym1).contains(esSym1));
        assertTrue(compList.get(sectorSym1).contains(esSym2));
    }

    @Test
    @SuppressWarnings("unchecked")
    public void test5() {
        String sectorSym1 = "812204.CNS";
        String esSym1 = "300384.SZ";
        String esSym2 = "600346.SH";

        String sectorSym2 = "813704.CNS";
        String esSym3 = "300937.SZ";
        String esSym4 = "605266.SH";

        List<String> sectorSyms = Lists.newArrayList(sectorSym1, sectorSym2);
        List<Symbol.SymbolDO> sectorObjSyms = makeObjSymbols(sectorSyms);
        String sectorGql = "gql=" + String.join(",", sectorSyms);

        doReturn(false).when(screenConfigService).isPlateGraySymbol(anyString());
        doReturn(sectorObjSyms).when(symbolGqlService).querySymbol(sectorGql);
        doReturn(makeCompos(sectorSym1, Lists.newArrayList(esSym1, esSym2)))
                .when(indexComponentService)
                .queryIndexComponent(sectorSym1, true, null);
        doThrow(new RuntimeException())
                .when(indexComponentService)
                .queryIndexComponent(sectorSym2, true, null);

        // 初始化数据
        ObjIdKeyWrapper cacheKey = new ObjIdKeyWrapper(DataType.DS_SECTOR_COMP_LIST, DataType.DS_SECTOR_COMP_LIST);
        ImmutableSetMultimap<String, String> cached = ImmutableSetMultimap.of(
                "81234.CNS", "600519.SH",
                "81234.CNS", "300021.SZ",
                "812204.CNS", "123456.SH");
        IndicatorDataWrapper cachedWrapper = new IndicatorDataWrapper(cached);
        screenCache.put(cacheKey, cachedWrapper);


        JobDescriptor belongSched = new JobDescriptor();
        belongSched.setJobName(dsComponentJob.getName());
        belongSched.setGql(sectorGql);
        Map<String, Object> props = new HashMap<>();
        props.put("relType", SECTOR_COMP_LIST);
        belongSched.setExtProps(props);
        ScreenJobContext context = ScreenJobContext.builder()
                .jobDescriptor(belongSched)
                .listTypes(listTypes())
                .build();

        // 执行任务
        dsComponentJob.execute(context);

        IndicatorDataWrapper wrapper = screenCache.get(cacheKey);
        assertNotNull(wrapper);
        assertNotNull(wrapper.getValue());
        ImmutableSetMultimap<String, String> compList = (ImmutableSetMultimap<String, String>) wrapper.getValue();
        assertFalse(compList.isEmpty());
        assertTrue(compList.containsKey(sectorSym1));
        assertFalse(compList.containsKey(sectorSym2));
        assertTrue(compList.get(sectorSym1).contains(esSym1));
        assertTrue(compList.get(sectorSym1).contains(esSym2));
        assertFalse(compList.get(sectorSym1).contains("123456.SH"));
        assertTrue(compList.containsKey("81234.CNS"));
        assertTrue(compList.get("81234.CNS").contains("600519.SH"));
        assertTrue(compList.get("81234.CNS").contains("300021.SZ"));
    }

    @Test
    @SuppressWarnings("unchecked")
    public void test6() throws Exception {
        String cnsSym1 = "812204.CNS";
        String cnsSym2 = "813704.CNS";

        // 板块
        List<String> sectorSyms = Lists.newArrayList(cnsSym1, cnsSym2);
        List<Symbol.SymbolDO> sectorObjSyms = makeObjSymbols(sectorSyms);
        String sectorGql = "gql=" + String.join(",", sectorSyms);


        doReturn(true).when(screenConfigService).isPlateGraySymbol(anyString());
        doReturn(sectorObjSyms).when(symbolGqlService).querySymbol(sectorGql);
        doReturn(makeObjComponent(cnsSym1, Lists.newArrayList("300384.SZ", "600346.SH")))
                .when(componentService)
                .queryComponentBySymbol(cnsSym1, null);
        doReturn(makeObjComponent(cnsSym2, Lists.newArrayList("300937.SZ", "605266.SH")))
                .when(componentService)
                .queryComponentBySymbol(cnsSym2, null);

        JobDescriptor sectorSched = new JobDescriptor();
        sectorSched.setJobName(dsComponentJob.getName());
        sectorSched.setGql(sectorGql);
        Map<String, Object> props = new HashMap<>();
        props.put("relType", SECTOR_COMP_LIST);
        sectorSched.setExtProps(props);
        ScreenJobContext context = ScreenJobContext.builder()
                .jobDescriptor(sectorSched)
                .listTypes(listTypes())
                .build();
        // 执行板块任务
        dsComponentJob.execute(context);


        // 验证依赖服务的调用情况
        verify(symbolGqlService, Mockito.times(1)).querySymbol(sectorGql);
        verify(componentService, Mockito.times(1)).queryComponentBySymbol(cnsSym1, null);
        verify(componentService, Mockito.times(1)).queryComponentBySymbol(cnsSym2, null);

        // 验证数据
        IndicatorDataWrapper sectorWrapper = screenCache.get(new ObjIdKeyWrapper(DataType.DS_SECTOR_COMP_LIST, DataType.DS_SECTOR_COMP_LIST));

        assertNotNull(sectorWrapper);
        assertNotNull(sectorWrapper.getValue());
        ImmutableSetMultimap<String, String> sectorCompList = (ImmutableSetMultimap<String, String>) sectorWrapper.getValue();
        assertFalse(sectorCompList.isEmpty());
        assertTrue(sectorCompList.containsKey(cnsSym1));
        assertEquals(2, sectorCompList.get(cnsSym1).size());
        assertTrue(sectorCompList.get(cnsSym1).containsAll(Lists.newArrayList("300384.SZ", "600346.SH")));
        assertTrue(sectorCompList.containsKey(cnsSym2));
        assertEquals(2, sectorCompList.get(cnsSym2).size());
        assertTrue(sectorCompList.get(cnsSym2).containsAll(Lists.newArrayList("300937.SZ", "605266.SH")));
    }

    @Test
    @SuppressWarnings("unchecked")
    public void test7() throws Exception {
        String cnsSym1 = "812204.CNS";
        String cnsSym2 = "813704.CNS";

        // 板块
        List<String> sectorSyms = Lists.newArrayList(cnsSym1, cnsSym2);
        List<Symbol.SymbolDO> sectorObjSyms = makeObjSymbols(sectorSyms);
        String sectorGql = "gql=" + String.join(",", sectorSyms);


        doReturn(true).when(screenConfigService).isPlateGraySymbol(anyString());
        doReturn(sectorObjSyms).when(symbolGqlService).querySymbol(sectorGql);
        doReturn(makeObjComponent(cnsSym1, Lists.newArrayList("300384.SZ", "600346.SH")))
                .when(componentService)
                .queryComponentBySymbol(cnsSym1, null);
        doThrow(new RuntimeException())
                .when(componentService)
                .queryComponentBySymbol(cnsSym2, null);

        JobDescriptor sectorSched = new JobDescriptor();
        sectorSched.setJobName(dsComponentJob.getName());
        sectorSched.setGql(sectorGql);
        Map<String, Object> props = new HashMap<>();
        props.put("relType", SECTOR_COMP_LIST);
        sectorSched.setExtProps(props);
        ScreenJobContext context = ScreenJobContext.builder()
                .jobDescriptor(sectorSched)
                .listTypes(listTypes())
                .build();
        // 执行板块任务
        dsComponentJob.execute(context);


        // 验证依赖服务的调用情况
        verify(symbolGqlService, Mockito.times(1)).querySymbol(sectorGql);
        verify(componentService, Mockito.times(1)).queryComponentBySymbol(cnsSym1, null);
        verify(componentService, Mockito.times(1)).queryComponentBySymbol(cnsSym2, null);

        // 验证数据
        IndicatorDataWrapper sectorWrapper = screenCache.get(new ObjIdKeyWrapper(DataType.DS_SECTOR_COMP_LIST, DataType.DS_SECTOR_COMP_LIST));

        assertNotNull(sectorWrapper);
        assertNotNull(sectorWrapper.getValue());
        ImmutableSetMultimap<String, String> sectorCompList = (ImmutableSetMultimap<String, String>) sectorWrapper.getValue();
        assertFalse(sectorCompList.isEmpty());
        assertTrue(sectorCompList.containsKey(cnsSym1));
        assertEquals(2, sectorCompList.get(cnsSym1).size());
        assertTrue(sectorCompList.get(cnsSym1).containsAll(Lists.newArrayList("300384.SZ", "600346.SH")));
        assertFalse(sectorCompList.containsKey(cnsSym2));
        assertEquals(0, sectorCompList.get(cnsSym2).size());
        assertFalse(sectorCompList.get(cnsSym2).containsAll(Lists.newArrayList("300937.SZ", "605266.SH")));
    }

    private List<IndexComponentPO> makeCompos(String indexSym, List<String> compSyms) {
        return compSyms
                .stream()
                .map(s -> {
                    IndexComponentPO i = new IndexComponentPO();
                    i.setIndexSymbol(indexSym);
                    i.setComponentSymbol(s);
                    return i;
                })
                .collect(Collectors.toList());

    }

    private Component.ObjComponent makeObjComponent(String plateSymbol, List<String> componentSymbols) {

        List<Component.ComponentDO> collect = componentSymbols.stream()
                .map(s -> Component.ComponentDO.newBuilder().setComponentSymbol(s).setDelStatus(0).build())
                .collect(Collectors.toList());
        collect.add(Component.ComponentDO.newBuilder()
                .setComponentSymbol("600519.SH")
                .setDelStatus(1)
                .build());
        return Component.ObjComponent.newBuilder()
                .addAllComponents(collect)
                .setSymbol(plateSymbol).build();
    }
}
