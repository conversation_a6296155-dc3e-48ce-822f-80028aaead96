/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.service.impl;

import com.alipay.quot.commons.facade.model.base.VCell;
import com.alipay.quot.commons.models.DatagramProto;
import com.alipay.quot.commons.models.Event;
import com.alipay.quot.commons.models.tag.TagOperationEnum;
import com.alipay.quot.commons.nimitz.TableSerializer;
import com.alipay.quot.commons.push.models.base.RsdataPb;
import com.alipay.quot.commons.util.calc.TagTableBuilder;
import com.alipay.quote.log.logger.QuoteLog;
import com.alipay.quotstore.busv2.client.vbus.impl.sofamq.SofamqPubProvider;
import com.alipay.quotstore.busv2.client.vbus.model.SendResult;
import com.alipay.quotstore.busv2.client.vbus.model.SendStatus;
import com.alipay.screenerquote.prod.model.ScreenerObjDTO;
import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerPortfolio;
import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerWriteConfig;
import com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerConfigService;
import com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerWriteService;
import com.alipay.screenerquote.scannerprod.integration.zsearch.ZsearchClient;
import com.alipay.screenerquote.scannerprod.log.DedicateLog;
import com.alipay.sofa.runtime.api.annotation.SofaReference;
import com.alipay.tradequote.core.falcon.service.tag.TagWriteService;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import tech.tablesaw.api.Table;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL1_SCANNER;
import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL2_SERVICE;
import static com.google.common.base.Preconditions.checkArgument;

/**
 * <AUTHOR>
 * @version ScannerWriteServiceImpl.java, v 0.1 2025年02月24日 15:25 lzt
 */
@Setter
public class ScannerWriteServiceImpl implements ScannerWriteService {

    /**
     * logger
     */
    private static final Logger LOGGER = DedicateLog.PROD_BIZ_SERVICE_LOGGER;

    /**
     * zsearch client
     */
    @SofaReference
    private ZsearchClient zsearchClient;

    /**
     * scannerConfigService
     */
    @Setter
    private ScannerConfigService scannerDrm;

    /**
     * 消息生产者
     */
    @SofaReference
    private SofamqPubProvider tagSofamqPubProvider;

    /**
     * tagWriteService
     */
    @SofaReference
    private TagWriteService tagWriteService;

    /**
     * 默认标签有效期
     */
    public static final long DEFAULT_TAG_EXPIRE_TIME = 1000L * 60 * 60 * 24 * 365 * 100;


    @Override
    public void write(ScannerWriteConfig writeConfig, ScannerPortfolio portfolio) {
        switch (writeConfig.getDest()) {
            case "ZSEARCH":
                writePortfolio(portfolio);
                break;
            case "TAG":
                writeTag(writeConfig, portfolio);
                break;
            default:
                break;
        }
    }

    private void writeTag(ScannerWriteConfig writeConfig, ScannerPortfolio portfolio) {
        if (!scannerDrm.isOpenTagWrite()) {
            return;
        }
        QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SERVICE)
                .ext("write tag start,{0}", portfolio.getStrategyId())
                .build()
                .info();

        List<ScreenerObjDTO> objs = portfolio.getObjs();
        List<List<ScreenerObjDTO>> objLists = Lists.partition(objs, 50);
        int totalCount = 0;
        for (List<ScreenerObjDTO> partition : objLists) {
            TagTableBuilder builder = TagTableBuilder.newBuilder();
            for (ScreenerObjDTO obj : partition) {
                int count = addTag(builder, obj, portfolio, writeConfig);
                totalCount += count;
            }
            if (scannerDrm.isLocalWriteTag()) {
                Table table = builder.build();
                QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SERVICE)
                        .ext("local write tag,{0}", table.rowCount())
                        .build()
                        .info();
                tagWriteService.writeTags(table);
            } else {
                sendTagMessage(builder);
            }
        }
        QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SERVICE)
                .ext("write tag end,{0},{1}", portfolio.getStrategyId(), totalCount)
                .build()
                .info();
    }

    /**
     * 发送标签消息
     *
     * @param builder
     */
    private void sendTagMessage(TagTableBuilder builder) {
        Table table = builder.build();
        RsdataPb.RsData rsData = TableSerializer.buildRsdataPb(table);
        DatagramProto.Datagram datagram = buildDatagram(rsData);
        QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SERVICE)
                .bizSuccess(false)
                .ext("tagSofamqPubProvider,{0}", tagSofamqPubProvider)
                .build()
                .info();
        SendResult sendResult = tagSofamqPubProvider.send("TP_QUOTEWRITE_RSDATA", datagram);
        QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SERVICE)
                .bizSuccess(false)
                .ext("tag写入消息发送结果,{0}", sendResult)
                .build()
                .info();
        if (sendResult.getStatus() != SendStatus.SEND_OK) {
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SERVICE)
                    .bizSuccess(false)
                    .ext("tag写入消息发送失败,{0}", sendResult)
                    .build()
                    .error();
        }
    }

    /**
     * 添加标签
     *
     * @param builder
     * @param obj
     * @param portfolio
     * @param writeConfig
     */
    private int addTag(TagTableBuilder builder, ScreenerObjDTO obj, ScannerPortfolio portfolio, ScannerWriteConfig writeConfig) {
        int count = 0;
        try {
            Map<String, VCell> indicators = obj.getIndicators();
            String extProps = convertToJson(indicators);
            long bizDate = portfolio.getBizDate();
            TagOperationEnum tagEnum;
            long expireTime;
            if (writeConfig.getTagExpireTime() != null) {
                tagEnum = TagOperationEnum.TAGGING;
                count++;
                expireTime = writeConfig.getTagExpireTime();
            } else if (indicators.containsKey("tag_operator")) {
                VCell tagOperator = indicators.get("tag_operator");
                Boolean tagging = tagOperator.getBv();
                tagEnum = Boolean.TRUE.equals(tagging) ? TagOperationEnum.TAGGING : TagOperationEnum.UNTAGGING;
                if (Boolean.TRUE.equals(tagging)) {
                    count++;
                }
                expireTime = DEFAULT_TAG_EXPIRE_TIME;
            } else {
                tagEnum = TagOperationEnum.UNTAGGING;
                expireTime = DEFAULT_TAG_EXPIRE_TIME;
            }
            builder.addTag(
                    tagEnum, portfolio.getStrategyId(), "", "STOCK", obj.getSymbol(), extProps,
                    bizDate, bizDate + expireTime, bizDate, bizDate
            );
        } catch (Exception e) {
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SERVICE)
                    .bizSuccess(false)
                    .ext("addTagBuilder失败,{0}", obj)
                    .build()
                    .error();
        }
        return count;
    }

    /**
     * 写入策略结果
     *
     * @param portfolioResult
     */
    @Override
    public void writePortfolio(ScannerPortfolio portfolioResult) {
        Preconditions.checkArgument(Objects.nonNull(portfolioResult),
                "写入的portfolioResult为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(portfolioResult.getStrategyId()),
                "写入的策略Id为空");

        try {
            long beginDate = portfolioResult.getBeginDate();
            long snapshotDate = portfolioResult.getSnapshotDate();

            portfolioResult.setEnv(scannerDrm.getWriteEnv());
            String id = String.join("@", portfolioResult.getStrategyId(), scannerDrm.getWriteEnv(),
                    String.valueOf(beginDate));
            JsonObject writeObject = makeJsonObject(portfolioResult);

            zsearchClient.index(zsearchClient.getBizType(), id, writeObject, snapshotDate);
        } catch (Exception e) {
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SERVICE)
                    .bizSuccess(false)
                    .throwable(e)
                    .ext("写入portfolioResult异常,{0}", portfolioResult)
                    .build()
                    .error();
            throw e;
        }
    }

    /**
     * 构造json对象
     *
     * @param portfolio
     * @return
     */
    private JsonObject makeJsonObject(ScannerPortfolio portfolio) {
        Gson gson = new Gson();
        JsonElement jsonTree = gson.toJsonTree(portfolio, ScannerPortfolio.class);
        return jsonTree.getAsJsonObject();
    }

    /**
     * 构造datagram
     *
     * @param rsData
     * @return
     */
    DatagramProto.Datagram buildDatagram(RsdataPb.RsData rsData) {
        checkArgument(rsData != null && rsData.hasRsDataset(), "rsData or dataset is null");

        return DatagramProto.Datagram.newBuilder()
                .setBehaveType(DatagramProto.Datagram.BehaveType.UPDATE)
                .setDs(Event.DataSource.DATA_RS_DATA)
                .setBizKey(rsData.getRsDataset())
                .setData(rsData.toByteString())
                .build();
    }

    public static String convertToJson(Map<String, VCell> vCellMap) {
        // Create an output object to store the JSON structure
        Map<String, Object> output = new HashMap<>();
        output.put("version", 1);

        // Schema map to store field name -> type
        Map<String, String> schema = new HashMap<>();
        // Fields map to store field name -> value
        Map<String, Object> fields = new HashMap<>();

        // Iterate over all entries in the map
        for (Map.Entry<String, VCell> entry : vCellMap.entrySet()) {
            String key = entry.getKey();
            VCell cell = entry.getValue();

            // Validate the VCell
            if (cell == null || !cell.isValid()) {
                QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SERVICE)
                        .ext("cell为空或cell不合法,{0}", cell)
                        .build()
                        .error();
                continue;
            }

            // Use TypeMapper to get readable type
            schema.put(key, toReadableType(cell.getType()));

            // Add to fields: key -> value
            fields.put(key, cell.get());
        }

        // Add schema and fields to the output JSON object
        output.put("schema", schema);
        output.put("fields", fields);

        // Convert the output map to JSON using Gson
        Gson gson = new GsonBuilder().setPrettyPrinting().serializeSpecialFloatingPointValues().create(); // 格式化输出
        return gson.toJson(output);
    }

    /**
     * @param type
     * @return
     */
    private static String toReadableType(VCell.Type type) {
        if (type == null) {
            return "unknown"; // 默认类型
        }
        switch (type) {
            case TBoolean:
                return "boolean";
            case TLong:
                return "long";
            case TDouble:
                return "double";
            case TString:
                return "string";
            default:
                throw new RuntimeException("unspported kind");
        }
    }
}