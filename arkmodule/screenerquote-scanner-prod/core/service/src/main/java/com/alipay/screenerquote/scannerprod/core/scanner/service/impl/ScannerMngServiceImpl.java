/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.service.impl;

import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerReplayRequest;
import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerReplayResponse;
import com.alipay.screenerquote.scannerprod.core.scanner.scheduler.model.ScannerTaskItem;
import com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerJobManager;
import com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerMngService;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @version ScannerMngServiceImpl.java, v 0.1 2025年03月12日 16:39 lzt
 */
public class ScannerMngServiceImpl implements ScannerMngService {

    @Setter
    private ScannerJobManager scannerJobManager;

    @Override
    public ScannerReplayResponse replay(ScannerReplayRequest taskId) {
        ScannerTaskItem item = new ScannerTaskItem();
        item.setTaskId(taskId.getTaskId());
        item.setExpectedTriggerTime(new Date());
        scannerJobManager.addJob(item);

        return new ScannerReplayResponse();
    }
}