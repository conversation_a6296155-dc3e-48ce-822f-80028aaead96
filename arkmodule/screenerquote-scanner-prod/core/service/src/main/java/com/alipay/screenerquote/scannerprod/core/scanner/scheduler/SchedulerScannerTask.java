/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.scheduler;

import com.alipay.antschedulerclient.serverless.model.ServerlessClusterJobExecuteContext;
import com.alipay.antschedulerclient.serverless.result.ServerlessCommonResult;
import com.alipay.quote.log.logger.QuoteLog;
import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerJobDescriptor;
import com.alipay.screenerquote.scannerprod.core.scanner.scheduler.model.ScannerTaskItem;
import com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerJobManager;
import com.alipay.screenerquote.scannerprod.log.DedicateLog;
import com.alipay.sofa.serverless.runtime.api.event.annotation.Func;
import com.alipay.sofa.serverless.runtime.api.event.annotation.Ingress;
import com.alipay.sofa.serverless.runtime.api.event.annotation.Scheduler;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL1_SCANNER;
import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL2_SCHED;

/**
 * <AUTHOR>
 * @version SchedulerScannerTask.java, v 0.1 2025年02月14日 16:14 lzt
 */
@Scheduler(name = "EC_SERVERLESS_SCANNER_TASK")
public class SchedulerScannerTask {

    private static final Logger LOGGER = DedicateLog.PROD_SCHED_LOGGER;

    /**
     * job manager
     */
    @Setter
    private ScannerJobManager scannerJobManager;

    /**
     * 任务分片
     *
     * @param context
     * @return
     */
    @Ingress
    @Func(next = "execute")
    public List<ScannerTaskItem> split(ServerlessClusterJobExecuteContext context) {

        Date triggerTime = new Date(System.currentTimeMillis());
        //基于expectedTriggerTime,过滤出所有待执行的任务
        List<ScannerJobDescriptor> jobs = scannerJobManager.getTriggeredJob(triggerTime);

        List<ScannerTaskItem> scannerTaskItems = new ArrayList<>();
        //任务分片/下发
        if (CollectionUtils.isNotEmpty(jobs)) {
            scannerTaskItems = jobs.stream()
                    .map(job -> buildItem(job.getTaskId(), triggerTime))
                    .collect(Collectors.toList());
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SCHED)
                    .ext("获取到所有待执行的任务,taskItem:{0}", scannerTaskItems)
                    .build()
                    .info();
        }
        return scannerTaskItems;
    }

    /**
     * 执行任务
     *
     * @param context
     * @return
     */
    @Func
    public ServerlessCommonResult execute(ServerlessClusterJobExecuteContext<ScannerTaskItem> context) {

        //获取分片
        ScannerTaskItem scannerTaskItem = context.getChunk();

        //基于分片信息,将任务放入线程池中
        scannerJobManager.addJob(scannerTaskItem);
        return ServerlessCommonResult.buildSuccessResult();
    }

    /**
     * 构建任务分片
     *
     * @param taskId
     * @param expectedTriggerTime
     * @return
     */
    private ScannerTaskItem buildItem(String taskId, Date expectedTriggerTime) {
        return ScannerTaskItem.builder()
                .taskId(taskId)
                .expectedTriggerTime(expectedTriggerTime)
                .build();
    }
}