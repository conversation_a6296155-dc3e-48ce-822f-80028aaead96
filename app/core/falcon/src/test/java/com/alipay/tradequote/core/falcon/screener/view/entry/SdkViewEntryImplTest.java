package com.alipay.tradequote.core.falcon.screener.view.entry;

import com.alipay.logicview.sdk.cache.ScreenCacheKey;
import com.alipay.logicview.sdk.cache.ViewCacheSdk;
import com.alipay.logicview.sdk.cache.impl.ObjIdKeyWrapper;
import com.alipay.quote.nerve.qsql.lib.value.primitive.DoubleValue;
import com.alipay.quote.nerve.qsql.lib.viewCell.ViewCell;
import com.alipay.tradequote.core.falcon.screener.view.entry.impl.SdkViewCacheEntryImpl;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static org.mockito.Mockito.when;
import static org.mockito.Mockito.*;


public class SdkViewEntryImplTest {

    @InjectMocks
    SdkViewCacheEntryImpl sdkViewCacheEntry;

    @Mock
    ViewCacheSdk viewCacheSdk;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getValueFromSdkTest(){
        when(viewCacheSdk.getAll(any())).thenReturn(null);
        sdkViewCacheEntry.getValueFromSdk(Lists.newArrayList("600519.SH"),"view_screener");
        when(viewCacheSdk.getAll(any())).thenReturn(cacheValueQuery());
        sdkViewCacheEntry.getValueFromSdk(Lists.newArrayList("600519.SH"),"view_screener");
        when(viewCacheSdk.getAll(any())).thenThrow(new RuntimeException());
        sdkViewCacheEntry.getValueFromSdk(Lists.newArrayList("600519.SH"),"view_screener");
    }

    @Test
    public void getSingleValueFromSdkTest(){
        when(viewCacheSdk.get(any())).thenReturn(null);
        sdkViewCacheEntry.getValueFromSdk("600519.SH","view_screener");
        when(viewCacheSdk.get(any())).thenReturn(getMock());
        sdkViewCacheEntry.getValueFromSdk("600519.SH","view_screener");
        when(viewCacheSdk.get(any())).thenThrow(new RuntimeException());
        sdkViewCacheEntry.getValueFromSdk("600519.SH","view_screener");
    }

    @Test
    public void getCacheInfoFromSdkTest() {
        CacheStats stats = CacheStats.of(1, 1, 1, 1, 1, 1, 1);
        when(viewCacheSdk.stats()).thenReturn(stats);
        when(viewCacheSdk.asMap()).thenReturn(new ConcurrentHashMap<>());
        sdkViewCacheEntry.getCacheInfoFromSdk();
    }

    public ConcurrentHashMap<String, ViewCell> getMock(){
        ConcurrentHashMap<String, ViewCell> viewCellMap = new ConcurrentHashMap<>();
        viewCellMap.put("price", ViewCell.builder().value((DoubleValue.of(0.99))).build());
        viewCellMap.put("amount", ViewCell.builder().value((DoubleValue.of(0.99))).build());
        return viewCellMap;
    }

    public static Map<ScreenCacheKey, ConcurrentHashMap<String, ViewCell>> cacheValueQuery() {
        ConcurrentHashMap<String, ViewCell> viewCellMap = new ConcurrentHashMap<>();
        viewCellMap.put("price", ViewCell.builder().value((DoubleValue.of(0.99))).build());
        viewCellMap.put("amount", ViewCell.builder().value((DoubleValue.of(0.99))).build());

        Map<ScreenCacheKey, ConcurrentHashMap<String, ViewCell> > cacheMap = new ConcurrentHashMap<>();
        cacheMap.put(new ObjIdKeyWrapper("view_screener","600519.SH"), viewCellMap);
        return cacheMap;
    }

}
