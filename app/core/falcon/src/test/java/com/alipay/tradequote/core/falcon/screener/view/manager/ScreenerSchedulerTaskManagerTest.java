package com.alipay.tradequote.core.falcon.screener.view.manager;

import com.alipay.logicview.sdk.common.util.exception.LogicViewSdkException;
import com.alipay.logicview.sdk.integration.drm.LogicViewDrm;
import com.alipay.logicview.sdk.model.scheduler.ScheDescriptor;
import com.alipay.logicview.sdk.service.entry.LogicViewCacheSdkService;
import com.alipay.tradequote.core.falcon.screener.model.JobDescriptor;
import com.alipay.tradequote.core.falcon.screener.sched.ScreenJobTrigger;
import com.alipay.tradequote.core.falcon.screener.sched.ScreenerSchedulerTaskManager;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.quartz.CronExpression;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

public class ScreenerSchedulerTaskManagerTest {

    @InjectMocks
    ScreenerSchedulerTaskManager screenerSchedulerTaskManager;

    @Mock
    LogicViewDrm logicViewDrm;

    @Mock
    LogicViewCacheSdkService logicViewCacheSdkService;


    @Mock
    ScreenJobTrigger screenJobTrigger;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void executeTest() throws ParseException {
        when(logicViewDrm.getViewSchedulerTypes()).thenReturn(uncorrecDescriptorList());
        screenerSchedulerTaskManager.execute();
        when(logicViewDrm.getViewSchedulerTypes()).thenReturn(descriptorList());
        screenerSchedulerTaskManager.execute();
        when(screenJobTrigger.isTriggered(any(JobDescriptor.class))).thenReturn(true);
        screenerSchedulerTaskManager.execute();

    }

    @Test
    public void executeTest2() throws ParseException {
        when(logicViewDrm.getViewSchedulerTypes()).thenReturn(descriptorList());
        screenerSchedulerTaskManager.execute();
        when(screenJobTrigger.isTriggered(any(JobDescriptor.class))).thenReturn(true);
        doThrow(new LogicViewSdkException("","")).when(logicViewCacheSdkService).seqCacheLoadExecute(anyString(), anyBoolean());
        screenerSchedulerTaskManager.execute();

    }


    public List<ScheDescriptor> descriptorList() throws ParseException {
        List<ScheDescriptor> descriptorList = new ArrayList<>();
        ScheDescriptor descriptor = new ScheDescriptor();
        descriptor.setCode("test");
        descriptor.setDesc("test");
        descriptor.setStop(false);
        CronExpression cron = new CronExpression("0/1 * * * * ?");
        descriptor.setCronExpr(cron.getCronExpression());
        descriptorList.add(descriptor);
        return descriptorList;
    }

    public List<ScheDescriptor> uncorrecDescriptorList() throws ParseException {
        List<ScheDescriptor> descriptorList = new ArrayList<>();
        ScheDescriptor descriptor = new ScheDescriptor();
        descriptor.setCode(null);
        descriptor.setDesc("test");
        descriptor.setStop(false);
        CronExpression cron = new CronExpression("0/1 * * * * ?");
        descriptor.setCronExpr(cron.getCronExpression());
        descriptorList.add(descriptor);
        return descriptorList;
    }
}
