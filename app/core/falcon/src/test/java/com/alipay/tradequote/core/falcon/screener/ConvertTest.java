/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener;

import com.alipay.quot.commons.push.models.base.RsdataPb;
import org.junit.Test;
import tech.tablesaw.api.DoubleColumn;
import tech.tablesaw.api.Table;
import tech.tablesaw.io.hornet.Transplier;
import tech.tablesaw.io.hornet.rsdatapb.RsDataPbReadOptions;

import java.util.HashMap;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 */
public class ConvertTest {
    @Test
    public void test1() {

        Table table = Table.create("TEST");
        table.addColumns(DoubleColumn.create("price", 0d));
        table.addColumns(DoubleColumn.create("highPrice").appendMissing());
        RsdataPb.RsData rsData = Transplier.toRsDataPb(table, new HashMap<>());
        assertNotNull(rsData);

        RsDataPbReadOptions options = new RsDataPbReadOptions.Builder().rsData(rsData).build();
        Table table2 = Transplier.toTable(options);
        assertNotNull(table2);
    }
}
