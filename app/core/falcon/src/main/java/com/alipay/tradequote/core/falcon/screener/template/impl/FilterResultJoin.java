/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.template.impl;

import com.alipay.logicview.sdk.model.request.IndicatorViewQueryRequest;
import com.alipay.logicview.sdk.model.response.IndicatorViewQueryResponse;
import com.alipay.logicview.sdk.service.entry.IndicatorViewZSearchQueryService;
import com.alipay.quot.commons.models.ScreenerProto;
import com.alipay.quote.log.logger.QuoteLog;
import com.alipay.quote.nerve.qsql.lib.value.Value;
import com.alipay.tradequote.core.falcon.log.DedicateLog;
import com.alipay.tradequote.core.falcon.screener.config.ScreenConfigService;
import com.alipay.tradequote.core.falcon.screener.indicator.IndicatorFrameService;
import com.alipay.tradequote.core.falcon.screener.model.DataType;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorFrame;
import com.alipay.tradequote.core.falcon.screener.model.template.*;
import com.alipay.tradequote.core.falcon.screener.template.Join;
import com.alipay.tradequote.core.falcon.screener.util.ScreenUtil;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import lombok.Setter;
import org.slf4j.Logger;

import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.alipay.tradequote.core.falcon.log.BizConstants.SCREENER;
import static com.alipay.tradequote.core.falcon.screener.util.Constants.TEMPLATE;
import static com.alipay.tradequote.util.Functors.IS_EMPTY;

/**
 * <AUTHOR>
 */
@Setter
public class FilterResultJoin implements Join {
    /**
     * logger
     */
    private static final Logger LOGGER = DedicateLog.BIZ_FALCON_SCREENER;

    /**
     * logger
     */
    private static final Logger LOGGER_DEBUG = DedicateLog.BIZ_FALCON_SCREENER_DEBUG;


    /**
     * limit
     */
    private static final int ZSEARCH_QUERY_LIMIT = 5000;

    /**
     * service
     */
    private ScreenConfigService screenConfigService;

    /**
     * service
     */
    private IndicatorFrameService indicatorFrameService;

    /**
     * service
     */
    private IndicatorViewZSearchQueryService indicatorViewZSearchQueryService;

    @Override
    public JoinResult execute(JoinRequest request) {
        CompoundFilterResult filterResult = request.getFilterResult();
        ScreenerProto.ScreenCompoundFilterRequest originalRequest = request.getRequest();
        Set<String> filterIndicators = filterResult.getFilterIndicators();
        List<ScreenObj> objs = filterResult.getObjs();

        Map<Boolean, List<ScreenObj>> partitionedObjs = objs
                .stream()
                .collect(Collectors.partitioningBy(ScreenObj::isFilterFromZSearch));

        List<ScreenObj> zsearchObjs = partitionedObjs.get(true);
        List<ScreenObj> nonZSearchObjs = partitionedObjs.get(false);
        QuoteLog.Builder.newBuilder(LOGGER_DEBUG, SCREENER, TEMPLATE)
                .bizScene("FilterResultJoin")
                .bizSuccess(true)
                .ext("zsearchObjs {0}", zsearchObjs)
                .build()
                .info();
        QuoteLog.Builder.newBuilder(LOGGER_DEBUG, SCREENER, TEMPLATE)
                .bizScene("FilterResultJoin")
                .bizSuccess(true)
                .ext("nonZSearchObjs {0}", nonZSearchObjs)
                .build()
                .info();

        // 定义通用的合并函数
        BiFunction<ScreenObj, IndicatorFrame, ScreenObj> merger =
                (baseObj, additionalFrame)
                        -> mergeFramesAndCollectIndicators(baseObj, additionalFrame, filterIndicators);

        // 1. 使用 Joiner 处理 zsearchObjs (补充 local 数据)
        List<ScreenObj> enrichedZSearchObjs = Joiner.leftJoin(
                zsearchObjs,
                ScreenObj::getObjId,
                this::queryLocalFrame,
                merger
        );
        QuoteLog.Builder.newBuilder(LOGGER_DEBUG, SCREENER, TEMPLATE)
                .bizScene("FilterResultJoin")
                .bizSuccess(true)
                .ext("enrichedZSearchObjs {0}", enrichedZSearchObjs)
                .build()
                .info();

        // 2. 使用 Joiner 处理 nonZSearchObjs (补充 ZSearch 数据)
        List<ScreenObj> enrichedNonZSearchObjs;
        boolean hasZSearchIndicator = ScreenUtil.hasZSearchIndicator(originalRequest, screenConfigService.getZSearchIndicators());

        if (hasZSearchIndicator && !nonZSearchObjs.isEmpty()) {
            // 如果筛选或者排序有zsearch的指标，那么需要查zsearch的join
            enrichedNonZSearchObjs = Joiner.leftJoin(
                    nonZSearchObjs,
                    ScreenObj::getObjId,
                    this::queryZSearchFrame,
                    merger
            );
        } else {
            // 如果筛选和排序都没有zsearch的指标，不处理
            enrichedNonZSearchObjs = new ArrayList<>(nonZSearchObjs);
        }
        QuoteLog.Builder.newBuilder(LOGGER_DEBUG, SCREENER, TEMPLATE)
                .bizScene("FilterResultJoin")
                .bizSuccess(true)
                .ext("enrichedNonZSearchObjs {0}", enrichedNonZSearchObjs)
                .build()
                .info();

        // 3. 合并结果
        List<ScreenObj> resultObjs = new ArrayList<>();
        resultObjs.addAll(enrichedZSearchObjs);
        resultObjs.addAll(enrichedNonZSearchObjs);

        return JoinResult.builder()
                .joinedResult(resultObjs)
                .filterIndicators(filterIndicators)
                .build();
    }

    /**
     * merge
     */
    private ScreenObj mergeFramesAndCollectIndicators(ScreenObj baseObj, IndicatorFrame additionalFrame, Set<String> allIndicators) {
        Map<String, Value> mergedData = new HashMap<>(baseObj.getFrame().getData());
        mergedData.putAll(additionalFrame.getData());

        IndicatorFrame mergedFrame = new IndicatorFrame(mergedData, true);
        baseObj.setFrame(mergedFrame);

        Optional.ofNullable(baseObj.getFilterIndicators()).ifPresent(allIndicators::addAll);
        return baseObj;
    }

    /**
     * query local frame
     */
    private Map<String, IndicatorFrame> queryLocalFrame(List<String> objIds) {
        Map<String, IndicatorFrame> frames = new HashMap<>();
        if (IS_EMPTY.test(objIds)) {
            return frames;
        }

        for (String objId : objIds) {
            try {
                IndicatorFrame frame = indicatorFrameService.getFrame(DataType.DS_INDICATOR_FRAME, objId);
                Preconditions.checkArgument(frame != null, "frame is null");
                frames.put(objId, frame);
            } catch (Exception e) {
                QuoteLog.Builder.newBuilder(LOGGER, SCREENER, TEMPLATE)
                        .bizScene("FilterResultJoin")
                        .bizSuccess(false)
                        .ext("query local frame exception {0}", objId)
                        .throwable(e)
                        .build()
                        .error();
            }
        }

        return frames;
    }

    /**
     * query zsearch frame
     */
    private Map<String, IndicatorFrame> queryZSearchFrame(List<String> objIds) {
        try {
            IndicatorViewQueryRequest viewQueryRequest = new IndicatorViewQueryRequest();
            viewQueryRequest.setSymbols(objIds);
            viewQueryRequest.setLimit(ZSEARCH_QUERY_LIMIT);
            IndicatorViewQueryResponse response = indicatorViewZSearchQueryService.queryBySymbols(viewQueryRequest);
            return Optional
                    .ofNullable(response)
                    .map(IndicatorViewQueryResponse::getData)
                    .orElse(Lists.newArrayList())
                    .stream()
                    .filter(Objects::nonNull)
                    .flatMap(data -> {
                        // 1. 先进行转换操作
                        IndicatorFrame frame = ScreenUtil.convertToSectionFrame(data);

                        // 2. 如果转换结果有效，则创建一个包含 Map.Entry 的单元素流
                        // 3. 如果转换结果为 null，则返回一个空流，这个元素就被自然地“过滤”掉了
                        return frame != null
                                ? Stream.of(new AbstractMap.SimpleEntry<>(data.getSymbol(), frame))
                                : Stream.empty();
                    })
                    // 4. 此时流中的元素都是有效的 Map.Entry，可以直接收集成 Map
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        } catch (Exception e) {
            QuoteLog.Builder.newBuilder(LOGGER, SCREENER, TEMPLATE)
                    .bizScene("FilterResultJoin")
                    .bizSuccess(false)
                    .ext("queryZSearchFrame exception, {0}", objIds)
                    .throwable(e)
                    .build()
                    .error();
            return new HashMap<>();
        }
    }
}
