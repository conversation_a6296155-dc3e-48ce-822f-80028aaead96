/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.model;

import lombok.*;

/**
 * <AUTHOR>
 * @version ScannerWriteConfig.java, v 0.1 2025年04月30日 15:50 lzt
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScannerWriteConfig {

    /**
     * 写入目的地,可枚举,目前支持
     * ZSEARCH:结果写入到zsearch中
     * TAG:结果写入到标签中
     */
    private String dest;

    /**
     * TAG专用,标签有效期,单位为ms
     * 可以为空，则默认使用策略中tag_operator字段进行打标撤标,如果填入该字段，则tag_operator失效
     */
    private Long tagExpireTime;
}