<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:sofa="http://img.alipay.net/dtd/schema/service"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
         http://img.alipay.net/dtd/schema/service http://img.alipay.net/dtd/schema/service/sofa-service.xsd"
       default-autowire="byName">

    <bean id="zsearchClient" class="com.alipay.screenerquote.scannerprod.integration.zsearch.impl.ZsearchClientImpl" >
        <property name="bizType" value="scanner_portfolio"/>
    </bean>
    <sofa:service ref="zsearchClient"
                   interface="com.alipay.screenerquote.scannerprod.integration.zsearch.ZsearchClient"/>

    <bean id="screenerStrategyQueryClient"
          class="com.alipay.screenerquote.scannerprod.integration.screenerprod.impl.ScreenerStrategyQueryClientImpl"/>
    <sofa:service ref="screenerStrategyQueryClient"
                  interface="com.alipay.screenerquote.scannerprod.integration.screenerprod.ScreenerStrategyQueryClient"/>

    <sofa:reference id="screenerModuleFacade" interface="com.alipay.screenerquote.prod.api.ScreenerModuleFacade" local-first="true">
        <sofa:binding.tr testURL="${screenerquote_prod_tr_service_url}">
            <compatible>
                <vip>${screenerquote_prod_tr_service_url}</vip>
            </compatible>
        </sofa:binding.tr>
    </sofa:reference>

</beans>