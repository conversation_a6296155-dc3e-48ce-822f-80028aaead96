/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.service;

import com.alipay.quot.commons.facade.result.Result;
import com.alipay.quot.commons.facade.result.Status;
import com.alipay.quote.log.logger.QuoteLog;
import com.alipay.screenerquote.scannerprod.api.ScannerMngFacade;
import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerReplayRequest;
import com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerMngService;
import com.alipay.screenerquote.scannerprod.log.DedicateLog;
import com.alipay.screenerquote.scannerprod.model.ScannerReplayRequestDTO;
import com.alipay.screenerquote.scannerprod.model.ScannerReplayResponseDTO;
import com.alipay.sofa.runtime.api.annotation.SofaReference;
import org.slf4j.Logger;

import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL1_SCANNER;
import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL2_SERVICE;

/**
 * <AUTHOR>
 * @version ScannerMngFacadeImpl.java, v 0.1 2025年03月12日 16:24 lzt
 */
public class ScannerMngFacadeImpl implements ScannerMngFacade {


    private static final Logger LOGGER = DedicateLog.PROD_BIZ_SERVICE_LOGGER;

    @SofaReference
    private ScannerMngService scannerMngService;

    @Override
    public Result<ScannerReplayResponseDTO> replay(ScannerReplayRequestDTO request) {
        long start = System.currentTimeMillis();
        ScannerReplayRequest scannerReplayRequest = new ScannerReplayRequest();
        scannerReplayRequest.setTaskId(request.getTaskId());
        try {
            scannerMngService.replay(scannerReplayRequest);
            Result<ScannerReplayResponseDTO> result = new Result<>();
            result.setStatus(Status.SUCCESS);
            return result;
        } catch (IllegalArgumentException ie) {
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SERVICE)
                    .bizSuccess(false)
                    .bizUseTime(System.currentTimeMillis() - start)
                    .ext("请求参数异常,{0}", request)
                    .throwable(ie)
                    .build()
                    .error();

            Result<ScannerReplayResponseDTO> result = new Result<>();
            result.setStatus(Status.BAD_REQUEST);
            return result;
        } catch (Exception e) {
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SERVICE)
                    .bizSuccess(false)
                    .bizUseTime(System.currentTimeMillis() - start)
                    .ext("重算触发异常,{0}", request)
                    .throwable(e)
                    .build()
                    .error();

            Result<ScannerReplayResponseDTO> result = new Result<>();
            result.setStatus(Status.ERROR);
            return result;
        }
    }
}