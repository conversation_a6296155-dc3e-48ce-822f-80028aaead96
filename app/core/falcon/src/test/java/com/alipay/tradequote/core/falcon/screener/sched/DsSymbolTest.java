/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.sched;

import com.alipay.tradequote.core.falcon.screener.ScreenSymbolService;
import com.alipay.tradequote.core.falcon.screener.model.JobDescriptor;
import com.alipay.tradequote.core.falcon.screener.sched.job.DsSymbolJob;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.HashSet;

import static org.junit.Assert.*;
import static org.mockito.Mockito.verify;

/**
 * <AUTHOR>
 */
public class DsSymbolTest {
    @InjectMocks
    DsSymbolJob dsSymbolJob;

    @Mock
    ScreenSymbolService screenSymbolService;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        dsSymbolJob = new DsSymbolJob();
        dsSymbolJob.setScreenSymbolService(screenSymbolService);
    }

    @Test
    public void test1() {
        assertEquals("DS_SYMBOL_JOB", dsSymbolJob.getName());
        JobDescriptor dsp = new JobDescriptor();
        dsp.setGql("obj=600519.SH");
        ScreenJobContext context = new ScreenJobContext(dsp, new HashSet<>());
        dsSymbolJob.execute(context);

        verify(screenSymbolService, Mockito.times(1)).load("obj=600519.SH");
    }

}
