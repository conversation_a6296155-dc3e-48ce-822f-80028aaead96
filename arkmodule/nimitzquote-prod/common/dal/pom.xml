<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.alipay.nimitzquote-prod</groupId>
        <artifactId>nimitzquote-prod</artifactId>
        <version>1.0.0</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>nimitzquote-prod-common-dal</artifactId>
    <version>1.0.0</version>

    <dependencies>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>zdal-hbase-alipay-sofa-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- ZDAL 5 -->
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>dds-alipay-sofa-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba.kondyle</groupId>
            <artifactId>kondyle-dataframe-core</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 如需序列化为byte[]，需引入kondyle-dataframe-serialize -->
        <dependency>
            <groupId>com.alibaba.kondyle</groupId>
            <artifactId>kondyle-dataframe-serialize</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 如需与Java对象互转，需引入kondyle-dataframe-mapping -->
        <dependency>
            <groupId>com.alibaba.kondyle</groupId>
            <artifactId>kondyle-dataframe-mapping</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- lombok start -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>quote-log</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- lombok end -->

        <!-- mybatis begin -->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- mybatis end -->

        <!-- aop begin -->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- aop end -->
        <dependency>
            <groupId>com.alipay.tradequote</groupId>
            <artifactId>tradequote-common-dal</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.tradequote</groupId>
            <artifactId>tradequote-common-util</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofa-serverless-trigger-starter</artifactId>
            <!--最新推荐版本见https://yuque.antfin.com/skbeoq/sofa-serverless-trigger/cozynf -->
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>
