/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.util;

import com.alipay.logicview.sdk.model.response.IndicatorViewData;
import com.alipay.logicview.sdk.model.response.IndicatorViewQueryResponse;
import com.alipay.logicview.sdk.model.response.IndicatorViewSeries;
import com.alipay.quot.commons.models.Common;
import com.alipay.quot.commons.models.ScreenerProto;
import com.alipay.quot.commons.models.querycond.SortCondOuterClass;
import com.alipay.quote.nerve.qsql.lib.value.primitive.LongValue;
import com.alipay.quote.nerve.qsql.lib.viewCell.ViewCell;
import com.alipay.quote.nerve.qsql.lib.value.primitive.StringValue;
import com.alipay.quote.nerve.qsql.lib.value.primitive.DoubleValue;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorFrame;
import com.alipay.tradequote.core.falcon.screener.model.template.ScreenObj;
import com.google.common.collect.Sets;
import org.junit.Test;

import java.util.*;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 */
public class ScreenUtilTest2 {

    @Test
    public void testHasIndicators_withNullFilter_returnsFalse() {
        Set<String> indicators = Sets.newHashSet("indicator1", "indicator2");
        assertFalse(ScreenUtil.hasIndicators(null, indicators));
    }

    @Test
    public void testHasIndicators_withNullIndicators_returnsFalse() {
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder().build();
        assertFalse(ScreenUtil.hasIndicators(filter, null));
    }

    @Test
    public void testHasIndicators_withEmptyIndicators_returnsFalse() {
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder().build();
        assertFalse(ScreenUtil.hasIndicators(filter, new HashSet<>()));
    }

    @Test
    public void testHasIndicators_withMatchingIndicator_returnsTrue() {
        Common.VCell operand = Common.VCell.newBuilder().setSv("indicator1").build();
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand)
                .build();
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .build();
        
        Set<String> indicators = Sets.newHashSet("indicator1", "indicator2");
        assertTrue(ScreenUtil.hasIndicators(filter, indicators));
    }

    @Test
    public void testHasIndicators_withNoMatchingIndicator_returnsFalse() {
        Common.VCell operand = Common.VCell.newBuilder().setSv("indicator3").build();
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand)
                .build();
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .build();
        
        Set<String> indicators = Sets.newHashSet("indicator1", "indicator2");
        assertFalse(ScreenUtil.hasIndicators(filter, indicators));
    }

    @Test
    public void testGroupConditionByIndicator_withMixedConditions() {
        Common.VCell operand1 = Common.VCell.newBuilder().setSv("indicator1").build();
        Common.VCell operand2 = Common.VCell.newBuilder().setSv("indicator3").build();
        
        ScreenerProto.ScreenCondition condition1 = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand1)
                .build();
        ScreenerProto.ScreenCondition condition2 = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand2)
                .build();
        
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition1)
                .addConditions(condition2)
                .build();
        
        Set<String> indicators = Sets.newHashSet("indicator1", "indicator2");
        Map<Boolean, List<ScreenerProto.ScreenCondition>> result = 
                ScreenUtil.groupConditionByIndicator(filter, indicators);
        
        assertEquals(1, result.get(true).size());
        assertEquals(1, result.get(false).size());
    }

    @Test
    public void testGetIndicatorsFromCompoundFilter_withConditionsAndSort() {
        Common.VCell operand1 = Common.VCell.newBuilder().setSv("indicator1").build();
        Common.VCell operand2 = Common.VCell.newBuilder().setSv("indicator2").build();
        
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand1)
                .addOperands(operand2)
                .build();
        
        SortCondOuterClass.SortCond sortCond = SortCondOuterClass.SortCond.newBuilder()
                .setField("sortIndicator")
                .build();
        
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .setSortCond(sortCond)
                .build();
        
        Set<String> indicators = ScreenUtil.getIndicatorsFromCompoundFilter(filter);
        
        assertEquals(3, indicators.size());
        assertTrue(indicators.contains("indicator1"));
        assertTrue(indicators.contains("indicator2"));
        assertTrue(indicators.contains("sortIndicator"));
    }

    @Test
    public void testGetIndicatorsFromCompoundFilter_withEmptySortField() {
        Common.VCell operand = Common.VCell.newBuilder().setSv("indicator1").build();
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand)
                .build();
        
        SortCondOuterClass.SortCond sortCond = SortCondOuterClass.SortCond.newBuilder()
                .setField("")
                .build();
        
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .setSortCond(sortCond)
                .build();
        
        Set<String> indicators = ScreenUtil.getIndicatorsFromCompoundFilter(filter);
        
        assertEquals(1, indicators.size());
        assertTrue(indicators.contains("indicator1"));
    }

    @Test
    public void testConvertToScreenObjList_withValidData() {
        ViewCell cell = ViewCell.builder().value(new StringValue("test")).build();

        IndicatorViewSeries series = new IndicatorViewSeries();
        series.setName("indicator1");
        series.setCells(Collections.singletonList(cell));
        
        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSymbol("600519.SH");
        viewData.setName("TestStock");
        viewData.setSeries(Collections.singletonList(series));
        
        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Collections.singletonList(viewData));
        
        List<ScreenObj> result = ScreenUtil.convertToScreenObjList(response, true);
        
        assertEquals(1, result.size());
        assertEquals("600519.SH", result.get(0).getObjId());
        assertTrue(result.get(0).isFilterFromZSearch());
    }

    @Test
    public void testConvertToScreenObjList_withNullData() {
        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Collections.singletonList((IndicatorViewData) null));
        
        List<ScreenObj> result = ScreenUtil.convertToScreenObjList(response, false);
        
        assertEquals(0, result.size());
    }

    @Test
    public void testConvertToScreenObj_withValidData() {
        ViewCell cell = ViewCell.builder().value(new DoubleValue(123.45)).build();

        IndicatorViewSeries series = new IndicatorViewSeries();
        series.setName("PE_TTM");
        series.setCells(Collections.singletonList(cell));
        
        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSymbol("000001.SZ");
        viewData.setName("TestStock");
        viewData.setSeries(Collections.singletonList(series));
        
        ScreenObj result = ScreenUtil.convertToScreenObj(viewData, false);
        
        assertNotNull(result);
        assertEquals("000001.SZ", result.getObjId());
        assertEquals("TestStock", result.getName());
        assertFalse(result.isFilterFromZSearch());
        assertNotNull(result.getFrame());
    }

    @Test
    public void testConvertToSectionFrame_withValidSeries() {
        ViewCell cell1 = ViewCell.builder().value(new StringValue("test1")).build();
        ViewCell cell2 = ViewCell.builder().value(new LongValue(100L)).build();

        IndicatorViewSeries series1 = new IndicatorViewSeries();
        series1.setName("indicator1");
        series1.setCells(Collections.singletonList(cell1));
        
        IndicatorViewSeries series2 = new IndicatorViewSeries();
        series2.setName("indicator2");
        series2.setCells(Collections.singletonList(cell2));
        
        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSeries(Arrays.asList(series1, series2));
        
        IndicatorFrame result = ScreenUtil.convertToSectionFrame(viewData);
        
        assertNotNull(result);
        assertEquals(2, result.getData().size());
        assertEquals("test1", result.getData().get("indicator1").getValue());
        assertEquals(100L, result.getData().get("indicator2").getValue());
    }

    @Test
    public void testConvertToSectionFrame_withEmptyCells() {
        IndicatorViewSeries series = new IndicatorViewSeries();
        series.setName("indicator1");
        series.setCells(new ArrayList<>());
        
        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSeries(Collections.singletonList(series));
        
        IndicatorFrame result = ScreenUtil.convertToSectionFrame(viewData);
        
        assertNotNull(result);
        assertEquals(0, result.getData().size());
    }

    @Test
    public void testRearrangeFilters_withNullFilters_returnsEmpty() {
        Set<String> zsearchIndicators = Sets.newHashSet("indicator1");
        List<ScreenerProto.ScreenCompoundFilter> result = 
                ScreenUtil.rearrangeFilters(null, zsearchIndicators);
        
        assertTrue(result.isEmpty());
    }

    @Test
    public void testRearrangeFilters_withEmptyZsearchIndicators_returnsOriginal() {
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder().build();
        List<ScreenerProto.ScreenCompoundFilter> filters = Collections.singletonList(filter);
        
        List<ScreenerProto.ScreenCompoundFilter> result = 
                ScreenUtil.rearrangeFilters(filters, new HashSet<>());
        
        assertEquals(filters, result);
    }

    @Test
    public void testRearrangeFilters_withZsearchIndicators() {
        Common.VCell operand1 = Common.VCell.newBuilder().setSv("zsearch_indicator").build();
        Common.VCell operand2 = Common.VCell.newBuilder().setSv("normal_indicator").build();
        
        ScreenerProto.ScreenCondition condition1 = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand1)
                .build();
        ScreenerProto.ScreenCondition condition2 = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand2)
                .build();
        
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition1)
                .addConditions(condition2)
                .build();
        
        Set<String> zsearchIndicators = Sets.newHashSet("zsearch_indicator");
        List<ScreenerProto.ScreenCompoundFilter> result = 
                ScreenUtil.rearrangeFilters(Collections.singletonList(filter), zsearchIndicators);
        
        assertEquals(2, result.size());
    }

    @Test
    public void testHasZSearchIndicator_withNullZsearchIndicators_returnsFalse() {
        ScreenerProto.ScreenCompoundFilterRequest request = 
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder().build();
        
        assertFalse(ScreenUtil.hasZSearchIndicator(request, null));
    }

    @Test
    public void testHasZSearchIndicator_withEmptyZsearchIndicators_returnsFalse() {
        ScreenerProto.ScreenCompoundFilterRequest request = 
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder().build();
        
        assertFalse(ScreenUtil.hasZSearchIndicator(request, new HashSet<>()));
    }

    @Test
    public void testHasZSearchIndicator_withMatchingFilterAndSort_returnsTrue() {
        Common.VCell operand = Common.VCell.newBuilder().setSv("zsearch_indicator").build();
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand)
                .build();
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .build();
        
        SortCondOuterClass.SortCond sortCond = SortCondOuterClass.SortCond.newBuilder()
                .setField("zsearch_sort")
                .build();
        
        ScreenerProto.ScreenCompoundFilterRequest request = 
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder()
                        .addFilters(filter)
                        .addSortConds(sortCond)
                        .build();
        
        Set<String> zsearchIndicators = Sets.newHashSet("zsearch_indicator", "zsearch_sort");
        assertTrue(ScreenUtil.hasZSearchIndicator(request, zsearchIndicators));
    }

    @Test
    public void testHasZSearchIndicator_withOnlyMatchingFilter_returnsFalse() {
        Common.VCell operand = Common.VCell.newBuilder().setSv("zsearch_indicator").build();
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand)
                .build();
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .build();
        
        ScreenerProto.ScreenCompoundFilterRequest request = 
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder()
                        .addFilters(filter)
                        .build();
        
        Set<String> zsearchIndicators = Sets.newHashSet("zsearch_indicator");
        assertFalse(ScreenUtil.hasZSearchIndicator(request, zsearchIndicators));
    }

    @Test
    public void testGetRequestIndicators_withAllTypes() {
        Common.VCell operand = Common.VCell.newBuilder().setSv("filter_indicator").build();
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand)
                .build();
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .build();
        
        SortCondOuterClass.SortCond sortCond = SortCondOuterClass.SortCond.newBuilder()
                .setField("sort_indicator")
                .build();
        
        ScreenerProto.ScreenCompoundFilterRequest request = 
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder()
                        .addFilters(filter)
                        .addSortConds(sortCond)
                        .addIndicators("request_indicator")
                        .build();
        
        Set<String> indicators = ScreenUtil.getRequestIndicators(request);
        
        assertEquals(3, indicators.size());
        assertTrue(indicators.contains("filter_indicator"));
        assertTrue(indicators.contains("sort_indicator"));
        assertTrue(indicators.contains("request_indicator"));
    }

    @Test
    public void testGetRequestIndicators_withEmptyRequest() {
        ScreenerProto.ScreenCompoundFilterRequest request = 
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder().build();
        
        Set<String> indicators = ScreenUtil.getRequestIndicators(request);
        
        assertTrue(indicators.isEmpty());
    }
}
