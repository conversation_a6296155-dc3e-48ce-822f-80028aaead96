/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.util;

import com.alipay.quote.hulk.thread.Coordinator;
import com.alipay.quote.hulk.thread.HulkThreadFactory;
import com.alipay.sofa.trigger.runtime.util.SceneThreadPoolExecutor;
import com.google.common.base.Preconditions;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version DefaultSceneCoordinator.java, v 0.1 2025年03月05日 15:19 lzt
 */
public class DefaultSceneCoordinator implements Coordinator {

    /**
     * 异步线程任务数量
     * <p>
     * 默认JVM可用线程 * 4
     */
    private static final int N_THREADS = Runtime.getRuntime().availableProcessors() * 4;

    /**
     * 队列长度
     * <p>
     * 默认 50000
     */
    private static final int DEFAULT_TASK_SIZE = 50000;

    /**
     *
     */
    private final ThreadPoolExecutor executor;

    /**
     * @param poolSize 线程池大小(此处线程数和最大线程数相等)
     * @param capacity 队列大小
     */
    public DefaultSceneCoordinator(int poolSize, int capacity) {
        executor = new SceneThreadPoolExecutor(poolSize,
                poolSize, 0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(capacity),
                new HulkThreadFactory(this.getClass().getName() + "_DefaultSceneCoordinator"),
                new ThreadPoolExecutor.AbortPolicy());
    }


    /**
     * @param poolSize 线程池大小(此处线程数和最大线程数相等)
     * @param capacity 队列大小
     * @param name     线程名称前缀
     */
    public DefaultSceneCoordinator(int poolSize, int capacity, String name) {
        executor = new SceneThreadPoolExecutor(poolSize,
                poolSize, 0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(capacity),
                new HulkThreadFactory(this.getClass().getName() + (name == null ? "_DefaultSceneCoordinator" : name)),
                new ThreadPoolExecutor.AbortPolicy());
    }

    @Override
    public ThreadPoolExecutor executor() {
        //check
        Preconditions.checkNotNull(executor);
        return executor;
    }

    /**
     * 无参默认Coordinator
     */
    public DefaultSceneCoordinator() {
        //默认设置
        this(N_THREADS, DEFAULT_TASK_SIZE);
    }
}