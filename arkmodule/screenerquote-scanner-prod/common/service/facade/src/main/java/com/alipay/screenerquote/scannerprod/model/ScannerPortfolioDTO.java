/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version ScannerPortfolioDTO.java, v 0.1 2025年02月25日 16:18 lzt
 */
@Getter
@Setter
@ToString
public class ScannerPortfolioDTO {

    /**
     * 策略id
     */
    private String strategyId;

    /**
     * 起始标的
     */
    private String srcId;

    /**
     * 归档时间
     */
    private long beginDate;

    /**
     * 业务时间
     */
    private long bizDate;

    /**
     * 版本号
     */
    private long snapshotDate;

    /**
     * 标的列表
     */
    private List<ScannerObjDTO> objs;

    /**
     * 筛选结果数量
     */
    private int count;

    /**
     * 总筛选标的数量
     */
    private int objCount;
}