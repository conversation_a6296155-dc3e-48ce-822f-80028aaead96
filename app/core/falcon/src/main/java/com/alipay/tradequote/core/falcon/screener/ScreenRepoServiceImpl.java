/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener;

import com.alipay.tradequote.core.falcon.log.DedicateLog;
import com.alipay.tradequote.core.falcon.screener.config.ScreenConfigService;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorDescriptor;
import com.alipay.tradequote.core.falcon.screener.model.JobDescriptor;
import com.alipay.tradequote.core.falcon.screener.model.template.strategy.ScreenStrategy;
import com.alipay.tradequote.core.falcon.service.screener.config.ScreenerConfigService;
import lombok.Setter;
import org.slf4j.Logger;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Setter
public class ScreenRepoServiceImpl implements ScreenRepoService {
    /**
     * logger
     */
    private static final Logger LOGGER = DedicateLog.BIZ_FALCON_SCREENER;

    /**
     * config service
     */
    private ScreenConfigService screenConfigService;

    @Setter
    private ScreenerConfigService screenerConfigService;

    @Override
    public JobDescriptor getSchedDescriptor(String jobName) {
        return screenConfigService.getSchedDescriptor(jobName);
    }

    @Override
    public List<JobDescriptor> getSchedDescriptors()  {
        return screenConfigService.getSchedDescriptors();
    }

    @Override
    public ScreenStrategy getScreenStrategy(String strategyId) {
        return screenConfigService.isLegacyStrategyId(strategyId)
                ? screenConfigService.getScreenStrategy(strategyId)
                : screenConfigService.getScreenStrategy2(strategyId);
    }

    @Override
    public Set<String> getObjSymbolListedType() {
        if (screenConfigService.isOpenNewLoadListType()) {
            return screenConfigService.getLoadSymbolListType();
        }
        return screenConfigService.getObjSymbolListedType();
    }

    @Override
    public Set<String> getAllowIndicators() {
        List<JobDescriptor> jobs = getSchedDescriptors();
        return Optional.ofNullable(jobs)
                .orElse(new ArrayList<>())
                .stream()
                .flatMap(job -> job.getIndicators().stream())
                .flatMap(indicator -> Stream.concat(
                        Stream.of(indicator.getField()),
                        Optional.ofNullable(indicator.getAlias()).orElse(Collections.emptyList()).stream()
                ))
                .collect(Collectors.toSet());
    }
}
