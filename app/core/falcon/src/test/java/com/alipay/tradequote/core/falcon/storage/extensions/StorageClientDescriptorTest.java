package com.alipay.tradequote.core.falcon.storage.extensions;

import org.junit.Before;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.alipay.quote.hulk.HulkCache;

public class StorageClientDescriptorTest {
    @Mock
    HulkCache               storageClient;
    @InjectMocks
    StorageClientDescriptor storageClientDescriptor;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme