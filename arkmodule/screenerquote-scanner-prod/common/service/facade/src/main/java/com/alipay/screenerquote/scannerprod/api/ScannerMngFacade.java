/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.api;

import com.alipay.quot.commons.facade.result.Result;
import com.alipay.screenerquote.scannerprod.model.ScannerReplayRequestDTO;
import com.alipay.screenerquote.scannerprod.model.ScannerReplayResponseDTO;

/**
 * <AUTHOR>
 * @version ScannerMngFacade.java, v 0.1 2025年03月12日 16:17 lzt
 */
public interface ScannerMngFacade {

    /**
     * replay
     * @param request
     * @return
     */
    Result<ScannerReplayResponseDTO> replay(ScannerReplayRequestDTO request);
}