
在玲珑解决方案中，模块设计成多版本安装，即一个模块版本可以被多个场景复用，所有申明服务的方式必须是官方提供方式，用传统方式(xml配的tr服务、消息、定时任务、drm等)虽然可以发布出服务，但是通常无法兼容模块多版本，存在巨大的风险隐患，强烈不建议使用，风险自担，目前没有支持的发服务能力，请联系郝密、尚之。

1. 模块中的logger必须使用 **org.slf4j.Logger**，其他logger类全部去掉。

2. 模块要构建出的jar太大会严重增加启动耗时和内存消耗，必须遵循[模块瘦身&编码规范](https://yuque.antfin.com/middleware/sofaserverless/uxanll)

3. 不建议在模块中新建太多子模块；除了模块有提供Facade接口的需求，可以在app目录下新增Facade模块

4. 模块、基座代码中切换线程必须透传场景context:[线程池配置](https://yuque.antfin.com/skbeoq/sofa-serverless-trigger/mgtvs6#nObGR)

5. 完全关闭sofa模块化:确保模块、二方包里的bean均位于同一个ApplicationContext中，按照sofaboot官方文档 [模块化](https://yuque.antfin-inc.com/middleware/sofaboot/modular#%E5%AE%8C%E5%85%A8%E5%85%B3%E9%97%AD%E6%A8%A1%E5%9D%97%E5%8C%96) 操作关闭模块的sofa模块化特性

更多编码规范以及服务发布方式参见：https://yuque.antfin.com/middleware/sofaserverless/module-code

模块编码demo参见：https://code.alipay.com/linglong_group/sofa-trigger-module-demo/tree/master-lbj


在 common/integration 下有很多中间件使用样例，当前默认已经被注释掉，不会发布相应服务。
1. 若需要生效，请先确保基座应用已引入相关依赖，否则，会导致启动失败，并解注释相关类上注解。
2. 若需要删除，请删除 common/integration 下相关文件夹，以及 src/main/resources/config/*.properties中的相关中间件配置。


1. 复用基座切面

基座中会定义很多 aspect 切面，你可能希望复用到模块中，复用方式见文档：[复用基座的切面](https://yuque.antfin.com/middleware/sofaserverless/aspect-reuse)