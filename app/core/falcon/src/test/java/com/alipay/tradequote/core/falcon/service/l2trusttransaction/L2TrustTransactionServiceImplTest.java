package com.alipay.tradequote.core.falcon.service.l2trusttransaction;

import com.alipay.quot.commons.falcon.inner.Level2Base;
import com.alipay.quot.commons.falcon.inner.Level2Base.L2ObjTrustTransaction;
import com.alipay.quote.hulk.model.HulkCacheScoredValue;
import com.alipay.quote.hulk.model.HulkCacheValue;
import com.alipay.quote.hulk.model.QueryCond;
import com.alipay.tradequote.core.falcon.model.DataWrapper;
import com.alipay.tradequote.core.falcon.storage.client.StorageClient;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class L2TrustTransactionServiceImplTest {
    L2TrustTransactionServiceImpl service;

    @Mock
    private StorageClient storageClient;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        service = new L2TrustTransactionServiceImpl();
        service.setStorageKey("store_key");
        service.setStorageClient(storageClient);
    }

    @Test
    public void query() {
        when(storageClient.zrange(any(), any(), any(QueryCond.class))).thenReturn(makeQueryRes());
        service.query("v2.600570.SH.SH.L2.20210111+0800", makeQueryCond());
    }

    @Test
    public void updateBatch() {
        service.getStorageKey();
        service.updateBatch(makeDOs());
        Mockito.doThrow(new RuntimeException()).when(storageClient).zadd(any(), any(), any());
        try {
            service.updateBatch(makeDOs());
        } catch (Exception e) {
            // nothing
        }
    }

    @Test
    public void delete() {
        L2TrustTransactionServiceImpl service = new L2TrustTransactionServiceImpl();
        service.setStorageKey("store_key");
        StorageClient storageClient = mock(StorageClient.class);
        service.setStorageClient(storageClient);
        service.delete(makeDOs());
        Mockito.doThrow(new RuntimeException()).when(storageClient).zrem(any(), any(), any());
        try {
            service.delete(makeDOs());
        } catch (Exception e) {
            // nothing
        }
    }

    @Test
    public void setStorageKey() {
        service.setStorageKey("store_key");
        Assert.assertNotNull(service.getStorageKey());
    }

    @Test
    public void setStorageClient() {
        service.setStorageClient(storageClient);
        Assert.assertNotNull(service.getStorageClient());
    }

    @Test
    public void getStorageKey() {
        Assert.assertNotNull(service.getStorageKey());
    }

    @Test
    public void getStorageClient() {
        Assert.assertNotNull(service.getStorageKey());
    }

    private List<L2ObjTrustTransaction> makeDOs() {
        return Arrays.asList(
                makeDO("600570.SH"),
                makeDO("600571.SH")
        );
    }

    private QueryCond makeQueryCond() {
        return new QueryCond();
    }

    private Level2Base.L2ObjTrustTransaction makeDO(String symbol) {
        return L2ObjTrustTransaction.newBuilder()
                .setSymbol(symbol)
                .addItems(Level2Base.L2TrustTransaction.newBuilder()
                        .setDate(new Date().getTime())
                        .build())
                .setChannelExchange("SH")
                .setChannelLevel("L2")
                .build();
    }

    private DataWrapper makeDataWraper() {
        return DataWrapper.builder()
                .data(makeDO("600570.SH.SH.L1").toByteArray())
                .build();
    }

    private HulkCacheScoredValue<HulkCacheValue> makeHulkCacheScoredValue() {
        HulkCacheScoredValue<HulkCacheValue> hulkCacheScoredValue = new HulkCacheScoredValue<>();
        hulkCacheScoredValue.setScore(123);
        hulkCacheScoredValue.setValue(makeDataWraper());
        return hulkCacheScoredValue;
    }

    private List<HulkCacheScoredValue<HulkCacheValue>> makeQueryRes() {
        return Arrays.asList(makeHulkCacheScoredValue(), makeHulkCacheScoredValue());
    }
}