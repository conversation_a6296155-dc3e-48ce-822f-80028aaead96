package com.alipay.tradequote.core.falcon.screener.view;

import com.alipay.quot.commons.facade.model.base.ObjRsData;
import com.alipay.quot.commons.facade.request.NimitzDatasetRequest;
import com.alipay.quot.commons.facade.request.NimitzQueryContext;
import com.alipay.quot.commons.falcon.api.Index;
import com.alipay.quot.commons.models.BaseIndicator;
import com.alipay.quot.commons.models.BaseSnapshot;
import com.alipay.quot.commons.models.Kline;
import com.alipay.quot.commons.models.Symbol;
import com.alipay.quot.commons.push.models.base.RsdataPb;
import com.alipay.quote.hulk.model.QueryCond;
import com.alipay.tradequote.core.falcon.nimitz.service.NimitzDataEngine;
import com.alipay.tradequote.core.falcon.service.index.IndexQueryService;
import com.alipay.tradequote.core.falcon.service.indicator.FalconIndicatorService;
import com.alipay.tradequote.core.falcon.service.kline.KlineServiceImpl;
import com.alipay.tradequote.core.falcon.service.snapshot.FalconSnapshotService;
import com.alipay.tradequote.meta.service.SymbolGqlService;
import org.javers.common.collections.Lists;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.*;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


public class LogicViewDatasourceTest {
    @InjectMocks
    private LogicViewDatasourceImpl logicViewDatasourceImpl;

    @Mock
    private FalconIndicatorService indicatorService;

    @Mock
    private FalconSnapshotService snapshotService;

    @Mock
    private KlineServiceImpl klineService;

    @Mock
    private IndexQueryService indexQueryService;

    @Mock
    private NimitzDataEngine nimitzDataEngine;

    @Mock
    private SymbolGqlService symbolGqlService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        logicViewDatasourceImpl = new LogicViewDatasourceImpl();
        logicViewDatasourceImpl.setIndicatorService(indicatorService);
        logicViewDatasourceImpl.setKlineService(klineService);
        logicViewDatasourceImpl.setSnapshotService(snapshotService);
        logicViewDatasourceImpl.setSymbolGqlService(symbolGqlService);
        logicViewDatasourceImpl.setNimitzDataEngine(nimitzDataEngine);
        logicViewDatasourceImpl.setIndexQueryService(indexQueryService);
    }


    @Test
    public void testQueryIndicator() throws Exception {
        BaseIndicator.BaseIndicatorDO indicatorDO = BaseIndicator.BaseIndicatorDO.newBuilder().build();
        Symbol.SymbolDO symbolDO = Symbol.SymbolDO.newBuilder()
                .setSymbol("600519.SH")
                .setChannelExchange("L1")
                .setChannelLevel("L1")
                .build();

        when(indicatorService.mgetLatestIndicators(anyList())).thenReturn(Arrays.asList(indicatorDO));
        when(symbolGqlService.querySymbol(anyString())).thenReturn(Lists.asList(symbolDO));

        BaseIndicator.BaseIndicatorDO result = logicViewDatasourceImpl.queryIndicator("600519.SH");

        assertNotNull(result);
        assertEquals(indicatorDO, result);

        // test empty
        when(indicatorService.mgetLatestIndicators(anyList())).thenReturn(new ArrayList<>());
        when(symbolGqlService.querySymbol(anyString())).thenReturn(Lists.asList(symbolDO));

        result = logicViewDatasourceImpl.queryIndicator("600519.SH");

        // test excpetion
        try {
            when(indicatorService.mgetLatestIndicators(anyList())).thenThrow(new RuntimeException());
            when(symbolGqlService.querySymbol(anyString())).thenReturn(new ArrayList<>());

            result = logicViewDatasourceImpl.queryIndicator("600519.SH");
        } catch (Exception e) {
        }
    }


    @Test
    public void testQuerySnapshot() throws Exception {
        BaseSnapshot.BaseSnapshotDO  snapshotDO = BaseSnapshot.BaseSnapshotDO.newBuilder().build();
        Symbol.SymbolDO symbolDO = Symbol.SymbolDO.newBuilder()
                .setSymbol("600519.SH")
                .setChannelExchange("L1")
                .setChannelLevel("L1")
                .build();

        when(snapshotService.mgetLatestQuotation(anyList())).thenReturn(Arrays.asList(snapshotDO));
        when(symbolGqlService.querySymbol(anyString())).thenReturn(Lists.asList(symbolDO));

        BaseSnapshot.BaseSnapshotDO result = logicViewDatasourceImpl.querySnapShot("600519.SH");

        assertNotNull(result);
        assertEquals(snapshotDO, result);

        // test empty
        when(snapshotService.mgetLatestQuotation(anyList())).thenReturn(new ArrayList<>());
        when(symbolGqlService.querySymbol(anyString())).thenReturn(Lists.asList(symbolDO));

        result = logicViewDatasourceImpl.querySnapShot("600519.SH");

        // test excpetion
        try {
            when(snapshotService.mgetLatestQuotation(anyList())).thenThrow(new RuntimeException());
            when(symbolGqlService.querySymbol(anyString())).thenReturn(Lists.asList(symbolDO));

            result = logicViewDatasourceImpl.querySnapShot("600519.SH");
        } catch (Exception e) {
        }
    }


    @Test
    public void testQueryKline() {
        Kline.KLineDO kLineDO = Kline.KLineDO.newBuilder().build();
        Kline.KLineList kLineList = Kline.KLineList.newBuilder().addAllItems(Lists.asList(kLineDO)).build();
        Kline.ObjKLine  objKLine = Kline.ObjKLine.newBuilder()
                .setList(kLineList)
                .build();
        Symbol.SymbolDO symbolDO = Symbol.SymbolDO.newBuilder()
                .setSymbol("600519.SH")
                .setChannelExchange("L1")
                .setChannelLevel("L1")
                .build();

        when(klineService.query(anyString(), anyInt(), anyInt(), any())).thenReturn(objKLine);
        when(symbolGqlService.querySymbol(anyString())).thenReturn(Lists.asList(symbolDO));

        Kline.ObjKLine result = logicViewDatasourceImpl.queryKLine("600519.SH", Kline.KLineSplit.SplitBefore, Kline.KLinePeriod.P_Day1, new QueryCond());

        assertNotNull(result);
        assertEquals(objKLine, result);

        // test empty
        when(klineService.query(anyString(), anyInt(), anyInt(), any())).thenReturn(null);
        when(symbolGqlService.querySymbol(anyString())).thenReturn(Lists.asList(symbolDO));

        logicViewDatasourceImpl.queryKLine("600519.SH", Kline.KLineSplit.SplitBefore, Kline.KLinePeriod.P_Day1, new QueryCond());

        // empty list
       kLineList = Kline.KLineList.newBuilder().addAllItems(new ArrayList<>()).build();
       objKLine = Kline.ObjKLine.newBuilder()
                .setList(kLineList)
                .build();
        when(klineService.query(anyString(), anyInt(), anyInt(), any())).thenReturn(objKLine);
        when(symbolGqlService.querySymbol(anyString())).thenReturn(Lists.asList(symbolDO));

        logicViewDatasourceImpl.queryKLine("600519.SH", Kline.KLineSplit.SplitBefore, Kline.KLinePeriod.P_Day1, new QueryCond());


        // test excpetion
        try {
            when(klineService.query(anyString(), anyInt(), anyInt(), any())).thenThrow(new RuntimeException());
            when(symbolGqlService.querySymbol(anyString())).thenReturn(Lists.asList(symbolDO));

            logicViewDatasourceImpl.queryKLine("600519.SH", Kline.KLineSplit.SplitBefore, Kline.KLinePeriod.P_Day1, new QueryCond());

        } catch (Exception e) {
        }
    }

    @Test
    public void testQuerypb() throws Exception {

        NimitzDatasetRequest request = NimitzDatasetRequest.builder().build();
        NimitzQueryContext context = NimitzQueryContext.builder().build();

        RsdataPb.ObjRsData objRsData = RsdataPb.ObjRsData.newBuilder().build();
        List<RsdataPb.ObjRsData> objRsDataList = Lists.asList(objRsData);
        when(nimitzDataEngine.queryDatasetPb(any(NimitzDatasetRequest.class), any(NimitzQueryContext.class))).thenReturn(objRsDataList);

        List<RsdataPb.ObjRsData> result = logicViewDatasourceImpl.queryDatasetByPb(request, context);

        assertNotNull(result);
        assertEquals(objRsDataList, result);

        // test empty
        when(nimitzDataEngine.queryDatasetPb(any(NimitzDatasetRequest.class), any(NimitzQueryContext.class))).thenReturn(new ArrayList<>());

        result = logicViewDatasourceImpl.queryDatasetByPb(request, context);

        // test excpetion
        try {
            when(nimitzDataEngine.queryDatasetPb(any(NimitzDatasetRequest.class), any(NimitzQueryContext.class)))
                    .thenThrow(new RuntimeException());
            logicViewDatasourceImpl.queryDatasetByPb(request, context);
        } catch (Exception e) {
        }
    }

    @Test
    public void testQueryDataset() throws Exception {

        NimitzDatasetRequest request = NimitzDatasetRequest.builder().build();
        NimitzQueryContext context = NimitzQueryContext.builder().build();

        ObjRsData objRsData = new ObjRsData();
        List<ObjRsData> objRsDataList = Lists.asList(objRsData);
        when(nimitzDataEngine.queryDataset(any(NimitzDatasetRequest.class), any(NimitzQueryContext.class))).thenReturn(objRsDataList);

        List<ObjRsData> result = logicViewDatasourceImpl.queryDataset(request, context);

        assertNotNull(result);
        assertEquals(objRsDataList, result);

        // test empty
        when(nimitzDataEngine.queryDataset(any(NimitzDatasetRequest.class), any(NimitzQueryContext.class))).thenReturn(new ArrayList<>());

        logicViewDatasourceImpl.queryDataset(request, context);

        // test excpetion
        try {
            when(nimitzDataEngine.queryDataset(any(NimitzDatasetRequest.class), any(NimitzQueryContext.class)))
                    .thenThrow(new RuntimeException());
            logicViewDatasourceImpl.queryDataset(request, context);
        } catch (Exception e) {
        }
    }

    @Test
    public void testQueryIndex() throws Exception {

        Index.ObjIndex objIndex= Index.ObjIndex.newBuilder().build();

        when(indexQueryService.queryIndices(anyString(), anyList(), any(), anyInt())).thenReturn(objIndex);

        Index.ObjIndex result = logicViewDatasourceImpl.queryIndex("symbol", "index", new QueryCond());

        assertNotNull(result);
        assertEquals(objIndex, result);

        // test empty
        when(indexQueryService.queryIndices(anyString(), anyList(), any(), anyInt())).thenReturn(null);

        logicViewDatasourceImpl.queryIndex("symbol", "index", new QueryCond());

        // test excpetion
        try {
            when(indexQueryService.queryIndices(anyString(), anyList(), any(), anyInt())).thenThrow(new RuntimeException());

            logicViewDatasourceImpl.queryIndex("symbol", "index", new QueryCond());
        } catch (Exception e) {
        }
    }
}
