package com.alipay.tradequote.core.falcon.screener.view.service;

import com.alipay.quot.commons.facade.request.NimitzDatasetRequest;
import com.alipay.quot.commons.indicatorview.IndicatorViewUtil;
import com.alipay.quot.commons.push.models.base.NimitzTablePb;
import com.alipay.quot.commons.push.models.base.RsdataPb;
import com.alipay.quote.nerve.qsql.lib.value.primitive.DoubleValue;
import com.alipay.quote.nerve.qsql.lib.viewCell.ViewCell;
import com.alipay.tradequote.core.falcon.nimitz.service.NimitzDataEngine;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ObjIdKeyWrapper;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ScreenCacheKey;
import com.alipay.tradequote.core.falcon.screener.view.ViewContext;
import com.alipay.tradequote.core.falcon.screener.view.cache.ViewCache;
import com.alipay.tradequote.core.falcon.screener.view.sched.IndicatorViewSchedulerImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.protobuf.InvalidProtocolBufferException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

import static java.nio.charset.StandardCharsets.ISO_8859_1;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

// 测试类
public class IndicatorViewSchedTest {

    @Mock
    ViewCache viewCache;

    @Mock
    NimitzDataEngine nimitzDataEngine;

    @InjectMocks
    IndicatorViewSchedulerImpl scheduler;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testExecute_withValidData_shouldUpdateCache() throws InvalidProtocolBufferException {
        // 设置测试数据和模拟行为
        List<String> symbols = Lists.newArrayList("600519.SH","002714.SZ");
        String viewName = "view_screener";
        ViewContext context = new ViewContext(symbols, viewName);

        List<RsdataPb.ObjRsData> rsDataList = new ArrayList<>();
        rsDataList.add(buildRsDataPb("600519.SH"));
        rsDataList.add(buildRsDataPb("002714.SZ"));

        Mockito.when(nimitzDataEngine.queryDatasetPb((NimitzDatasetRequest) any(), any())).thenReturn(new ArrayList<>());

        // 执行测试的方法
        scheduler.execute(context);
        verify(viewCache, times(0)).put(any(ScreenCacheKey.class), any(ConcurrentHashMap.class));

        Mockito.when(nimitzDataEngine.queryDatasetPb((NimitzDatasetRequest) any(), any())).thenReturn(rsDataList);
        scheduler.execute(context);

        Mockito.when(viewCache.viewKeySet()).thenReturn(Sets.newHashSet(new ObjIdKeyWrapper("view_screener","600519.SH")));
        Mockito.when(viewCache.get(any())).thenReturn(CompletableFuture.completedFuture(cacheResMock()));
        scheduler.execute(context);

        mockStatic(IndicatorViewUtil.class);
        Mockito.when(IndicatorViewUtil.rsData2Table(anyList()))
                .thenThrow(new RuntimeException("Forced Exception"));
        scheduler.execute(context);
    }

    private RsdataPb.ObjRsData buildRsDataPb(String symbol) {
        List<NimitzTablePb.NimitzColumn> columnsPbList = new ArrayList<>();
        NimitzTablePb.NimitzColumn columns_1 = NimitzTablePb.NimitzColumn.newBuilder()
                .setKind("KString")
                .setName("obj_id")
                .addCells(NimitzTablePb.NimitzCell.newBuilder().setSVal(symbol).build())
                .build();
        NimitzTablePb.NimitzColumn columns_2 = NimitzTablePb.NimitzColumn.newBuilder()
                .setKind("KString")
                .setName("name")
                .addCells(NimitzTablePb.NimitzCell.newBuilder().setSVal("view_screener").build())
                .build();
        NimitzTablePb.NimitzColumn columns_3 = NimitzTablePb.NimitzColumn.newBuilder()
                .setKind("KString")
                .setName("value")
                .addCells(NimitzTablePb.NimitzCell.newBuilder().setSVal(indicatorViewValueMaker()).build())
                .build();
        NimitzTablePb.NimitzColumn columns_4 = NimitzTablePb.NimitzColumn.newBuilder()
                .setKind("KInt64")
                .setName("biz_date")
                .addCells(NimitzTablePb.NimitzCell.newBuilder().setI64Val(System.currentTimeMillis()).build())
                .build();
        NimitzTablePb.NimitzColumn columns_5 = NimitzTablePb.NimitzColumn.newBuilder()
                .setKind("KInt64")
                .setName("snapshot_date")
                .addCells(NimitzTablePb.NimitzCell.newBuilder().setI64Val(System.currentTimeMillis()).build())
                .build();

        columnsPbList.add(columns_1);
        columnsPbList.add(columns_2);
        columnsPbList.add(columns_3);
        columnsPbList.add(columns_4);
        columnsPbList.add(columns_5);
        NimitzTablePb.NimitzTable nimitzTable = NimitzTablePb.NimitzTable.newBuilder().addAllColumns(columnsPbList).build();
        RsdataPb.RsData rsDataPb = RsdataPb.RsData.newBuilder()
                .setRsDataset("DS_FUND_YIELD_RANK")
                .setData(nimitzTable)
                .build();

        return RsdataPb.ObjRsData.newBuilder()
                .setData(rsDataPb)
                .setObjDimKey(symbol).build();
    }

    private String indicatorViewValueMaker(){
        List<NimitzTablePb.NimitzColumn> columnList = new ArrayList<NimitzTablePb.NimitzColumn>();

        columnList.add(NimitzTablePb.NimitzColumn.newBuilder()
                .setName("price")
                .setKind("KDouble")
                .addCells( NimitzTablePb.NimitzCell.newBuilder()
                        .setDVal(79.90).build()
                ).build());

        columnList.add(NimitzTablePb.NimitzColumn.newBuilder()
                .setName("plate")
                .setKind("KString")
                .addCells( NimitzTablePb.NimitzCell.newBuilder()
                        .setSVal("猪肉").build()
                ).build());

        columnList.add(NimitzTablePb.NimitzColumn.newBuilder()
                .setName("changePercent")
                .setKind("KDouble")
                .addCells(NimitzTablePb.NimitzCell.newBuilder()
                        .setDVal(8.5).build()
                ).build());

        columnList.add(NimitzTablePb.NimitzColumn.newBuilder()
                .setName("amount")
                .setKind("KDouble")
                .addCells(NimitzTablePb.NimitzCell.newBuilder()
                        .setDVal(8990898.001).build()
                ).build());

        columnList.add(NimitzTablePb.NimitzColumn.newBuilder()
                .setName("changePercent_snapshot_date")
                .setKind("KInt64")
                .addCells(NimitzTablePb.NimitzCell.newBuilder()
                        .setI64Val(System.currentTimeMillis()).build()
                ).build());


        NimitzTablePb.NimitzTable table =  NimitzTablePb.NimitzTable.newBuilder()
                .addAllColumns(columnList)
                .build();

        return table.toByteString().toString(ISO_8859_1);
    }

    private ConcurrentHashMap<String, ViewCell> cacheResMock(){
        ConcurrentHashMap<String, ViewCell> cacheRes = new ConcurrentHashMap<>();

        cacheRes.put("price", ViewCell.builder()
                .value(DoubleValue.of(20.99))
                .snapshotDate(System.currentTimeMillis())
                .build());
        return cacheRes;
    }
}

