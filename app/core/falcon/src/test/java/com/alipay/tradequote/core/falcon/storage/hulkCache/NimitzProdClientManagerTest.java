/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.storage.hulkCache;

import com.alipay.quote.hulk.cache.provider.HulkProvider;
import com.alipay.quote.hulk.cache.provider.NimitzStorageProvider;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $Id: NimitzProdClientManagerTest.java, v 0.1 2021-07-31 19:43 sushuang Exp $$
 */
public class NimitzProdClientManagerTest {
    @InjectMocks
    private NimitzProdClientManager nimitzProdClientManager;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        Map<String, HulkProvider> queryProviderMap = new HashMap<>();
        nimitzProdClientManager.setQueryProviderMap(queryProviderMap);
    }

    @Test
    public void testAddHulkProvider() {
        HulkProvider hulkProvider = new NimitzStorageProvider();
        nimitzProdClientManager.addHulkProvider("DS_F", hulkProvider);
    }

    @Test
    public void testBuildNimitzDatasetBizType() {
        assert nimitzProdClientManager.buildNimitzDatasetBizType("DS_F").equals("NIMITZ_DATASET_DS_F");
    }
}