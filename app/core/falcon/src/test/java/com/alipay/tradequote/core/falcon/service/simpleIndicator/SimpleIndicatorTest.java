package com.alipay.tradequote.core.falcon.service.simpleIndicator;

import com.alipay.quot.commons.falcon.inner.Indicator.IndicatorList;
import com.alipay.quot.commons.falcon.inner.Level2Base;
import com.alipay.tradequote.core.falcon.model.DataWrapper;
import com.alipay.tradequote.core.falcon.model.KeyWrapper;
import com.alipay.tradequote.core.falcon.service.l2tick.L2TickSerialize;
import com.alipay.tradequote.core.falcon.service.simpleindicator.SimpleIndicatorSerialize;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class SimpleIndicatorTest {
    SimpleIndicatorSerialize serialize;
    @Before
    public void setUp() throws Exception {
        serialize = new SimpleIndicatorSerialize();
    }

    @Test
    public void serializeK() {
        KeyWrapper keyWrapper = new KeyWrapper(1, "600570.SH.SH.L1");
        Assert.assertNotNull(serialize.serializeK(keyWrapper, KeyWrapper.class));

    }

    @Test
    public void deserializeK() {
        KeyWrapper keyWrapper = new KeyWrapper(1, "600570.SH.SH.L1");
        Assert.assertNotNull(serialize.deserializeK(keyWrapper.getBytes(), KeyWrapper.class));
    }

    @Test
    public void serializeV() {
        DataWrapper dataWrapper = makeDataWraper();
        Assert.assertNotNull(serialize.serializeV(dataWrapper, DataWrapper.class));
        Assert.assertNotNull(serialize.serializeV(null, DataWrapper.class));
    }

    @Test
    public void deserializeV() {
        DataWrapper dataWrapper = makeDataWraper();
        Assert.assertNull(serialize.deserializeV(dataWrapper.getData(), DataWrapper.class));
        Assert.assertNull(serialize.deserializeV(null, DataWrapper.class));
        Assert.assertNull(serialize.deserializeV(new byte[0], DataWrapper.class));
    }

    private DataWrapper makeDataWraper() {
        return DataWrapper.builder()
                .data(makeDO().toByteArray())
                .build();
    }

    private IndicatorList makeDO() {
        return IndicatorList.newBuilder().build();
    }
}