/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.util;

import com.alibaba.common.lang.StringUtil;
import com.alipay.logicview.sdk.model.response.IndicatorViewData;
import com.alipay.logicview.sdk.model.response.IndicatorViewQueryResponse;
import com.alipay.logicview.sdk.model.response.IndicatorViewSeries;
import com.alipay.quot.commons.facade.model.base.NimitzColumn;
import com.alipay.quot.commons.facade.model.base.NimitzTable;
import com.alipay.quot.commons.facade.model.base.ObjRsData;
import com.alipay.quot.commons.facade.model.base.RsData;
import com.alipay.quot.commons.models.Common;
import com.alipay.quot.commons.models.ScreenerProto;
import com.alipay.quot.commons.models.Snapshot;
import com.alipay.quot.commons.models.Symbol;
import com.alipay.quot.commons.models.querycond.SortCondOuterClass;
import com.alipay.quot.commons.push.models.base.NimitzTablePb;
import com.alipay.quote.log.logger.QuoteLog;
import com.alipay.quote.nerve.qsql.lib.value.Value;
import com.alipay.quote.nerve.qsql.lib.value.primitive.*;
import com.alipay.quote.nerve.qsql.lib.viewCell.ViewCell;
import com.alipay.quote.nerve.qsql.util.ValueUtil;
import com.alipay.tradequote.core.falcon.log.DedicateLog;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ScreenCacheKey;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorDescriptor;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorFrame;
import com.alipay.tradequote.core.falcon.screener.model.JobDescriptor;
import com.alipay.tradequote.core.falcon.screener.model.template.FilterRequest;
import com.alipay.tradequote.core.falcon.screener.model.template.ScreenObj;
import com.alipay.tradequote.core.falcon.screener.model.template.strategy.StrategyFilter;
import com.alipay.tradequote.core.falcon.screener.view.ViewContext;
import com.google.common.base.Preconditions;
import com.google.common.collect.Iterables;
import com.google.protobuf.Descriptors;
import com.google.protobuf.Descriptors.Descriptor;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import tech.tablesaw.api.Table;

import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.alipay.quot.commons.facade.model.base.NimitzColumn.Kind.KBoolean;
import static com.alipay.quot.commons.facade.model.base.NimitzColumn.Kind.KInt64;
import static com.alipay.quot.commons.models.Datatable.Column.Kind.KDouble;
import static com.alipay.quot.commons.models.Datatable.Column.Kind.KString;
import static com.alipay.tradequote.core.falcon.screener.util.Constants.SCREENER;
import static com.alipay.tradequote.core.falcon.screener.util.Constants.TEMPLATE;
import static com.alipay.tradequote.util.Functors.*;

/**
 * <AUTHOR>
 */
public final class ScreenUtil {
    /**
     * logger
     */
    private static final Logger LOGGER = DedicateLog.BIZ_FALCON_SCREENER;

    /**
     * disable
     */
    private ScreenUtil() {
    }

    /**
     * app_name是screenerquote, application_name是tradequote
     */
    private static final String SCREENERQUOTE_APP_NAME = "screenerquote";

    /**
     *
     */
    private static final String QUOTEWRITE_APP_NAME = "quotewrite";

    /**
     * @return screenerquote应用
     */
    public static boolean isScreenerquoteApp(String appName) {
        return StringUtil.equals(SCREENERQUOTE_APP_NAME, appName);
    }

    /**
     * @return quotewrite应用
     */
    public static boolean isQuotewriteApp(String appName) {
        return StringUtil.equals(QUOTEWRITE_APP_NAME, appName);
    }

    /**
     * checker
     */
    public static boolean isStrategyFilterRequest(FilterRequest request) {
        return request != null
                && isValidStrategyFilter(request.getStrategyFilter())
                && IS_EMPTY.test(request.getUserDefineFilter());
    }

    /**
     * checker
     */
    public static boolean isUserDefineFilterRequest(FilterRequest request) {
        return request != null
                && !isValidStrategyFilter(request.getStrategyFilter())
                && IS_NOT_EMPTY.test(request.getUserDefineFilter());
    }

    /**
     * checker
     */
    public static boolean isCompoundFilterRequest(FilterRequest request) {
        return request != null && request.getCompoundFilter() != null;
    }

    /**
     * chcker
     */
    public static boolean isValidStrategyFilter(StrategyFilter filter) {
        return filter != null
                && IS_NOT_EMPTY_STRING.test(filter.getFilter());
    }

    /**
     * beginInclusive
     * true 0 <= collection.size() <= limit
     * false 0 < collection.size() <= limit
     */
    public static boolean isLimitCollection(Collection<?> collection, int limit, boolean beginInclusive) {
        return beginInclusive
                ? IS_EMPTY.test(collection) || collection.size() <= limit
                : IS_NOT_EMPTY.test(collection) && collection.size() <= limit;
    }

    /**
     * convert
     */
    public static IndicatorFrame convertToSection(Table raw, JobDescriptor dsp) {
        Map<String, Value> oriIndicators = ValueUtil.tableToValue(raw, 0);
        Map<String, Value> newIndicators = new HashMap<>();
        for (IndicatorDescriptor d : dsp.getIndicators()) {
            String oriFieldName = d.getField();
            String newName = d.getName();
            List<String> alias = Optional.ofNullable(d.getAlias()).orElse(new ArrayList<>());

            if (oriIndicators.containsKey(oriFieldName)) {
                Value value = oriIndicators.get(oriFieldName);
                newIndicators.put(newName, value);
                alias.forEach(a -> newIndicators.put(a, value));
            }
        }

        return new IndicatorFrame(newIndicators);
    }

    /**
     * @param table
     * @param dsp
     * @return
     */
    public static IndicatorFrame convertToSection(NimitzTablePb.NimitzTable table, JobDescriptor dsp) {
        Map<String, Value> oriIndicators = nimitzTableToValue(table, 0);
        Map<String, Value> newIndicators = new HashMap<>();
        for (IndicatorDescriptor d : dsp.getIndicators()) {
            String oriFieldName = d.getField();
            String newName = d.getName();
            List<String> alias = Optional.ofNullable(d.getAlias()).orElse(new ArrayList<>());

            if (oriIndicators.containsKey(oriFieldName)) {
                Value value = oriIndicators.get(oriFieldName);
                newIndicators.put(newName, value);
                alias.forEach(a -> newIndicators.put(a, value));
            }
        }

        return new IndicatorFrame(newIndicators);
    }

    /**
     * 如果该标的的指标视图没有查到，生成空数据帧
     *
     * @param descriptor
     * @return
     */
    public static IndicatorFrame convertWithNullValue(JobDescriptor descriptor) {
        Map<String, Value> newIndicators = new HashMap<>();
        for (IndicatorDescriptor d : descriptor.getIndicators()) {
            String newName = d.getName();
            List<String> alias = Optional.ofNullable(d.getAlias()).orElse(new ArrayList<>());

            newIndicators.put(newName, new NullValue());
            alias.forEach(a -> newIndicators.put(a, new NullValue()));
        }

        return new IndicatorFrame(newIndicators);
    }

    /**
     * nimitzTable获取valueMap
     *
     * @param table
     * @param rowIndex
     * @return
     */
    public static Map<String, Value> nimitzTableToValue(NimitzTablePb.NimitzTable table, int rowIndex) {
        Preconditions.checkArgument(rowIndex > -1, "invalid row");
        if (CollectionUtils.isEmpty(table.getColumnsList())) {
            return new HashMap<>();
        } else {
            Map<String, Value> result = new HashMap<>();

            for (NimitzTablePb.NimitzColumn column : table.getColumnsList()) {
                Value value = validColumn(column, rowIndex) ?
                        cellToValue(column.getKind(), column.getCellsList().get(rowIndex)) : new NullValue();
                result.put(column.getName(), value);
            }

            return result;
        }
    }

    /**
     * @param column
     * @param rowIndex
     * @return
     */
    private static boolean validColumn(NimitzTablePb.NimitzColumn column, int rowIndex) {
        return CollectionUtils.isNotEmpty(column.getCellsList()) &&
                column.getCellsList().size() - 1 >= rowIndex && isValidCell(column.getCells(rowIndex));
    }


    private static boolean isValidCell(NimitzTablePb.NimitzCell cell) {
        if (cell == null) {
            return false;
        }

        return cell.hasBVal() || cell.hasDVal() || cell.hasSVal() || cell.hasI64Val();
    }

    /**
     * nimitzCell2Value
     *
     * @param kind
     * @param nimitzCell
     * @return
     */
    private static Value cellToValue(String kind, NimitzTablePb.NimitzCell nimitzCell) {
        switch (kind) {
            case "KDouble":
                return Double.isNaN(nimitzCell.getDVal()) ? new NullValue() : DoubleValue.of(nimitzCell.getDVal());
            case "KInt64":
                return LongValue.of(nimitzCell.getI64Val());
            case "KBoolean":
                return BooleanValue.of(nimitzCell.getBVal());
            case "KString":
                return new StringValue(nimitzCell.getSVal());
            default:
                return new NullValue();
        }
    }

    /**
     * convert
     */
    public static long toEpochMilli(ZonedDateTime zdt) {
        return zdt.toInstant().toEpochMilli();
    }

    /**
     * listedType filter
     */
    public static List<Symbol.SymbolDO> filter(List<Symbol.SymbolDO> symObjs, Set<String> listedTypes) {
        Preconditions.checkArgument(symObjs != null);
        Preconditions.checkArgument(IS_NOT_EMPTY.test(listedTypes));

        return symObjs
                .stream()
                .filter(symObj -> listedTypes.contains(symObj.getListType().name()))
                .collect(Collectors.toList());
    }

    /**
     * project nullable obj
     */
    public static <T, U> T proj(U obj, Function<U, T> projection) {
        Preconditions.checkArgument(projection != null);
        return Optional
                .ofNullable(obj)
                .map(projection)
                .orElse(null);
    }

    /**
     * NimitzTable= style cast as a value map
     * @param table NimitzTable style
     * @return  Map<String, Map<String, Value>> map
     */
    public static Map<String, Map<String, ViewCell>> viewNimitzTableToValueList(NimitzTablePb.NimitzTable table){
        if(CollectionUtils.isEmpty(table.getColumnsList())){
            return new HashMap<>();
        }

        Map<String,  Map<String, ViewCell>> valeListMapper =  new HashMap<>();

        int colNum  = table.getColumnsCount();
        int rowNum = table.getColumns(0).getCellsCount();

        for(int i = 0; i < rowNum; i++) {
            String key = "";
            Map<String, NimitzTablePb.NimitzCell> valueMap = new ConcurrentHashMap<>();
            Map<String, ViewCell> viewCellMapper = new ConcurrentHashMap<>();

            for (int j = 0; j < colNum; j++) {
                NimitzTablePb.NimitzColumn column = table.getColumns(j);
                valueMap.put(column.getName(), validColumn(column, i) ?
                             column.getCellsList().get(i) : NimitzTablePb.NimitzCell.newBuilder().build());

                if (("obj_id").equals(column.getName())) {
                    key = column.getCellsList().get(i).getSVal();
                }
            }

            Long bizDate = valueMap.get("biz_date").getI64Val();

            valueMap.forEach((key1, value) -> viewCellMapper.put(key1, nimitzCell2ViewCell(value, bizDate)));
            if(StringUtil.isNotEmpty(key) && CollectionUtils.isNotEmpty(valueMap.keySet())){
                valeListMapper.put(key, viewCellMapper);
            }
        }

        return valeListMapper;
    }

    /**
     * @param cacheMap viewCahe
     * @return List<ObjRsData> rsData
     */
    public static List<ObjRsData> cacheValue2RasData(Map<ScreenCacheKey, ConcurrentHashMap<String, ViewCell>> cacheMap){
        if(null == cacheMap || cacheMap.isEmpty()){
            throw new RuntimeException("map is empty");
        }

        List<ObjRsData> res  = new ArrayList<>();
        cacheMap.forEach((cacheKey, cacheValue) -> {
            RsData rsData = new RsData();
            ObjRsData objRsData = new ObjRsData();
            List<NimitzColumn> columnList =  new ArrayList<>();
            NimitzTable nimitzTable = new NimitzTable();
            cacheValue.forEach((field, fieldValue) -> {
                NimitzColumn column =  new NimitzColumn();
                column.setName(field);
                nimitzPbCell2NimitzColumn(fieldValue.getValue(), column);
                columnList.add(column);
            });
            nimitzTable.setColumns(columnList);
            rsData.setData(nimitzTable);
            objRsData.setData(rsData);
            objRsData.setObjDimKey(cacheKey.getDataKey());
            res.add(objRsData);
        });

        return res;
    }

    /**
     * nimitzCellPb to nimitzColumn
     * @param value value, nimitz column
     * @param column nimitz column set by value
     */
    public static void nimitzPbCell2NimitzColumn(Value value, NimitzColumn column){
        if(null != value){
            if(value.isBoolean()){
                column.setKind(KBoolean.name());
                column.setCells(Collections.singletonList(NimitzColumn.NimitzCell.builder()
                        .bVal(value.asBoolean())
                        .build()));
                return;
            } else if (value.isDouble()){
                column.setKind(KDouble.name());
                column.setCells(Collections.singletonList(NimitzColumn.NimitzCell.builder()
                        .dVal(value.asDouble())
                        .build()));
                return;
            } else if (value.isLong()){
                column.setKind(KInt64.name());
                column.setCells(Collections.singletonList(NimitzColumn.NimitzCell.builder()
                        .i64Val(value.asLong())
                        .build()));
                return;
            } else if (value.isString()){
                column.setKind(KString.name());
                column.setCells(Collections.singletonList(NimitzColumn.NimitzCell.builder()
                        .sVal(value.asString())
                        .build()));
                return;
            }else{
                column.setCells(Collections.singletonList(NimitzColumn.NimitzCell.builder().build()));
            }
        }
    }

    /**
     *
     * @param snapshot Snapshot.SnapshotDO
     * @return Map<String, Object> cast snapshott as value style
     */
    public static Map<String, Object> propertyMapper(Snapshot.SnapshotDO snapshot){
        Descriptor descriptor = snapshot.getDescriptorForType();
        Map<String, Object> propertyValueMapper = new HashMap<>();

        List<Descriptors.FieldDescriptor> fields = descriptor.getFields();

        for(Descriptors.FieldDescriptor field : fields){
            String propertyName = field.getName();
            Object objValue = field.getDefaultValue();
            if(null!= objValue){
                propertyValueMapper.put(propertyName, objValue);
            }
        }
        return propertyValueMapper;
    }

    /**
     *  objecg2Value
     * @param obj object
     * @param value nimitzPb value
     * @return Value data
     */
    public static Value object2Value(Object obj, Value value){
        if(null == obj){
            return new NullValue();
        }

        if(value.isBoolean()) {
            return BooleanValue.of((boolean) obj);
        }
        if(value.isDouble()) {
            return DoubleValue.of((double) obj);
        }
        if(value.isLong()) {
            return LongValue.of((long) obj);
        }
        if(value.isString()) {
            return new StringValue((String) obj);
        }

        return new NullValue();
    }

    /**
     * nimitzCell2ViewCell
     * @param cell NimitzTablePb.NimitzCell
     * @param bizDate snapshotdate version
     * @return ViewCell
     */
    public static ViewCell nimitzCell2ViewCell(NimitzTablePb.NimitzCell cell, Long bizDate){
        Preconditions.checkArgument(cell != null, "value couldn't be null");
        Preconditions.checkArgument(bizDate != null, "bizDate couldn't be null");

        return ViewCell.builder()
                .value(cellToValue(cell))
                .snapshotDate(bizDate)
                .build();
    }


    /**
     * extractAndRemoveSnapshotDates
     * @param originalMap originalMap
     * @return ConcurrentHashMap<String, ViewCell>
     */
    public static ConcurrentHashMap<String, ViewCell> extractAndRemoveSnapshotDates(ConcurrentHashMap<String, ViewCell> originalMap) {
        ConcurrentHashMap<String, ViewCell> resultMap = new ConcurrentHashMap<>();
        Iterator<Map.Entry<String, ViewCell>> iterator = originalMap.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<String, ViewCell> entry = iterator.next();
            String key = entry.getKey();
            if (key.endsWith("_snapshot_date")) {
                resultMap.put(key, entry.getValue());
                iterator.remove();
                }
            }
        return resultMap;
    }

    /**
     *
     * @param originalString originalString
     * @param suffix suffix
     * @return string without suffix
     */
    public static String removeSuffix(String originalString, String suffix) {
        if (originalString != null && suffix != null && originalString.endsWith(suffix)) {
            return originalString.substring(0, originalString.length() - suffix.length());
        }
        return originalString;
    }

    /**
     * nimitzCellPb2Long
     * @param value
     * @return version
     */
    public static Long nimitzCellPb2Long(Value value){
        if(null == value){
            throw new RuntimeException("cell can't ne bull");
        }

        if(value.isLong()){
            return value.asLong();
        }else if(value.isBoolean()){
            return Double.valueOf(value.asDouble()).longValue();
        }
        throw new RuntimeException("cell couldn't cast into long, please check its type, value: " + value);
    }

    /**
     * valueMap snapshotDateValueReplace
     * @param valueMap valueMap
     * @param versionMap versionMap
     */
    public static void snapshotDateValueReplace(ConcurrentHashMap<String, ViewCell> valueMap, ConcurrentHashMap<String, ViewCell> versionMap){
        if(CollectionUtils.isEmpty(valueMap.entrySet())){
           throw new RuntimeException("valueMap is empty");
        }else if(CollectionUtils.isEmpty(versionMap.entrySet())){
            return;
        }

        versionMap.forEach((key, value) -> {
            String valueKey = ScreenUtil.removeSuffix(key, "_snapshot_date");
            valueMap.computeIfPresent(valueKey, (k, v) -> ViewCell.builder()
                    .value(v.getValue())
                    // 改为方法判断，如果本身有long 转 long, 否则 double 转 long
                    .snapshotDate(nimitzCellPb2Long(value.getValue()))
                    .build()
            );
        });
    }

    /**
     * nimitzTableToCellMap
     * @param table nimitzTable
     * @param rowIndex rowIndex
     * @return Map<String, NimitzTablePb.NimitzCell>
     */
    public static Map<String, Value> nimitzTableToCellMap(NimitzTablePb.NimitzTable table, int rowIndex) {
        Preconditions.checkArgument(rowIndex > -1, "invalid row");
        if (CollectionUtils.isEmpty(table.getColumnsList())) {
            return new HashMap<>();
        } else {
            Map<String, Value> result = new HashMap<>();

            for (NimitzTablePb.NimitzColumn column : table.getColumnsList()) {
               Value value = validColumn(column, rowIndex) ?
                       cellToValue(column.getCellsList().get(rowIndex)) : new NullValue();
                result.put(column.getName(), value);
            }

            return result;
        }
    }

    /**
     * obj_updates
     * @param objSymbol objSymbols
     * @param viewName view_name
     * @return ViewContext context
     */
    public static ViewContext contextConverter(List<Symbol.SymbolDO> objSymbol, String viewName){
        if(StringUtil.isEmpty(viewName) || null == objSymbol){
            throw new RuntimeException("Job basic parameter error, objSymbol: " + objSymbol + " ,viewName: "+viewName);
        }

        List<String> symbols = objSymbol.stream().filter(Objects::nonNull)
                .map(Symbol.SymbolDO::getAliSymbol)
                .collect(Collectors.toList());

        return new ViewContext(symbols, viewName);
    }

    /**
     * NimitzCell to Value without kind
     * @param nimitzCell nimitzCell
     * @return Value
     */
    public static Value cellToValue(NimitzTablePb.NimitzCell nimitzCell){
        if(null == nimitzCell){
            return new NullValue();
        }

        if(nimitzCell.hasI64Val()){
            return LongValue.of(nimitzCell.getI64Val());
        }else if(nimitzCell.hasSVal()){
            return new StringValue(nimitzCell.getSVal());
        }else if(nimitzCell.hasDVal()){
            return DoubleValue.of(nimitzCell.getDVal());
        }else if(nimitzCell.hasBVal()){
            return BooleanValue.of(nimitzCell.getBVal());
        }

        return new NullValue();
    }

    /**
     * 基于指标视图名称和字段名称获取对应名称
     * @param indicatorViewName
     * @param valueName
     * @return
     */
    public static String getNameWithIndicatorViewName(String indicatorViewName, String valueName) {
        return String.join(".", indicatorViewName, valueName);
    }


    /**
     * 检查复合条件（CompoundFilter) 中如果有Condition包含指定的指标
     */
    public static boolean hasIndicators(ScreenerProto.ScreenCompoundFilter cf, Set<String> indicators) {
        if (cf == null) {
            return false;
        }

        if (indicators == null || indicators.isEmpty()) {
            return false;
        }

        if (cf.hasSortCond() && StringUtil.isNotBlank(cf.getSortCond().getField())) {
            if (indicators.contains(cf.getSortCond().getField())) {
                return true;
            }
        }

        return cf.getConditionsList()
                .stream()
                .flatMap(cond -> cond.getOperandsList().stream())
                .map(Common.VCell::getSv)
                .anyMatch(indicators::contains);
    }

    /**
     * group conditions
     */
    public static Map<Boolean, List<ScreenerProto.ScreenCondition>> groupConditionByIndicator(
            ScreenerProto.ScreenCompoundFilter cf,
            Set<String> indicators) {
        Preconditions.checkArgument(cf != null);
        Preconditions.checkArgument(indicators != null);

        return cf.getConditionsList()
                .stream()
                .collect(Collectors.partitioningBy(
                        cond -> cond.getOperandsList()
                                .stream()
                                .map(Common.VCell::getSv)
                                .anyMatch(indicators::contains)
                ));
    }

    /**
     * get indicators
     */
    public static Set<String> getIndicatorsFromCompoundFilter(ScreenerProto.ScreenCompoundFilter cf) {
        Preconditions.checkArgument(cf != null);

        Set<String> indicators = cf.getConditionsList()
                .stream()
                .flatMap(cond -> cond.getOperandsList().stream())
                .map(Common.VCell::getSv)
                .collect(Collectors.toSet());
        if (cf.hasSortCond() && StringUtil.isNotBlank(cf.getSortCond().getField())) {
            indicators.add(cf.getSortCond().getField());
        }

        return indicators;
    }

    /**
     * convert
     */
    public static List<ScreenObj> convertToScreenObjList(IndicatorViewQueryResponse view, boolean isFromZSearch) {
        Preconditions.checkArgument(view != null);

        return view
                .getData()
                .stream()
                .filter(Objects::nonNull)
                .map(data -> ScreenUtil.convertToScreenObj(data, isFromZSearch))
                .collect(Collectors.toList());
    }

    /**
     * convert
     */
    public static ScreenObj convertToScreenObj(IndicatorViewData viewData, boolean isFromZSearch) {
        try {
            IndicatorFrame frame = convertToSectionFrame(viewData);
            if (frame == null) {
                return null;
            }

            ScreenObj obj = new ScreenObj(viewData.getSymbol(), viewData.getName());
            obj.setFilterFromZSearch(isFromZSearch);
            obj.setFrame(frame);
            return obj;
        } catch (Exception e) {
            QuoteLog.Builder.newBuilder(LOGGER, SCREENER, TEMPLATE)
                    .bizScene("convertToSection")
                    .bizSuccess(false)
                    .ext("zsearch view data convert screenobj exception {0}, {1}", viewData, isFromZSearch)
                    .throwable(e)
                    .build()
                    .error();
            return null;
        }
    }

    /**
     * convert
     */
    public static IndicatorFrame convertToSectionFrame(IndicatorViewData viewData) {
        try {
            List<IndicatorViewSeries> viewSeries = viewData.getSeries();
            Preconditions.checkArgument(viewSeries != null, "indicator view data series is null");
            Map<String, Value> indicators = new HashMap<>();
            for (IndicatorViewSeries series : viewSeries) {
                // 指标名称
                String indicatorName = series.getName();
                // 截面只有一个数据
                ViewCell cell = Iterables.getFirst(series.getCells(), null);
                if (StringUtil.isNotBlank(indicatorName)
                        && cell != null
                        && cell.getValue() != null) {
                    indicators.put(indicatorName, cell.getValue());
                } else {
                    QuoteLog.Builder.newBuilder(LOGGER, SCREENER, TEMPLATE)
                            .bizScene("convertToSection")
                            .bizSuccess(false)
                            .ext("indicator view data series name is null or cell is null, series: {0},{1}", viewData.getSymbol(), series)
                            .build()
                            .warn();
                }
            }

            return new IndicatorFrame(indicators, true);
        } catch (Exception e) {
            QuoteLog.Builder.newBuilder(LOGGER, SCREENER, TEMPLATE)
                    .bizScene("convertToSection")
                    .bizSuccess(false)
                    .ext("zsearch view data convert section frame exception {0}, {1}", viewData)
                    .throwable(e)
                    .build()
                    .error();
            return null;
        }
    }

    /**
     * 对filter进行分类重排
     */
    public static List<ScreenerProto.ScreenCompoundFilter> rearrangeFilters(
            List<ScreenerProto.ScreenCompoundFilter> filters,
            Set<String> zsearchIndicators) {

        // 如果输入为空或无需拆分，则直接返回
        if (filters == null || filters.isEmpty()) {
            return new ArrayList<>();
        }
        if (zsearchIndicators == null || zsearchIndicators.isEmpty()) {
            return filters;
        }

        List<ScreenerProto.ScreenCompoundFilter> resultFilters = new ArrayList<>();

        for (ScreenerProto.ScreenCompoundFilter filter : filters) {
            if (IS_EMPTY.test(filter.getConditionsList())) {
                resultFilters.add(filter);
                continue;
            }

            // 使用辅助函数检查此filter是否需要被拆分
            if (hasIndicators(filter, zsearchIndicators)) {
                // 使用辅助函数按指标将条件分组
                Map<Boolean, List<ScreenerProto.ScreenCondition>> groupedConditions =
                        groupConditionByIndicator(filter, zsearchIndicators);

                List<ScreenerProto.ScreenCondition> conditionsWithZsearch = groupedConditions.get(true);
                List<ScreenerProto.ScreenCondition> conditionsWithoutZsearch = groupedConditions.get(false);

                // 如果包含zsearch指标的条件列表不为空，则为它们创建一个新的filter
                if (conditionsWithZsearch != null && !conditionsWithZsearch.isEmpty()) {
                    ScreenerProto.ScreenCompoundFilter.Builder builder = filter.toBuilder()
                            .clearConditions()
                            .addAllConditions(conditionsWithZsearch);
                    // 智能处理sortCond
                    if (filter.hasSortCond()) {
                        String sortField = filter.getSortCond().getField();
                        if (zsearchIndicators.contains(sortField)) {
                            builder.setSortCond(filter.getSortCond());  // 只有zsearch filter保留zsearch sortCond
                        }
                    }
                    if (filter.hasPageCond()){
                        builder.setPageCond(filter.getPageCond());
                    }
                    resultFilters.add(builder.build());
                }

                // 如果不包含zsearch指标的条件列表不为空，则为它们创建另一个新的filter
                if (conditionsWithoutZsearch != null && !conditionsWithoutZsearch.isEmpty()) {
                    ScreenerProto.ScreenCompoundFilter.Builder builder = filter.toBuilder()
                            .clearConditions()
                            .addAllConditions(conditionsWithoutZsearch);
                    // 智能处理sortCond
                    if (filter.hasSortCond()) {
                        String sortField = filter.getSortCond().getField();
                        if (!zsearchIndicators.contains(sortField)) {
                            builder.setSortCond(filter.getSortCond());  // 只有普通filter保留普通sortCond
                        }
                    }
                    if (filter.hasSortCond()) {
                        builder.setSortCond(filter.getSortCond());
                    }
                    if (filter.hasPageCond()){
                        builder.setPageCond(filter.getPageCond());
                    }
                    resultFilters.add(builder.build());
                }
            } else {
                // 如果此filter中没有任何zsearch指标，则直接将其加入结果列表
                resultFilters.add(filter);
            }
        }

        return resultFilters;
    }

    /**
     * 检查请求是否有zsearch的指标
     */
    public static boolean hasZSearchIndicator(ScreenerProto.ScreenCompoundFilterRequest request, Set<String> zsearchIndicators) {
        Preconditions.checkArgument(request != null);
        if (IS_EMPTY.test(zsearchIndicators)) {
            return false;
        }

        // 筛选条件检查
        boolean checkFilter = request.getFiltersList()
                .stream()
                .anyMatch(filter -> hasIndicators(filter, zsearchIndicators));

        // 排序条件检查
        boolean checkSort = request.getSortCondsList()
                .stream()
                .map(SortCondOuterClass.SortCond::getField)
                .anyMatch(zsearchIndicators::contains);

        return checkFilter && checkSort;
    }


    /**
     * get indicators
     */
    public static Set<String> getRequestIndicators(ScreenerProto.ScreenCompoundFilterRequest request) {
        Set<String> indicators = new HashSet<>();
        request.getFiltersList().stream().map(ScreenUtil::getIndicatorsFromCompoundFilter).forEach(indicators::addAll);
        request.getSortCondsList().stream().map(SortCondOuterClass.SortCond::getField).forEach(indicators::add);
        indicators.addAll(request.getIndicatorsList());

        return indicators;
    }
}
