package com.alipay.tradequote.core.falcon.screener.view.service;


import com.alipay.quot.commons.models.index.IndexFields;
import com.alipay.quot.commons.push.models.base.NimitzTablePb;
import com.alipay.quot.commons.push.models.base.RsdataPb;
import com.alipay.quote.nerve.qsql.lib.value.primitive.DoubleValue;
import com.alipay.quote.nerve.qsql.lib.viewCell.ViewCell;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ObjIdKeyWrapper;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ScreenCacheKey;
import com.alipay.tradequote.core.falcon.screener.view.cache.ViewCache;
import com.alipay.tradequote.core.falcon.screener.view.drm.ScreenerViewDrm;
import com.alipay.tradequote.core.falcon.screener.view.realtime.IndicatorViewIndexUpdate;
import com.alipay.tradequote.core.falcon.screener.view.sched.IndicatorViewAssetScheduler;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.junit.Before;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import tech.tablesaw.api.LongColumn;
import tech.tablesaw.api.StringColumn;
import tech.tablesaw.api.Table;
import tech.tablesaw.io.hornet.Transplier;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.LongStream;

import static org.mockito.ArgumentMatchers.any;

public class indicatorViewHornetTest {

    @InjectMocks
    IndicatorViewIndexUpdate indicatorViewHornet;

    @Mock
    ViewCache viewCache;

    @Mock
    ScreenerViewDrm screenerViewDrm;

    @Mock
    IndicatorViewAssetScheduler indicatorViewAssetScheduler;

    String prefix = "INDEX_daily_pb_";

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }


    @org.junit.Test
    public void testCacheUpdate() {
        Mockito.when(viewCache.get(any())).thenReturn(CompletableFuture.completedFuture(null));
        Mockito.when(screenerViewDrm.getViewNameList()).thenReturn(Lists.newArrayList("view_screener"));
        Mockito.when(screenerViewDrm.getIndexVersionDate()).thenReturn("biz_date");
        indicatorViewHornet.cacheUpdate(null);
        RsdataPb.RsData rsData = Transplier.toRsDataPb(makeTable(), new HashMap<>());
        indicatorViewHornet.cacheUpdate(rsData);
        Mockito.when(viewCache.viewKeySet()).thenReturn(Sets.newHashSet(new ObjIdKeyWrapper("view_screener","600519.SH")));
        Mockito.when(viewCache.asMap()).thenReturn(viewCacheResInitial());
        indicatorViewHornet.cacheUpdate(rsData);
        Mockito.when(indicatorViewAssetScheduler.getAliasMap()).thenReturn(new HashMap<>());
        indicatorViewHornet.cacheUpdate(rsData);
        Mockito.when(indicatorViewAssetScheduler.getAliasMap()).thenReturn(aliasMap());
        indicatorViewHornet.cacheUpdate(rsData);
    }

    /**
     * table creater
     * @return
     */
    private Table makeTable() {
        LongColumn beginDateCol = LongColumn.create(IndexFields.BEGIN_DATE, LongStream.of(1));
        LongColumn bizDateCol = LongColumn.create(IndexFields.BIZ_DATE, LongStream.of(1));
        LongColumn endDateCol = LongColumn.create(IndexFields.END_DATE, LongStream.of(1));
        StringColumn symbolCol = StringColumn.create(IndexFields.SYMBOL, Arrays.asList("600519.SH"));
        LongColumn snapshotCol = LongColumn.create(IndexFields.SNAPSHOT_DATE, LongStream.of(1));
        LongColumn isValidCol = LongColumn.create(IndexFields.IS_VALID, LongStream.of(1));
        StringColumn indexCodeCol = StringColumn.create(IndexFields.INDEX_CODE, Arrays.asList("daily_pb"));
        StringColumn valueListCol = StringColumn.create(IndexFields.VALUELIST, Arrays.asList("{\"cells\":[{\"key\":\"plate_valuation_level\",\"value\":{\"type\":\"TString\",\"sv\":\"level4\"}},{\"key\":\"plate_valuation_scope\",\"value\":{\"type\":\"TDouble\",\"dv\":63.175395}}]}"));


        return Table.create("DS_INDEX",
                beginDateCol,
                bizDateCol,
                endDateCol,
                symbolCol,
                snapshotCol,
                isValidCol,
                indexCodeCol,
                valueListCol);
    }


    private Map<String, String> aliasMap(){
        Map<String, String> map = new HashMap<>();
        map.put(prefix + "30day_changePercent", "thirty_days_change_percent");
        map.put(prefix + "valuelist", "sixty_daily_value");
        return map;
    }


    private ConcurrentMap<@NonNull ScreenCacheKey, @NonNull CompletableFuture<ConcurrentHashMap<String, ViewCell>>> viewCacheResInitial(){
        ConcurrentHashMap<String, ViewCell> viewCellConcurrentHashMap = new ConcurrentHashMap<>();
        viewCellConcurrentHashMap.put("sixty_daily_value",ViewCell.builder()
                .value(DoubleValue.of(9089.00))
                .snapshotDate(System.currentTimeMillis())
                .build());

        viewCellConcurrentHashMap.put("amount",ViewCell.builder()
                .value(DoubleValue.of(19089.00))
                .snapshotDate(1702286493854L)
                .build());
        ConcurrentHashMap<ScreenCacheKey, CompletableFuture<ConcurrentHashMap<String, ViewCell>>> res = new ConcurrentHashMap<>();
        ScreenCacheKey key = new ObjIdKeyWrapper("view_screener","600519.SH");
        res.put(key ,CompletableFuture.completedFuture(viewCellConcurrentHashMap));
        return res;
    }
}
