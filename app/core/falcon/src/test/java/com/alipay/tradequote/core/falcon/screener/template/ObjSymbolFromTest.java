/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.template;

import com.alipay.quot.commons.facade.model.SymbolDTO;
import com.alipay.tradequote.core.falcon.screener.AbstractScreenerTest;
import com.alipay.tradequote.core.falcon.screener.ScreenRepoService;
import com.alipay.tradequote.core.falcon.screener.ScreenSymbolService;
import com.alipay.tradequote.core.falcon.screener.config.ScreenConfigService;
import com.alipay.tradequote.core.falcon.screener.model.template.Context;
import com.alipay.tradequote.core.falcon.screener.model.template.FromRequest;
import com.alipay.tradequote.core.falcon.screener.model.template.FromResult;
import com.alipay.tradequote.core.falcon.screener.template.impl.ObjSymbolFrom;
import com.google.common.collect.Sets;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
public class ObjSymbolFromTest extends AbstractScreenerTest {

    @InjectMocks
    ObjSymbolFrom from = new ObjSymbolFrom();

    @Mock
    ScreenRepoService repoService;

    @Mock
    ScreenSymbolService symbolService;

    @Mock
    ScreenConfigService configService;


    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        from.setScreenRepoService(repoService);
        from.setScreenSymbolService(symbolService);
        from.setScreenConfigService(configService);
    }

    @Test
    public void test1() throws Exception {
        when(configService.getObjSymbolListedType()).thenReturn(Sets.newHashSet("List_D"));
        List<SymbolDTO> symbols = getObjs("symbol_list_1.csv");
        when(symbolService.query(any())).thenReturn(symbols);
        Context context = Context.builder().build();
        FromRequest request = FromRequest.builder()
                .gql("mkt=SH,SZ")
                .build();

        FromResult result = from.execute(context, request);
        assertNotNull(result);
    }
}
