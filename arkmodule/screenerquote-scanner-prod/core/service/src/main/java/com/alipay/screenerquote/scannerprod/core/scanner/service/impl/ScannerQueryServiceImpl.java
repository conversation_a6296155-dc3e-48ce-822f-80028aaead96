/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.service.impl;

import com.alipay.quot.commons.facade.model.querycond.RangeCond;
import com.alipay.quote.log.logger.QuoteLog;
import com.alipay.screenerquote.scannerprod.integration.zsearch.ZsearchClient;
import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerPortfolio;
import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerPortfolioBO;
import com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerConfigService;
import com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerQueryService;
import com.alipay.screenerquote.scannerprod.log.DedicateLog;
import com.alipay.sofa.runtime.api.annotation.SofaReference;
import com.alipay.zsearch.core.SearchResult;
import com.alipay.zsearch.core.query.BoolQueryBuilder;
import com.alipay.zsearch.core.query.RangeQueryBuilder;
import com.alipay.zsearch.core.query.TermsQueryBuilder;
import com.alipay.zsearch.core.search.SearchSourceBuilder;
import com.alipay.zsearch.core.search.sort.SortOrder;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL1_SCANNER;
import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL2_SERVICE;

/**
 * <AUTHOR>
 * @version ScannerQueryServiceIMpl.java, v 0.1 2025年02月25日 14:31 lzt
 */
public class ScannerQueryServiceImpl implements ScannerQueryService {

    /**
     * logger
     */
    private static final Logger LOGGER = DedicateLog.PROD_BIZ_SERVICE_LOGGER;

    /**
     * zsearch client
     */
    @SofaReference
    private ZsearchClient zsearchClient;

    /**
     * scannerConfigService
     */
    @Setter
    private ScannerConfigService scannerDrm;

    @Override
    public List<ScannerPortfolio> queryPortfolio(ScannerPortfolioBO request) {
        //查询zsearch,后续可能新增其他数据中间件
        return queryZsearch(request);
    }

    /**
     * 查询zsearch
     * @param request
     * @return
     */
    private List<ScannerPortfolio> queryZsearch(ScannerPortfolioBO request) {

        long start = System.currentTimeMillis();
        //构造zsearch请求
        SearchSourceBuilder builder = makeRequest(request);

        QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SERVICE)
                .ext("查询请求,bizType:{0},request:{1}", zsearchClient.getBizType(), builder)
                .build()
                .info();

        SearchResult searchResult = zsearchClient.search(zsearchClient.getBizType(), builder);
        List<String> searchResultList = parseSearchResult(searchResult);
        //查询&结果转化
        List<ScannerPortfolio> results = searchResultList.stream()
                .map(this::parse)
                .collect(Collectors.toList());

        QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SERVICE)
                .bizSuccess(true)
                .bizUseTime(System.currentTimeMillis() - start)
                .ext("查询成功,数据:{0}", results.size())
                .build()
                .info();
        return results;
    }

    /**
     * 构造zsearch请求
     *
     * @param request
     * @return
     */
    private SearchSourceBuilder makeRequest(ScannerPortfolioBO request) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();
        sourceBuilder.query(boolBuilder);

        boolBuilder.must(new TermsQueryBuilder("strategyId", request.getStrategyId()));
        boolBuilder.must(new TermsQueryBuilder("env", scannerDrm.getQueryEnv()));
        if (StringUtils.isNotBlank(request.getSrcId())) {
            boolBuilder.must(new TermsQueryBuilder("srcId", request.getSrcId()));
        }

        //添加rangeCond条件
        addRangeCond(boolBuilder, request.getRangeCond());

        //添加PageCond条件
        sourceBuilder.from(0).size(request.getPageCond().getLimit());

        //添加SortCond条件
        sourceBuilder.sort(request.getRangeCond().getField(), getSortOrder(request.getReversed()));
        return sourceBuilder;
    }

    /**
     * 获取排序方式
     *
     * @param reversed
     * @return
     */
    private SortOrder getSortOrder(boolean reversed) {
        return reversed ? SortOrder.DESC : SortOrder.ASC;
    }

    /**
     * 解析zsearch搜索结果
     *
     * @param sr
     * @return
     */
    private List<String> parseSearchResult(SearchResult sr) {
        if (sr == null || !sr.isSucceeded()) {
            //查询失败抛异常，异常具体日志已在client打印
            throw new RuntimeException("查询zsearch失败，result:" + sr);
        } else if (null == sr.getSourceAsStringList()) {
            //查询成功，但sr.getSourceAsStringList()会返回null，处理该情况
            return new ArrayList<>();
        } else {
            //查询成功
            return sr.getSourceAsStringList();
        }
    }

    /**
     * parse
     *
     * @param searchResult
     * @return
     */
    private ScannerPortfolio parse(String searchResult) {
        Gson gson = new Gson();
        JsonObject jsonObject = gson.fromJson(searchResult, JsonObject.class);
        return gson.fromJson(jsonObject, ScannerPortfolio.class);
    }

    /**
     * 添加rangeCond
     *
     * @param boolBuilder
     * @param rangeCond
     */
    private static void addRangeCond(BoolQueryBuilder boolBuilder, RangeCond rangeCond) {
        RangeQueryBuilder range = new RangeQueryBuilder(rangeCond.getField());
        range.to(rangeCond.getMax(), !rangeCond.isMaxExcluded());
        range.from(rangeCond.getMin(), !rangeCond.isMinExcluded());
        boolBuilder.must(range);
    }
}