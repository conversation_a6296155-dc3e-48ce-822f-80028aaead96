/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.template;

import com.alipay.logicview.sdk.model.request.IndicatorViewQueryRequest;
import com.alipay.logicview.sdk.model.response.IndicatorViewData;
import com.alipay.logicview.sdk.model.response.IndicatorViewQueryResponse;
import com.alipay.logicview.sdk.model.response.IndicatorViewSeries;
import com.alipay.logicview.sdk.service.entry.IndicatorViewZSearchQueryService;
import com.alipay.quot.commons.models.ScreenerProto;
import com.alipay.quote.nerve.qsql.lib.value.primitive.DoubleValue;
import com.alipay.quote.nerve.qsql.lib.value.primitive.StringValue;
import com.alipay.quote.nerve.qsql.lib.viewCell.ViewCell;
import com.alipay.tradequote.core.falcon.screener.config.ScreenConfigService;
import com.alipay.tradequote.core.falcon.screener.indicator.IndicatorFrameService;
import com.alipay.tradequote.core.falcon.screener.model.DataType;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorFrame;
import com.alipay.tradequote.core.falcon.screener.model.template.*;
import com.alipay.tradequote.core.falcon.screener.template.impl.FilterResultJoin;
import com.alipay.tradequote.core.falcon.screener.template.impl.Joiner;
import com.google.common.collect.Sets;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class FilterResultJoinTest {

    @Mock
    private ScreenConfigService screenConfigService;

    @Mock
    private IndicatorFrameService indicatorFrameService;

    @Mock
    private IndicatorViewZSearchQueryService indicatorViewZSearchQueryService;

    private FilterResultJoin filterResultJoin;

    @Before
    public void setUp() {
        filterResultJoin = new FilterResultJoin();
        filterResultJoin.setScreenConfigService(screenConfigService);
        filterResultJoin.setIndicatorFrameService(indicatorFrameService);
        filterResultJoin.setIndicatorViewZSearchQueryService(indicatorViewZSearchQueryService);
    }

    // ========== Joiner 类测试 ==========

    @Test
    public void testJoiner_leftJoin_withNullLeftCollection_returnsEmptyList() {
        Function<String, String> keyExtractor = s -> s;
        Function<List<String>, Map<String, String>> batchFetcher = keys -> new HashMap<>();
        BiFunction<String, String, String> merger = (left, right) -> left + right;

        List<String> result = Joiner.leftJoin(null, keyExtractor, batchFetcher, merger);

        assertTrue(result.isEmpty());
    }

    @Test
    public void testJoiner_leftJoin_withEmptyLeftCollection_returnsEmptyList() {
        Function<String, String> keyExtractor = s -> s;
        Function<List<String>, Map<String, String>> batchFetcher = keys -> new HashMap<>();
        BiFunction<String, String, String> merger = (left, right) -> left + right;

        List<String> result = Joiner.leftJoin(Collections.emptyList(), keyExtractor, batchFetcher, merger);

        assertTrue(result.isEmpty());
    }

    @Test
    public void testJoiner_leftJoin_withValidData_returnsJoinedResults() {
        List<String> leftData = Arrays.asList("key1", "key2", "key3");
        Function<String, String> keyExtractor = s -> s;
        Function<List<String>, Map<String, String>> batchFetcher = keys -> {
            Map<String, String> rightData = new HashMap<>();
            rightData.put("key1", "value1");
            rightData.put("key2", "value2");
            // key3 没有对应的右表数据
            return rightData;
        };
        BiFunction<String, String, String> merger = (left, right) -> left + ":" + right;

        List<String> result = Joiner.leftJoin(leftData, keyExtractor, batchFetcher, merger);

        assertEquals(2, result.size());
        assertTrue(result.contains("key1:value1"));
        assertTrue(result.contains("key2:value2"));
        assertFalse(result.contains("key3")); // key3没有匹配到右表数据，被过滤掉
    }

    @Test
    public void testJoiner_leftJoin_withDuplicateKeys_handlesCorrectly() {
        List<String> leftData = Arrays.asList("key1", "key1", "key2");
        Function<String, String> keyExtractor = s -> s;
        Function<List<String>, Map<String, String>> batchFetcher = keys -> {
            assertEquals(2, keys.size()); // 应该去重
            assertTrue(keys.contains("key1"));
            assertTrue(keys.contains("key2"));

            Map<String, String> rightData = new HashMap<>();
            rightData.put("key1", "value1");
            rightData.put("key2", "value2");
            return rightData;
        };
        BiFunction<String, String, String> merger = (left, right) -> left + ":" + right;

        List<String> result = Joiner.leftJoin(leftData, keyExtractor, batchFetcher, merger);

        assertEquals(3, result.size());
        assertEquals(2, result.stream().filter(s -> s.equals("key1:value1")).count());
        assertEquals(1, result.stream().filter(s -> s.equals("key2:value2")).count());
    }

    @Test
    public void testJoiner_leftJoin_withEmptyRightData_returnsEmptyList() {
        List<String> leftData = Arrays.asList("key1", "key2");
        Function<String, String> keyExtractor = s -> s;
        Function<List<String>, Map<String, String>> batchFetcher = keys -> new HashMap<>();
        BiFunction<String, String, String> merger = (left, right) -> left + ":" + right;

        List<String> result = Joiner.leftJoin(leftData, keyExtractor, batchFetcher, merger);

        assertTrue(result.isEmpty());
    }

    // ========== FilterResultJoin 类测试 ==========

    @Test
    public void testExecute_withEmptyObjs_returnsEmptyResult() {
        CompoundFilterResult filterResult = CompoundFilterResult.builder()
                .objs(Collections.emptyList())
                .filterIndicators(Sets.newHashSet("indicator1"))
                .build();

        ScreenerProto.ScreenCompoundFilterRequest request =
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder().build();

        JoinRequest joinRequest = JoinRequest.builder()
                .filterResult(filterResult)
                .request(request)
                .build();

        JoinResult result = filterResultJoin.execute(joinRequest);

        assertNotNull(result);
        assertTrue(result.getJoinedResult().isEmpty());
        assertEquals(Sets.newHashSet("indicator1"), result.getFilterIndicators());
    }

    @Test
    public void testExecute_withOnlyZSearchObjs_enrichesWithLocalData() throws Exception {
        // 准备测试数据
        IndicatorFrame mockFrame = new IndicatorFrame(Collections.singletonMap("local_indicator", new StringValue("local_value")));
        ScreenObj zsearchObj = new ScreenObj("600519.SH", "TestStock");
        zsearchObj.setFilterFromZSearch(true);
        zsearchObj.setFrame(new IndicatorFrame(Collections.singletonMap("zsearch_indicator", new StringValue("zsearch_value"))));

        CompoundFilterResult filterResult = CompoundFilterResult.builder()
                .objs(Collections.singletonList(zsearchObj))
                .filterIndicators(Sets.newHashSet("indicator1"))
                .build();

        ScreenerProto.ScreenCompoundFilterRequest request =
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder().build();

        JoinRequest joinRequest = JoinRequest.builder()
                .filterResult(filterResult)
                .request(request)
                .build();

        // Mock 依赖
        when(indicatorFrameService.getFrame(DataType.DS_INDICATOR_FRAME, "600519.SH"))
                .thenReturn(mockFrame);

        JoinResult result = filterResultJoin.execute(joinRequest);

        assertNotNull(result);
        assertEquals(1, result.getJoinedResult().size());

        ScreenObj enrichedObj = result.getJoinedResult().get(0);
        assertEquals("600519.SH", enrichedObj.getObjId());
        assertTrue(enrichedObj.isFilterFromZSearch());

        // 验证frame被合并了
        Map<String, com.alipay.quote.nerve.qsql.lib.value.Value> mergedData = enrichedObj.getFrame().getData();
        assertTrue(mergedData.containsKey("zsearch_indicator"));
        assertTrue(mergedData.containsKey("local_indicator"));

        verify(indicatorFrameService).getFrame(DataType.DS_INDICATOR_FRAME, "600519.SH");
    }

    @Test
    public void testExecute_withOnlyNonZSearchObjs_noZSearchIndicator_returnsOriginalObjs() {
        ScreenObj nonZsearchObj = new ScreenObj("000001.SZ", "TestStock");
        nonZsearchObj.setFilterFromZSearch(false);
        nonZsearchObj.setFrame(new IndicatorFrame(Collections.singletonMap("normal_indicator", new StringValue("normal_value"))));

        CompoundFilterResult filterResult = CompoundFilterResult.builder()
                .objs(Collections.singletonList(nonZsearchObj))
                .filterIndicators(Sets.newHashSet("indicator1"))
                .build();

        ScreenerProto.ScreenCompoundFilterRequest request =
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder().build();

        JoinRequest joinRequest = JoinRequest.builder()
                .filterResult(filterResult)
                .request(request)
                .build();

        // Mock 没有zsearch指标
        when(screenConfigService.getZSearchIndicators()).thenReturn(Sets.newHashSet("zsearch_indicator"));

        JoinResult result = filterResultJoin.execute(joinRequest);

        assertNotNull(result);
        assertEquals(1, result.getJoinedResult().size());
        assertEquals(nonZsearchObj, result.getJoinedResult().get(0));

        // 验证没有调用zsearch服务
        verifyNoInteractions(indicatorViewZSearchQueryService);
    }

    @Test
    public void testExecute_withNonZSearchObjs_hasZSearchIndicator_enrichesWithZSearchData() throws Exception {
        ScreenObj nonZsearchObj = new ScreenObj("000001.SZ", "TestStock");
        nonZsearchObj.setFilterFromZSearch(false);
        nonZsearchObj.setFrame(new IndicatorFrame(Collections.singletonMap("normal_indicator", new StringValue("normal_value"))));

        CompoundFilterResult filterResult = CompoundFilterResult.builder()
                .objs(Collections.singletonList(nonZsearchObj))
                .filterIndicators(Sets.newHashSet("indicator1"))
                .build();

        // 创建包含zsearch指标的请求
        ScreenerProto.ScreenCompoundFilterRequest request =
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder()
                        .addIndicators("zsearch_indicator")
                        .build();

        JoinRequest joinRequest = JoinRequest.builder()
                .filterResult(filterResult)
                .request(request)
                .build();

        // Mock zsearch指标存在
        when(screenConfigService.getZSearchIndicators()).thenReturn(Sets.newHashSet("zsearch_indicator"));

        // Mock zsearch查询响应
        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSymbol("000001.SZ");
        viewData.setName("TestStock");

        IndicatorViewSeries series = new IndicatorViewSeries();
        series.setName("zsearch_indicator");
        series.setCells(Collections.singletonList(ViewCell.builder().value(new DoubleValue(123.45)).build()));
        viewData.setSeries(Collections.singletonList(series));

        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Collections.singletonList(viewData));

        when(indicatorViewZSearchQueryService.queryBySymbols(any(IndicatorViewQueryRequest.class)))
                .thenReturn(response);

        JoinResult result = filterResultJoin.execute(joinRequest);

        assertNotNull(result);
        assertEquals(1, result.getJoinedResult().size());

        ScreenObj enrichedObj = result.getJoinedResult().get(0);
        assertEquals("000001.SZ", enrichedObj.getObjId());
        assertFalse(enrichedObj.isFilterFromZSearch());

        // 验证frame被合并了
        Map<String, com.alipay.quote.nerve.qsql.lib.value.Value> mergedData = enrichedObj.getFrame().getData();
        assertTrue(mergedData.containsKey("normal_indicator"));
        assertTrue(mergedData.containsKey("zsearch_indicator"));

        verify(indicatorViewZSearchQueryService).queryBySymbols(any(IndicatorViewQueryRequest.class));
    }

    @Test
    public void testExecute_withMixedObjs_enrichesBothTypes() throws Exception {
        // 准备zsearch对象
        ScreenObj zsearchObj = new ScreenObj("600519.SH", "ZSearchStock");
        zsearchObj.setFilterFromZSearch(true);
        zsearchObj.setFrame(new IndicatorFrame(Collections.singletonMap("zsearch_indicator", new StringValue("zsearch_value"))));

        // 准备非zsearch对象
        ScreenObj nonZsearchObj = new ScreenObj("000001.SZ", "NormalStock");
        nonZsearchObj.setFilterFromZSearch(false);
        nonZsearchObj.setFrame(new IndicatorFrame(Collections.singletonMap("normal_indicator", new StringValue("normal_value"))));

        CompoundFilterResult filterResult = CompoundFilterResult.builder()
                .objs(Arrays.asList(zsearchObj, nonZsearchObj))
                .filterIndicators(Sets.newHashSet("indicator1"))
                .build();

        // 创建包含zsearch指标的请求
        ScreenerProto.ScreenCompoundFilterRequest request =
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder()
                        .addIndicators("zsearch_indicator")
                        .build();

        JoinRequest joinRequest = JoinRequest.builder()
                .filterResult(filterResult)
                .request(request)
                .build();

        // Mock 依赖
        IndicatorFrame localFrame = new IndicatorFrame(Collections.singletonMap("local_indicator", new StringValue("local_value")));
        when(indicatorFrameService.getFrame(DataType.DS_INDICATOR_FRAME, "600519.SH"))
                .thenReturn(localFrame);

        when(screenConfigService.getZSearchIndicators()).thenReturn(Sets.newHashSet("zsearch_indicator"));

        // Mock zsearch查询响应
        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSymbol("000001.SZ");
        viewData.setName("NormalStock");

        IndicatorViewSeries series = new IndicatorViewSeries();
        series.setName("zsearch_indicator");
        series.setCells(Collections.singletonList(ViewCell.builder().value(new DoubleValue(123.45)).build()));
        viewData.setSeries(Collections.singletonList(series));

        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Collections.singletonList(viewData));

        when(indicatorViewZSearchQueryService.queryBySymbols(any(IndicatorViewQueryRequest.class)))
                .thenReturn(response);

        JoinResult result = filterResultJoin.execute(joinRequest);

        assertNotNull(result);
        assertEquals(2, result.getJoinedResult().size());

        // 验证zsearch对象被enriched
        ScreenObj enrichedZsearchObj = result.getJoinedResult().stream()
                .filter(obj -> obj.getObjId().equals("600519.SH"))
                .findFirst().orElse(null);
        assertNotNull(enrichedZsearchObj);
        assertTrue(enrichedZsearchObj.isFilterFromZSearch());
        assertTrue(enrichedZsearchObj.getFrame().getData().containsKey("zsearch_indicator"));
        assertTrue(enrichedZsearchObj.getFrame().getData().containsKey("local_indicator"));

        // 验证非zsearch对象被enriched
        ScreenObj enrichedNonZsearchObj = result.getJoinedResult().stream()
                .filter(obj -> obj.getObjId().equals("000001.SZ"))
                .findFirst().orElse(null);
        assertNotNull(enrichedNonZsearchObj);
        assertFalse(enrichedNonZsearchObj.isFilterFromZSearch());
        assertTrue(enrichedNonZsearchObj.getFrame().getData().containsKey("normal_indicator"));
        assertTrue(enrichedNonZsearchObj.getFrame().getData().containsKey("zsearch_indicator"));

        verify(indicatorFrameService).getFrame(DataType.DS_INDICATOR_FRAME, "600519.SH");
        verify(indicatorViewZSearchQueryService).queryBySymbols(any(IndicatorViewQueryRequest.class));
    }

    @Test
    public void testExecute_withLocalFrameServiceException_handlesGracefully() throws Exception {
        ScreenObj zsearchObj = new ScreenObj("600519.SH", "TestStock");
        zsearchObj.setFilterFromZSearch(true);
        zsearchObj.setFrame(new IndicatorFrame(Collections.singletonMap("zsearch_indicator", new StringValue("zsearch_value"))));

        CompoundFilterResult filterResult = CompoundFilterResult.builder()
                .objs(Collections.singletonList(zsearchObj))
                .filterIndicators(Sets.newHashSet("indicator1"))
                .build();

        ScreenerProto.ScreenCompoundFilterRequest request =
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder().build();

        JoinRequest joinRequest = JoinRequest.builder()
                .filterResult(filterResult)
                .request(request)
                .build();

        // Mock 异常
        when(indicatorFrameService.getFrame(DataType.DS_INDICATOR_FRAME, "600519.SH"))
                .thenThrow(new RuntimeException("Service exception"));

        JoinResult result = filterResultJoin.execute(joinRequest);

        assertNotNull(result);
        // 由于local frame查询失败，zsearch对象不会被enriched，所以结果为空
        assertTrue(result.getJoinedResult().isEmpty());

        verify(indicatorFrameService).getFrame(DataType.DS_INDICATOR_FRAME, "600519.SH");
    }

    @Test
    public void testExecute_withZSearchServiceException_handlesGracefully() throws Exception {
        ScreenObj nonZsearchObj = new ScreenObj("000001.SZ", "TestStock");
        nonZsearchObj.setFilterFromZSearch(false);
        nonZsearchObj.setFrame(new IndicatorFrame(Collections.singletonMap("normal_indicator", new StringValue("normal_value"))));

        CompoundFilterResult filterResult = CompoundFilterResult.builder()
                .objs(Collections.singletonList(nonZsearchObj))
                .filterIndicators(Sets.newHashSet("indicator1"))
                .build();

        ScreenerProto.ScreenCompoundFilterRequest request =
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder()
                        .addIndicators("zsearch_indicator")
                        .build();

        JoinRequest joinRequest = JoinRequest.builder()
                .filterResult(filterResult)
                .request(request)
                .build();

        // Mock zsearch指标存在
        when(screenConfigService.getZSearchIndicators()).thenReturn(Sets.newHashSet("zsearch_indicator"));

        // Mock zsearch服务异常
        when(indicatorViewZSearchQueryService.queryBySymbols(any(IndicatorViewQueryRequest.class)))
                .thenThrow(new RuntimeException("ZSearch service exception"));

        JoinResult result = filterResultJoin.execute(joinRequest);

        assertNotNull(result);
        // 由于zsearch查询失败，非zsearch对象不会被enriched，所以结果为空
        assertTrue(result.getJoinedResult().isEmpty());

        verify(indicatorViewZSearchQueryService).queryBySymbols(any(IndicatorViewQueryRequest.class));
    }

    @Test
    public void testExecute_withNullZSearchResponse_handlesGracefully() throws Exception {
        ScreenObj nonZsearchObj = new ScreenObj("000001.SZ", "TestStock");
        nonZsearchObj.setFilterFromZSearch(false);
        nonZsearchObj.setFrame(new IndicatorFrame(Collections.singletonMap("normal_indicator", new StringValue("normal_value"))));

        CompoundFilterResult filterResult = CompoundFilterResult.builder()
                .objs(Collections.singletonList(nonZsearchObj))
                .filterIndicators(Sets.newHashSet("indicator1"))
                .build();

        ScreenerProto.ScreenCompoundFilterRequest request =
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder()
                        .addIndicators("zsearch_indicator")
                        .build();

        JoinRequest joinRequest = JoinRequest.builder()
                .filterResult(filterResult)
                .request(request)
                .build();

        when(screenConfigService.getZSearchIndicators()).thenReturn(Sets.newHashSet("zsearch_indicator"));
        when(indicatorViewZSearchQueryService.queryBySymbols(any(IndicatorViewQueryRequest.class)))
                .thenReturn(null);

        JoinResult result = filterResultJoin.execute(joinRequest);

        assertNotNull(result);
        assertTrue(result.getJoinedResult().isEmpty());

        verify(indicatorViewZSearchQueryService).queryBySymbols(any(IndicatorViewQueryRequest.class));
    }

    @Test
    public void testExecute_withEmptyZSearchResponseData_handlesGracefully() throws Exception {
        ScreenObj nonZsearchObj = new ScreenObj("000001.SZ", "TestStock");
        nonZsearchObj.setFilterFromZSearch(false);
        nonZsearchObj.setFrame(new IndicatorFrame(Collections.singletonMap("normal_indicator", new StringValue("normal_value"))));

        CompoundFilterResult filterResult = CompoundFilterResult.builder()
                .objs(Collections.singletonList(nonZsearchObj))
                .filterIndicators(Sets.newHashSet("indicator1"))
                .build();

        ScreenerProto.ScreenCompoundFilterRequest request =
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder()
                        .addIndicators("zsearch_indicator")
                        .build();

        JoinRequest joinRequest = JoinRequest.builder()
                .filterResult(filterResult)
                .request(request)
                .build();

        when(screenConfigService.getZSearchIndicators()).thenReturn(Sets.newHashSet("zsearch_indicator"));

        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Collections.emptyList()); // 空数据

        when(indicatorViewZSearchQueryService.queryBySymbols(any(IndicatorViewQueryRequest.class)))
                .thenReturn(response);

        JoinResult result = filterResultJoin.execute(joinRequest);

        assertNotNull(result);
        assertTrue(result.getJoinedResult().isEmpty());

        verify(indicatorViewZSearchQueryService).queryBySymbols(any(IndicatorViewQueryRequest.class));
    }

    @Test
    public void testExecute_withNullViewDataInZSearchResponse_filtersOut() throws Exception {
        ScreenObj nonZsearchObj = new ScreenObj("000001.SZ", "TestStock");
        nonZsearchObj.setFilterFromZSearch(false);
        nonZsearchObj.setFrame(new IndicatorFrame(Collections.singletonMap("normal_indicator", new StringValue("normal_value"))));

        CompoundFilterResult filterResult = CompoundFilterResult.builder()
                .objs(Collections.singletonList(nonZsearchObj))
                .filterIndicators(Sets.newHashSet("indicator1"))
                .build();

        ScreenerProto.ScreenCompoundFilterRequest request =
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder()
                        .addIndicators("zsearch_indicator")
                        .build();

        JoinRequest joinRequest = JoinRequest.builder()
                .filterResult(filterResult)
                .request(request)
                .build();

        when(screenConfigService.getZSearchIndicators()).thenReturn(Sets.newHashSet("zsearch_indicator"));

        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Arrays.asList((IndicatorViewData) null)); // null数据

        when(indicatorViewZSearchQueryService.queryBySymbols(any(IndicatorViewQueryRequest.class)))
                .thenReturn(response);

        JoinResult result = filterResultJoin.execute(joinRequest);

        assertNotNull(result);
        assertTrue(result.getJoinedResult().isEmpty());

        verify(indicatorViewZSearchQueryService).queryBySymbols(any(IndicatorViewQueryRequest.class));
    }

    @Test
    public void testExecute_withFilterIndicatorsCollection_collectsCorrectly() throws Exception {
        ScreenObj zsearchObj = new ScreenObj("600519.SH", "TestStock");
        zsearchObj.setFilterFromZSearch(true);
        zsearchObj.setFilterIndicators(Sets.newHashSet("obj_indicator1", "obj_indicator2"));
        zsearchObj.setFrame(new IndicatorFrame(Collections.singletonMap("zsearch_indicator", new StringValue("zsearch_value"))));

        CompoundFilterResult filterResult = CompoundFilterResult.builder()
                .objs(Collections.singletonList(zsearchObj))
                .filterIndicators(Sets.newHashSet("filter_indicator1", "filter_indicator2"))
                .build();

        ScreenerProto.ScreenCompoundFilterRequest request =
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder().build();

        JoinRequest joinRequest = JoinRequest.builder()
                .filterResult(filterResult)
                .request(request)
                .build();

        IndicatorFrame localFrame = new IndicatorFrame(Collections.singletonMap("local_indicator", new StringValue("local_value")));
        when(indicatorFrameService.getFrame(DataType.DS_INDICATOR_FRAME, "600519.SH"))
                .thenReturn(localFrame);

        JoinResult result = filterResultJoin.execute(joinRequest);

        assertNotNull(result);
        assertEquals(1, result.getJoinedResult().size());

        // 验证filterIndicators被正确收集
        Set<String> expectedIndicators = Sets.newHashSet("filter_indicator1", "filter_indicator2", "obj_indicator1", "obj_indicator2");
        assertEquals(expectedIndicators, result.getFilterIndicators());

        verify(indicatorFrameService).getFrame(DataType.DS_INDICATOR_FRAME, "600519.SH");
    }

    @Test
    public void testExecute_withNullFilterIndicatorsInScreenObj_handlesGracefully() throws Exception {
        ScreenObj zsearchObj = new ScreenObj("600519.SH", "TestStock");
        zsearchObj.setFilterFromZSearch(true);
        zsearchObj.setFilterIndicators(null); // null filterIndicators
        zsearchObj.setFrame(new IndicatorFrame(Collections.singletonMap("zsearch_indicator", new StringValue("zsearch_value"))));

        CompoundFilterResult filterResult = CompoundFilterResult.builder()
                .objs(Collections.singletonList(zsearchObj))
                .filterIndicators(Sets.newHashSet("filter_indicator1"))
                .build();

        ScreenerProto.ScreenCompoundFilterRequest request =
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder().build();

        JoinRequest joinRequest = JoinRequest.builder()
                .filterResult(filterResult)
                .request(request)
                .build();

        IndicatorFrame localFrame = new IndicatorFrame(Collections.singletonMap("local_indicator", new StringValue("local_value")));
        when(indicatorFrameService.getFrame(DataType.DS_INDICATOR_FRAME, "600519.SH"))
                .thenReturn(localFrame);

        JoinResult result = filterResultJoin.execute(joinRequest);

        assertNotNull(result);
        assertEquals(1, result.getJoinedResult().size());

        // 验证只有原始的filterIndicators
        assertEquals(Sets.newHashSet("filter_indicator1"), result.getFilterIndicators());

        verify(indicatorFrameService).getFrame(DataType.DS_INDICATOR_FRAME, "600519.SH");
    }

    @Test
    public void testJoiner_leftJoin_withNullKeyFromExtractor_handlesGracefully() {
        List<String> leftData = Arrays.asList("key1", null, "key2");
        Function<String, String> keyExtractor = s -> s; // null会被直接返回
        Function<List<String>, Map<String, String>> batchFetcher = keys -> {
            Map<String, String> rightData = new HashMap<>();
            rightData.put("key1", "value1");
            rightData.put("key2", "value2");
            rightData.put(null, "null_value"); // 为null key提供值
            return rightData;
        };
        BiFunction<String, String, String> merger = (left, right) -> (left == null ? "null" : left) + ":" + right;

        List<String> result = Joiner.leftJoin(leftData, keyExtractor, batchFetcher, merger);

        assertEquals(3, result.size());
        assertTrue(result.contains("key1:value1"));
        assertTrue(result.contains("key2:value2"));
        assertTrue(result.contains("null:null_value"));
    }

    @Test
    public void testJoiner_leftJoin_withExceptionInBatchFetcher_propagatesException() {
        List<String> leftData = Arrays.asList("key1", "key2");
        Function<String, String> keyExtractor = s -> s;
        Function<List<String>, Map<String, String>> batchFetcher = keys -> {
            throw new RuntimeException("Batch fetcher exception");
        };
        BiFunction<String, String, String> merger = (left, right) -> left + ":" + right;

        try {
            Joiner.leftJoin(leftData, keyExtractor, batchFetcher, merger);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException e) {
            assertEquals("Batch fetcher exception", e.getMessage());
        }
    }
}
