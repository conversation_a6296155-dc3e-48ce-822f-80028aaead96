package com.alipay.tradequote.core.falcon.screener.view.entry;

import com.alibaba.lindorm.thirdparty.com.google.common.collect.Maps;
import com.alipay.quote.nerve.qsql.lib.value.Value;
import com.alipay.quote.nerve.qsql.lib.value.primitive.DoubleValue;
import com.alipay.quote.nerve.qsql.lib.value.primitive.NullValue;
import com.alipay.quote.nerve.qsql.lib.value.primitive.StringValue;
import com.alipay.quote.nerve.qsql.lib.viewCell.ViewCell;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ObjIdKeyWrapper;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ScreenCacheKey;
import com.alipay.tradequote.core.falcon.screener.view.entry.impl.ViewCacheEntryImpl;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static org.mockito.BDDMockito.*;
import static org.junit.Assert.*;

import com.alipay.tradequote.core.falcon.screener.view.cache.ViewCache;
import com.alipay.quot.commons.facade.model.base.ObjRsData;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

public class ViewCacheEntryImplTest {

    @Mock
    private ViewCache viewCache;

    @InjectMocks
    private ViewCacheEntryImpl viewCacheEntry;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getValue_ShouldReturnResult_WhenViewNameIsValid() throws Exception {
        // Arrange - 设置模拟条件
        List<String> symbols = Arrays.asList("symbol1", "symbol2");
        String viewName = "validViewName";

        ConcurrentHashMap<String, ViewCell> viewCellMap = new ConcurrentHashMap<>();
        // 假设ViewCell是一个你需要的返回值类型
        ViewCell viewCell = ViewCell.builder().build();// 使用实际的ViewCell或者模拟的ViewCell
        viewCellMap.put("symbol1", viewCell);


        CompletableFuture<Map<ScreenCacheKey, ConcurrentHashMap<String, ViewCell>>> future = CompletableFuture.completedFuture(null);
        // 模拟viewCache.getAll()被调用时返回的值
        given(viewCache.getAll(any())).willReturn(future);

        // Act - 执行测试方法
        List<ObjRsData> result = viewCacheEntry.getValue(symbols, viewName);

        // Assert - 验证结果
        assertNull(result);

        future = cacheValueBatchQuery();
        given(viewCache.getAll(any())).willReturn(future);

        given(viewCache.getAll(any())).willReturn(future);
        // Act - 执行测试方法
        List<ObjRsData> result_two = viewCacheEntry.getValue(symbols, viewName);

        // 根据你的cacheValue2RsData方法实际返回的值来断言
        assertNotNull(result_two);

    }

    @Test
    public void getValue_ShouldReturnNull_WhenViewNameIsEmpty() {
        // Arrange
        List<String> symbols = Arrays.asList("symbol1", "symbol2");
        String viewName = "";

        // Act
        List<ObjRsData> result = viewCacheEntry.getValue(symbols, viewName);

        // Assert
        assertNull(result);
    }

    @Test
    public void getValue_ShouldHandleException_WhenViewCacheThrowsException() throws Exception {
        // Arrange
        List<String> symbols = Arrays.asList("symbol1", "symbol2");
        String viewName = "validViewName";

        // 模拟一个异常的场景，当viewCache.getAll()被调用时抛出异常
        CompletableFuture<Map<ScreenCacheKey, ConcurrentHashMap<String, ViewCell>>> future = CompletableFuture.completedFuture(null);
        given(viewCache.getAll(any())).willThrow(new RuntimeException("Cache access error"));

        // Act
        List<ObjRsData> result = viewCacheEntry.getValue(symbols, viewName);

        // Assert
        assertNull(result);
    }

    private CompletableFuture<Map<ScreenCacheKey, ConcurrentHashMap<String, ViewCell>>> cacheValueBatchQuery() {
        ConcurrentHashMap<ScreenCacheKey, ConcurrentHashMap<String, ViewCell>> resMap = new ConcurrentHashMap<>();

        ConcurrentHashMap<String, ViewCell> valueOne = new ConcurrentHashMap<>();
        valueOne.put("plate_name", ViewCell.builder().value(new StringValue("白酒")).snapshotDate(System.currentTimeMillis()).build());
        valueOne.put("price", ViewCell.builder().value(DoubleValue.of(18898.98)).snapshotDate(System.currentTimeMillis()).build());
        valueOne.put("amount", ViewCell.builder().value(new NullValue()).snapshotDate(System.currentTimeMillis()).build());
        resMap.put(new ObjIdKeyWrapper("view_screener", "600519.SH"), valueOne);

        ConcurrentHashMap<String, ViewCell> valueTwo = new ConcurrentHashMap<>();
        valueTwo.put("plate_name", ViewCell.builder().value(new StringValue("工程机械")).snapshotDate(System.currentTimeMillis()).build());
        valueTwo.put("price", ViewCell.builder().value(DoubleValue.of(9.98)).snapshotDate(System.currentTimeMillis()).build());
        valueTwo.put("amount", ViewCell.builder().value(DoubleValue.of(100.90)).snapshotDate(System.currentTimeMillis()).build());
        resMap.put(new ObjIdKeyWrapper("view_screener", "300987.SZ"), valueTwo);

        CompletableFuture<Map<ScreenCacheKey, ConcurrentHashMap<String, ViewCell>>> future = CompletableFuture.completedFuture(resMap);

        return future;
    }
    // 更多测试方法...

    private CompletableFuture<ConcurrentHashMap<String, ViewCell>> cacheValueQuery() {

        ConcurrentHashMap<String, ViewCell> value = new ConcurrentHashMap<>();
        value.put("plate_name", ViewCell.builder().value(new StringValue("白酒")).snapshotDate(System.currentTimeMillis()).build());
        value.put("price", ViewCell.builder().value(DoubleValue.of(18898.98)).snapshotDate(System.currentTimeMillis()).build());
        value.put("amount", ViewCell.builder().value(new NullValue()).snapshotDate(System.currentTimeMillis()).build());

        CompletableFuture<ConcurrentHashMap<String, ViewCell>> future = CompletableFuture.completedFuture(value);
        return future;
    }

    @Test
    public void testGetValueWithBlankSymbolOrViewName() {
        // 测试空字符串作为symbol或viewName的情况
        ViewCacheEntryImpl viewCacheEntry = new ViewCacheEntryImpl();
        assertEquals(Maps.newHashMap(), viewCacheEntry.getValue("", "viewName"));
        assertEquals(Maps.newHashMap(), viewCacheEntry.getValue("symbol", ""));
    }

    @Test
    public void testGetValueWithValidInput() {
        CompletableFuture<ConcurrentHashMap<String, ViewCell>> future = cacheValueQuery();
        when(viewCache.get(any())).thenReturn(future);
        Map<String, Value> viewScreener = viewCacheEntry.getValue("600519.SH", "view_screener");
        assertEquals(3, viewScreener.size());
        assertEquals("白酒", viewScreener.get("view_screener.plate_name").asString());
    }

    @Test
    public void testGetValueWithException() {
        when(viewCache.get(any())).thenThrow(new RuntimeException());
        Map<String, Value> viewScreener = viewCacheEntry.getValue("600519.SH", "view_screener");
        assertEquals(0, viewScreener.size());
        assertEquals(Maps.newHashMap(), viewScreener);
    }

    @Test
    public void testNan() {
        System.out.println(Double.compare(Double.NaN, 1.0d));
        System.out.println(Double.compare(-1.0d, Double.NaN));
        System.out.println(Double.compare(Double.NaN, Double.POSITIVE_INFINITY));

        double nan = Double.NaN;
        double value1 = 1.0d;
        double value2 = -1.0d;

        System.out.println(nan>value1);
        System.out.println(nan>value2);

        System.out.println(nan<value1);
        System.out.println(nan<value2);
    }

}

