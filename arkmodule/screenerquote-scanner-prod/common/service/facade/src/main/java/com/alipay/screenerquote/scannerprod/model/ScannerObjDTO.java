/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.model;

import com.alipay.quot.commons.facade.model.base.VCell;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version ScreenerObjDTO.java, v 0.1 2025年02月27日 10:20 lzt
 */
@Getter
@Setter
@ToString
@Builder
public class ScannerObjDTO implements Serializable {
    private static final long serialVersionUID = 6585732403026102712L;

    /**
     * symbol
     */
    private String symbol;

    /**
     * name
     */
    private String name;

    /**
     * mkt
     */
    private String market;

    /**
     * type
     */
    private String type;

    /**
     * subType
     */
    private String subType;

    /**
     * st
     */
    private boolean st;

    /**
     * 上市状态
     */
    private String listedStatus;

    /**
     * 所属的分组信息
     */
    private List<ScannerObjGroupDTO> belongGroups;

    /**
     * 指标
     */
    private Map<String, VCell> indicators;

    /**
     * 顺序
     */
    private Integer ordinal;

    /**
     * 所处百分位
     */
    private Double percentile;
}