/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener;

import com.alipay.quot.commons.models.ScreenerProto;
import com.alipay.quot.commons.models.querycond.PageCondOuterClass;
import com.alipay.quot.commons.models.querycond.SortCondOuterClass;
import com.alipay.tradequote.core.falcon.screener.util.ScreenRequestValidator;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
public class ScreenRequestValidatorTest {

    @InjectMocks
    private ScreenRequestValidator screenRequestValidator;

    @Mock
    private ScreenRepoService screenRepoService;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        screenRequestValidator = new ScreenRequestValidator();
    }


    @Test
    public void test1() {
        ScreenerProto.ScreenStrategyRequest screenStrategyRequest = ScreenerProto.ScreenStrategyRequest.newBuilder()
                .setPageCond(PageCondOuterClass.PageCond.newBuilder()
                        .setOffset(0)
                        .setLimit(10)
                        .build())
                .setStrategyId("test")
                .setObjFilter(ScreenerProto.ScreenObjFilter.newBuilder()
                        .setObjProp(ScreenerProto.ScreenObjProp.newBuilder()
                                .addAllMarkets(Lists.newArrayList("SH", "SZ"))
                                .addAllTypes(Lists.newArrayList("ES"))
                                .addAllSubTypes(Lists.newArrayList("ASH", "KSH", "GEM", "KCDR"))
                                .build())
                        .setObjBelongTo(ScreenerProto.ScreenObjBelongTo.newBuilder()
                                .addAllBelongObjIds(Lists.newArrayList("161005.OF"))
                                .build())
                        .build())
                .build();

        ScreenRequestValidator validator = new ScreenRequestValidator();
        validator.validRequest(screenStrategyRequest);
    }

    @Test
    public void test2(){
        ScreenerProto.ScreenStrategyRequest screenStrategyRequest = ScreenerProto.ScreenStrategyRequest.newBuilder()
                .setPageCond(PageCondOuterClass.PageCond.newBuilder()
                        .setOffset(0)
                        .setLimit(10)
                        .build())
                .setStrategyId("test")
                .setObjFilter(ScreenerProto.ScreenObjFilter.newBuilder()
                        .setObjProp(ScreenerProto.ScreenObjProp.newBuilder()
                                .addAllMarkets(Lists.newArrayList("SH", "SZ"))
                                .addAllTypes(Lists.newArrayList("ES"))
                                .addAllSubTypes(Lists.newArrayList("ASH", "KSH", "GEM", "KCDR"))
                                .setListTypeFilter(ScreenerProto.ScreenUdfPropFilter.newBuilder()
                                        .setUseUdfFilter(true)
                                        .setUdfFilter("")
                                        .build())
                                .build())
                        .setObjBelongTo(ScreenerProto.ScreenObjBelongTo.newBuilder()
                                .addAllBelongObjIds(Lists.newArrayList("161005.OF"))
                                .build())

                        .build())
                .setPercentCond(ScreenerProto.PercentCond.newBuilder().setPercent(5)
                        .setRoundMode(ScreenerProto.RoundMode.ROUND_DOWN)
                        .build())
                .build();
        ScreenRequestValidator validator = new ScreenRequestValidator();
        validator.validRequest(screenStrategyRequest);
    }

    @Test
    public void test3(){
        ScreenerProto.ScreenStrategyRequest screenStrategyRequest = ScreenerProto.ScreenStrategyRequest.newBuilder()
                .setPageCond(PageCondOuterClass.PageCond.newBuilder()
                        .setOffset(0)
                        .setLimit(10)
                        .build())
                .setStrategyId("test")
                .setObjFilter(ScreenerProto.ScreenObjFilter.newBuilder()
                        .setObjProp(ScreenerProto.ScreenObjProp.newBuilder()
                                .addAllMarkets(Lists.newArrayList("SH", "SZ"))
                                .addAllTypes(Lists.newArrayList("ES"))
                                .addAllSubTypes(Lists.newArrayList("ASH", "KSH", "GEM", "KCDR"))
                                .setListTypeFilter(ScreenerProto.ScreenUdfPropFilter.newBuilder()
                                        .setUseUdfFilter(true)
                                        .setUdfFilter("")
                                        .build())
                                .build())
                        .setObjBelongTo(ScreenerProto.ScreenObjBelongTo.newBuilder()
                                .addAllBelongObjIds(Lists.newArrayList("161005.OF"))
                                .build())
                        .build())
                .setPercentCond(ScreenerProto.PercentCond.newBuilder().setPercent(101)
                        .setRoundMode(ScreenerProto.RoundMode.ROUND_UP)
                        .build())
                .build();
        ScreenRequestValidator validator = new ScreenRequestValidator();
        try{
            validator.validRequest(screenStrategyRequest);
        }catch (Exception e){
            assertTrue(e instanceof IllegalArgumentException);
        }
    }

    @Test
    public void test4(){
        when(screenRepoService.getAllowIndicators()).thenReturn(Sets.newHashSet("PRICE"));
        ScreenerProto.ScreenFilterRequest request = ScreenerProto.ScreenFilterRequest.newBuilder()
                .setPageCond(PageCondOuterClass.PageCond.newBuilder()
                        .setOffset(0)
                        .setLimit(10)
                        .build())
                .addAllFilters(Lists.newArrayList(ScreenerProto.ScreenFilter.newBuilder()
                                .setOperator("Gt")
                                .setLeft("PRICE")
                                .setRight("1.0")
                                .build()))
                .setObjFilter(ScreenerProto.ScreenObjFilter.newBuilder()
                        .setObjProp(ScreenerProto.ScreenObjProp.newBuilder()
                                .addAllMarkets(Lists.newArrayList("SH", "SZ"))
                                .addAllTypes(Lists.newArrayList("ES"))
                                .addAllSubTypes(Lists.newArrayList("ASH", "KSH", "GEM", "KCDR"))
                                .setListTypeFilter(ScreenerProto.ScreenUdfPropFilter.newBuilder()
                                        .setUseUdfFilter(true)
                                        .setUdfFilter("")
                                        .build())
                                .build())
                        .setObjBelongTo(ScreenerProto.ScreenObjBelongTo.newBuilder()
                                .addAllBelongObjIds(Lists.newArrayList("161005.OF"))
                                .build())
                        .build())
                .setPercentCond(ScreenerProto.PercentCond.newBuilder().setPercent(50)
                        .setRoundMode(ScreenerProto.RoundMode.ROUND_UP)
                        .build())
                .addSortConds(SortCondOuterClass.SortCond.newBuilder()
                        .setDesc(true)
                        .setField("PRICE")
                        .build())
                .build();


        screenRequestValidator.validRequest(request);
    }


    @Test
    public void test5(){
        when(screenRepoService.getAllowIndicators()).thenReturn(Sets.newHashSet("PRICE"));
        ScreenerProto.ScreenCompoundFilterRequest request = ScreenerProto.ScreenCompoundFilterRequest.newBuilder()
                .addAllFilters(Lists.newArrayList(ScreenerProto.ScreenCompoundFilter.newBuilder()
                                .addConditions(ScreenerProto.ScreenCondition.newBuilder().build()).build()))
                .setObjFilter(ScreenerProto.ScreenObjFilter.newBuilder()
                        .setObjProp(ScreenerProto.ScreenObjProp.newBuilder()
                                .addAllMarkets(Lists.newArrayList("SH", "SZ"))
                                .addAllTypes(Lists.newArrayList("ES"))
                                .addAllSubTypes(Lists.newArrayList("ASH", "KSH", "GEM", "KCDR"))
                                .setListTypeFilter(ScreenerProto.ScreenUdfPropFilter.newBuilder()
                                        .setUseUdfFilter(true)
                                        .setUdfFilter("")
                                        .build())
                                .build())
                        .setObjBelongTo(ScreenerProto.ScreenObjBelongTo.newBuilder()
                                .addAllBelongObjIds(Lists.newArrayList("161005.OF"))
                                .build())
                        .build())
                .setPercentCond(ScreenerProto.PercentCond.newBuilder().setPercent(50)
                        .setRoundMode(ScreenerProto.RoundMode.ROUND_UP)
                        .build())
                .setPageCond(PageCondOuterClass.PageCond.newBuilder()
                        .setOffset(0)
                        .setLimit(10)
                        .build())
                .addSortConds(SortCondOuterClass.SortCond.newBuilder()
                        .setDesc(true)
                        .setField("PRICE")
                        .build())
                .build();

        screenRequestValidator.validRequest(request);
    }
}
