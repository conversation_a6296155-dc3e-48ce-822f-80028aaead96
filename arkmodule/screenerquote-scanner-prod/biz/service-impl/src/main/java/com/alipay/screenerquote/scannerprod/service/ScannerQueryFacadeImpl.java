/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.service;

import com.alibaba.common.lang.StringUtil;
import com.alipay.quot.commons.facade.result.Result;
import com.alipay.quot.commons.facade.result.Status;
import com.alipay.quote.log.logger.QuoteLog;
import com.alipay.screenerquote.prod.model.ScreenerObjDTO;
import com.alipay.screenerquote.prod.model.ScreenerObjGroupDTO;
import com.alipay.screenerquote.scannerprod.api.ScannerQueryFacade;
import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerPortfolio;
import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerPortfolioBO;
import com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerQueryService;
import com.alipay.screenerquote.scannerprod.log.DedicateLog;
import com.alipay.screenerquote.scannerprod.model.*;
import com.alipay.sofa.runtime.api.annotation.SofaReference;
import com.google.common.base.Preconditions;
import org.slf4j.Logger;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL1_SCANNER;
import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL2_SERVICE;

/**
 * <AUTHOR>
 * @version ScannerQueryFacadeImpl.java, v 0.1 2025年02月25日 10:57 lzt
 */
public class ScannerQueryFacadeImpl implements ScannerQueryFacade {

    private static final Logger LOGGER = DedicateLog.PROD_BIZ_SERVICE_LOGGER;

    /**
     * scannerQueryService
     */
    @SofaReference
    private ScannerQueryService scannerQueryService;

    @Override
    public Result<ScannerPortfolioResponseDTO> queryPortfolio(ScannerPortfolioRequestDTO request) {
        long start = System.currentTimeMillis();
        try {
            Preconditions.checkArgument(Objects.nonNull(request), "请求为空");
            Preconditions.checkArgument(StringUtil.isNotBlank(request.getSceneId()), "场景Id为空");
            Preconditions.checkArgument(
                    StringUtil.isNotBlank(request.getStrategyId()), "StrategyId为空");
            Preconditions.checkArgument(request.getRangeCond() != null &&
                    request.getRangeCond().isValid(),"rangeCond不合法");
            Preconditions.checkArgument(request.getPageCond() != null &&
                    request.getPageCond().isValid(),"pageCond不合法");

            List<ScannerPortfolio> results = scannerQueryService.queryPortfolio(build(request));

            Result<ScannerPortfolioResponseDTO> result = new Result<>();
            result.setData(build(results));
            result.setStatus(Status.SUCCESS);
            return result;
        } catch (IllegalArgumentException ie) {
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SERVICE)
                    .bizSuccess(false)
                    .bizUseTime(System.currentTimeMillis() - start)
                    .ext("请求参数异常,{0}", request)
                    .throwable(ie)
                    .build()
                    .error();

            Result<ScannerPortfolioResponseDTO> result = new Result<>();
            result.setStatus(Status.BAD_REQUEST);
            return result;
        } catch (Exception e) {
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SERVICE)
                    .bizSuccess(false)
                    .bizUseTime(System.currentTimeMillis() - start)
                    .ext("选股服务处理异常,{0}", request)
                    .throwable(e)
                    .build()
                    .error();

            Result<ScannerPortfolioResponseDTO> result = new Result<>();
            result.setStatus(Status.ERROR);
            return result;
        }
    }

    /**
     *
     * @param results
     * @return
     */
    private ScannerPortfolioResponseDTO build(List<ScannerPortfolio> results) {
        ScannerPortfolioResponseDTO responseDTO = new ScannerPortfolioResponseDTO();
        List<ScannerPortfolioDTO> list = results.stream().map(this::convertToDTO).collect(Collectors.toList());
        responseDTO.setPortfolioList(list);
        return responseDTO;
    }

    /**
     * build
     * @param scannerPortfolio
     * @return
     */
    private ScannerPortfolioDTO convertToDTO(ScannerPortfolio scannerPortfolio) {
        ScannerPortfolioDTO portfolioDTO = new ScannerPortfolioDTO();
        portfolioDTO.setStrategyId(scannerPortfolio.getStrategyId());
        portfolioDTO.setSrcId(scannerPortfolio.getSrcId());
        portfolioDTO.setBeginDate(scannerPortfolio.getBeginDate());
        portfolioDTO.setBizDate(scannerPortfolio.getBizDate());
        portfolioDTO.setSnapshotDate(scannerPortfolio.getSnapshotDate());
        portfolioDTO.setObjs(scannerPortfolio.getObjs().stream().map(this::build).collect(Collectors.toList()));
        portfolioDTO.setCount(scannerPortfolio.getCount());
        portfolioDTO.setObjCount(scannerPortfolio.getObjCount());
        return portfolioDTO;
    }

    private ScannerObjDTO build(ScreenerObjDTO screenerObjDTO) {
        return ScannerObjDTO.builder()
                .symbol(screenerObjDTO.getSymbol())
                .type(screenerObjDTO.getType())
                .subType(screenerObjDTO.getSubType())
                .st(screenerObjDTO.isSt())
                .market(screenerObjDTO.getMarket())
                .name(screenerObjDTO.getName())
                .listedStatus(screenerObjDTO.getListedStatus())
                .belongGroups(buildGroup(screenerObjDTO.getBelongGroups()))
                .indicators(screenerObjDTO.getIndicators())
                .ordinal(screenerObjDTO.getOrdinal())
                .percentile(screenerObjDTO.getPercentile())
                .build();
    }

    private List<ScannerObjGroupDTO> buildGroup(List<ScreenerObjGroupDTO> belongGroups) {
        return belongGroups.stream().map(this::build).collect(Collectors.toList());
    }

    private ScannerObjGroupDTO build(ScreenerObjGroupDTO screenerObjGroupDTO) {
        return ScannerObjGroupDTO.builder()
                .groupId(screenerObjGroupDTO.getGroupId())
                .ordinal(screenerObjGroupDTO.getOrdinal())
                .percentile(screenerObjGroupDTO.getPercentile())
                .extProps(screenerObjGroupDTO.getExtProps())
                .build();
    }

    /**
     * build
     * @param request
     * @return
     */
    private ScannerPortfolioBO build(ScannerPortfolioRequestDTO request) {
        return ScannerPortfolioBO.builder()
                .strategyId(request.getStrategyId())
                .srcId(request.getSrcId())
                .rangeCond(request.getRangeCond())
                .pageCond(request.getPageCond())
                .reversed(request.getReversed())
                .build();
    }
}