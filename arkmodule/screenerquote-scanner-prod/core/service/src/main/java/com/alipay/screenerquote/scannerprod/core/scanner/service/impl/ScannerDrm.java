/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.service.impl;

import com.alibaba.fastjson.JSON;
import com.alipay.drm.client.DRMClient;
import com.alipay.drm.client.api.annotation.AfterUpdate;
import com.alipay.drm.client.api.annotation.DAttribute;
import com.alipay.drm.client.api.annotation.DResource;
import com.alipay.quote.log.logger.QuoteLog;
import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerJobDescriptor;
import com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerConfigService;
import com.alipay.screenerquote.scannerprod.log.DedicateLog;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL1_SCANNER;
import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL2_DRM;

/**
 * <AUTHOR>
 * @version ScannerDrm.java, v 0.1 2025年02月14日 16:39 lzt
 */
@Getter
@Setter
@DResource(id = "com.alipay.screenerquote.scannerprod.core.scanner.service.impl.ScannerDrm")
public class ScannerDrm implements ScannerConfigService {

    private static final Logger LOGGER = DedicateLog.PROD_BIZ_SERVICE_LOGGER;

    /**
     * init
     */
    public void init() {
        DRMClient.getInstance().register(this, "screenerquote");
    }

    /**
     * 任务配置
     */
    @DAttribute
    private String taskConfig;

    /**
     * 任务配置,key:taskId
     */
    private Map<String, ScannerJobDescriptor> taskConfigMap = new HashMap<>();

    /**
     * 环境参数,枚举值:dev/pre/gray/prod
     * 请勿推送其他值
     */
    @DAttribute
    private String writeEnv = "";

    /**
     * 环境参数,枚举值:dev/pre/gray/prod
     * 请勿推送其他值
     */
    @DAttribute
    private String queryEnv = "";

    /**
     * 是否开启标签写入
     */
    @DAttribute
    private boolean openTagWrite = false;

    /**
     * 是否本地写入
     * @param key
     * @param value
     */
    @DAttribute
    private boolean localWriteTag = false;

    /**
     * drm更新后置处理
     *
     * @param key   key
     * @param value value
     */
    @AfterUpdate
    public void onUpdate(String key, Object value) {
        if (key.equals("taskConfig")) {
            parseTaskConfig(taskConfig);
        }
    }

    /**
     * 解析任务配置
     *
     * @param taskConfig 任务配置
     */
    private void parseTaskConfig(String taskConfig) {
        try {
            Map<String, ScannerJobDescriptor> taskConfigMapBak = new HashMap<>();
            if (StringUtils.isNotBlank(taskConfig)) {
                // 解析 JSON 数组，并通过 Stream 转换为 Map
                List<ScannerJobDescriptor> descriptorList = JSON.parseArray(taskConfig, ScannerJobDescriptor.class);
                taskConfigMapBak = descriptorList.stream()
                        .collect(Collectors.toMap(ScannerJobDescriptor::getTaskId, descriptor -> descriptor));
            }
            taskConfigMap = taskConfigMapBak;
        } catch (Exception e) {
            //log
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_DRM)
                    .ext("taskConfig解析失败,taskConfig:{0}", taskConfig)
                    .throwable(e)
                    .build()
                    .error();
        }
    }

    /**
     * 基于taskId获取任务配置，没有则返回null
     *
     * @param taskId
     * @return
     */
    @Override
    public ScannerJobDescriptor getScannerJobDescriptor(String taskId) {
        return taskConfigMap.getOrDefault(taskId, null);
    }

}