/*
 * Ant Group
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.service.fininfoflow;

import com.alipay.quot.commons.falcon.api.FinInfoFlow;
import com.alipay.quot.commons.falcon.api.FinInfoFlowFilterDim;
import com.alipay.tradequote.common.dal.mapper.quotscene.FinInfoFlowFilterDimMapper;
import com.alipay.tradequote.common.dal.mapper.quotscene.FinInfoFlowMapper;
import com.alipay.tradequote.common.dal.model.quotscene.FinInfoFlowFilterDimPO;
import com.alipay.tradequote.core.falcon.drm.FinInfoFlowWriteDrm;
import com.alipay.tradequote.core.falcon.service.fininfoflow.converter.FinInfoFlowConverter;
import com.alipay.tradequote.integration.zsearch.ZSearchClient;
import com.alipay.zsearch.core.BulkResult;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;


import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @version FinInfoFlowWriteServiceImplTest.java, v 0.1 2024年04月11日 17:28 lzt
 */
public class FinInfoFlowWriteServiceImplTest {

    @InjectMocks
    private FinInfoFlowWriteServiceImpl finInfoFlowWriteService;

    @Mock
    private FinInfoFlowMapper finInfoFlowMapper;

    @Mock
    private FinInfoFlowFilterDimMapper finInfoFlowFilterDimMapper;

    @Mock
    private FinInfoFlowWriteDrm finInfoFlowWriteDrm;

    @Mock
    private ZSearchClient finInfoFlowZSearchClient;
    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        finInfoFlowWriteService.setFinInfoFlowMapper(finInfoFlowMapper);
        finInfoFlowWriteService.setFinInfoFlowFilterDimMapper(finInfoFlowFilterDimMapper);
        finInfoFlowWriteService.setFinInfoFlowZSearchClient(finInfoFlowZSearchClient);
        finInfoFlowWriteService.setFinInfoFlowWriteDrm(finInfoFlowWriteDrm);
    }

    @Test
    public void writeFinInfoFlow() {

        BulkResult bulkResult = Mockito.mock(BulkResult.class);
        when(bulkResult.isSucceeded()).thenReturn(true);
        when(finInfoFlowFilterDimMapper.queryFinInfoFilterDimByCond(any(), any())).thenReturn(Lists.newArrayList(getFinInfoFlowFilterDimPO()));
        when(finInfoFlowMapper.insertOrUpdateBatch(any())).thenReturn(1L);
        when(finInfoFlowZSearchClient.batchIndex(any(), any(), anyLong())).thenReturn(bulkResult);
        when(finInfoFlowWriteDrm.getPartitionSize()).thenReturn(1);
        finInfoFlowWriteService.updateFinInfoFlow(Lists.newArrayList(getFinInfoFlowDO()));
    }

    @Test
    public void writeFinInfoFlow1() {

        BulkResult bulkResult = Mockito.mock(BulkResult.class);
        when(bulkResult.isSucceeded()).thenReturn(false);
        BulkResult.BulkResultItem item = Mockito.mock(BulkResult.BulkResultItem.class);
        when(bulkResult.getFailedItems()).thenReturn(Lists.newArrayList(item));

        when(finInfoFlowFilterDimMapper.queryFinInfoFilterDimByCond(any(), any())).thenReturn(Lists.newArrayList(getFinInfoFlowFilterDimPO()));
        when(finInfoFlowMapper.insertOrUpdateBatch(any())).thenReturn(1L);
        when(finInfoFlowZSearchClient.batchIndex(any(), any(), anyLong())).thenReturn(bulkResult);
        when(finInfoFlowWriteDrm.getPartitionSize()).thenReturn(1);

        boolean isException = false;
        try {
            finInfoFlowWriteService.updateFinInfoFlow(Lists.newArrayList(getFinInfoFlowDO()));
        } catch (Exception e) {
            isException = true;
        }
        Assert.assertTrue(isException);
    }

    @Test
    public void writeFinInfoFlowFilterDim() {
        List<FinInfoFlowFilterDim.FinInfoFlowFilterDimDO> finInfoFlowFilterDimDOS = Lists.newArrayList(getFinInfoFlowFilterDimDO());

        BulkResult bulkResult = Mockito.mock(BulkResult.class);
        when(bulkResult.isSucceeded()).thenReturn(false);
        BulkResult.BulkResultItem item = Mockito.mock(BulkResult.BulkResultItem.class);
        when(bulkResult.getFailedItems()).thenReturn(Lists.newArrayList(item));

        when(finInfoFlowMapper.queryFinInfoFlowWithInfoIdAndInfoType(any(), any())).thenReturn(Lists.newArrayList(FinInfoFlowConverter.convertFinInfoFlowPO(getFinInfoFlowDO())));
        when(finInfoFlowFilterDimMapper.queryFinInfoFilterDimByCond(any(), any())).thenReturn(Lists.newArrayList(getFinInfoFlowFilterDimPO()));
        when(finInfoFlowMapper.insertOrUpdate(any())).thenReturn(1L);
        when(finInfoFlowZSearchClient.batchIndex(any(), any(), anyLong())).thenReturn(bulkResult);
        when(finInfoFlowWriteDrm.getPartitionSize()).thenReturn(1);
        boolean isException = false;
        try {
            finInfoFlowWriteService.updateFinInfoFlowFilterDim(finInfoFlowFilterDimDOS);
        } catch (Exception e) {
            isException = true;
        }
        Assert.assertTrue(isException);
    }

    @Test
    public void writeFinInfoFlowFilterDim1() {
        List<FinInfoFlowFilterDim.FinInfoFlowFilterDimDO> finInfoFlowFilterDimDOS = Lists.newArrayList(getFinInfoFlowFilterDimDO());

        BulkResult bulkResult = Mockito.mock(BulkResult.class);
        when(bulkResult.isSucceeded()).thenReturn(true);
        when(finInfoFlowMapper.queryFinInfoFlowWithInfoIdAndInfoType(any(), any())).thenReturn(Lists.newArrayList(FinInfoFlowConverter.convertFinInfoFlowPO(getFinInfoFlowDO())));
        when(finInfoFlowFilterDimMapper.queryFinInfoFilterDimByCond(any(), any())).thenReturn(Lists.newArrayList(getFinInfoFlowFilterDimPO()));
        when(finInfoFlowMapper.insertOrUpdate(any())).thenReturn(1L);
        when(finInfoFlowZSearchClient.batchIndex(any(), any(), anyLong())).thenReturn(bulkResult);
        when(finInfoFlowWriteDrm.getPartitionSize()).thenReturn(1);

        finInfoFlowWriteService.updateFinInfoFlowFilterDim(finInfoFlowFilterDimDOS);
    }

    @Test
    public void writeFinInfoFlowFilterDim2() {
        List<FinInfoFlowFilterDim.FinInfoFlowFilterDimDO> finInfoFlowFilterDimDOS = Lists.newArrayList(getFinInfoFlowFilterDimDO());

        when(finInfoFlowMapper.queryFinInfoFlowWithInfoIdAndInfoType(any(), any())).thenReturn(Lists.newArrayList());

        finInfoFlowWriteService.updateFinInfoFlowFilterDim(finInfoFlowFilterDimDOS);
    }

    @Test
    public void delete() {
        BulkResult bulkResult = Mockito.mock(BulkResult.class);
        when(bulkResult.isSucceeded()).thenReturn(true);
        when(finInfoFlowMapper.queryFinInfoFlowWithInfoIdAndInfoType(any(), any())).thenReturn(Lists.newArrayList(FinInfoFlowConverter.convertFinInfoFlowPO(getFinInfoFlowDO())));
        when(finInfoFlowFilterDimMapper.queryFinInfoFilterDimByCond(any(), any())).thenReturn(Lists.newArrayList(getFinInfoFlowFilterDimPO()));
        when(finInfoFlowMapper.insertOrUpdateBatch(any())).thenReturn(1L);
        when(finInfoFlowZSearchClient.batchIndex(any(), any(), anyLong())).thenReturn(bulkResult);
        when(finInfoFlowWriteDrm.getPartitionSize()).thenReturn(1);
        finInfoFlowWriteService.deleteFinInfoFlow(Lists.newArrayList(getFinInfoFlowDO()));
    }

    @Test
    public void delete1() {
        BulkResult bulkResult = Mockito.mock(BulkResult.class);
        when(bulkResult.isSucceeded()).thenReturn(false);
        BulkResult.BulkResultItem item = Mockito.mock(BulkResult.BulkResultItem.class);
        when(bulkResult.getFailedItems()).thenReturn(Lists.newArrayList(item));

        when(finInfoFlowMapper.queryFinInfoFlowWithInfoIdAndInfoType(any(), any())).thenReturn(Lists.newArrayList(FinInfoFlowConverter.convertFinInfoFlowPO(getFinInfoFlowDO())));
        when(finInfoFlowFilterDimMapper.queryFinInfoFilterDimByCond(any(), any())).thenReturn(Lists.newArrayList(getFinInfoFlowFilterDimPO()));
        when(finInfoFlowMapper.insertOrUpdateBatch(any())).thenReturn(1L);
        when(finInfoFlowZSearchClient.batchIndex(any(), any(), anyLong())).thenReturn(bulkResult);
        when(finInfoFlowWriteDrm.getPartitionSize()).thenReturn(1);

        boolean isException = false;
        try {
            finInfoFlowWriteService.deleteFinInfoFlow(Lists.newArrayList(getFinInfoFlowDO()));
        } catch (Exception e) {
            isException = true;
        }
        Assert.assertTrue(isException);
    }

    private FinInfoFlow.FinInfoFlowDO getFinInfoFlowDO() {
        return FinInfoFlow.FinInfoFlowDO.newBuilder()
                .setSymbol("symbol")
                .setDelStatus("0")
                .setInfoType("infoType")
                .setInfoId("infoId")
                .setBizTime(10L)
                .setFeedId("feedId")
                .setInfoDisplayFields("infoDisplayFields")
                .setExtend("extend")
                .build();
    }

    private FinInfoFlowFilterDim.FinInfoFlowFilterDimDO getFinInfoFlowFilterDimDO() {
        return FinInfoFlowFilterDim.FinInfoFlowFilterDimDO.newBuilder()
                .setInfoType("infoType")
                .setInfoId("infoId")
                .setFilterKey("filterKey")
                .setFilterValue("filterValue")
                .setDelStatus("1")
                .build();
    }

    private FinInfoFlowFilterDimPO getFinInfoFlowFilterDimPO() {
        FinInfoFlowFilterDimPO finInfoFlowFilterDimPO = new FinInfoFlowFilterDimPO();
        finInfoFlowFilterDimPO.setInfoType("infoType");
        finInfoFlowFilterDimPO.setInfoId("infoId");
        finInfoFlowFilterDimPO.setFilterKey("filterKey");
        finInfoFlowFilterDimPO.setFilterValue("filterValue");
        finInfoFlowFilterDimPO.setDelStatus(true);

        return finInfoFlowFilterDimPO;
    }

}