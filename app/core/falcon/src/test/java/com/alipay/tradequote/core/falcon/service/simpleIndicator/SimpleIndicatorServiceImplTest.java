/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.service.simpleIndicator;

import com.alipay.quot.commons.falcon.inner.Indicator.IndicatorItem;
import com.alipay.quot.commons.falcon.inner.Indicator.IndicatorList;
import com.alipay.quot.commons.falcon.inner.Indicator.ObjIndicatorList;
import com.alipay.quote.hulk.model.HulkCacheScoredValue;
import com.alipay.quote.hulk.model.HulkCacheValue;
import com.alipay.quote.hulk.model.QueryCond;
import com.alipay.tradequote.core.falcon.model.DataWrapper;
import com.alipay.tradequote.core.falcon.service.simpleindicator.SimpleIndicatorServiceImpl;
import com.alipay.tradequote.core.falcon.storage.client.StorageClient;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @version : SimpleIndicatorServiceImplTest.java, v 0.1 2021年02月18日 9:36 PM qinwei Exp $
 */
public class SimpleIndicatorServiceImplTest {
    SimpleIndicatorServiceImpl service;

    @Mock
    private StorageClient storageClient;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        service = new SimpleIndicatorServiceImpl();
        service.setStorageKey("store_key");
        service.setStorageClient(storageClient);
    }

    @Test
    public void query() {
        when(storageClient.zrange(any(), any(), any(QueryCond.class))).thenReturn(makeQueryRes());
        try {
            final ObjIndicatorList query = service.query("600570.SH.SH.L2", makeQueryCond());
        }catch (Exception ex){

        }
    }

    @Test
    public void queryWithTag() {
        when(storageClient.zrange(any(), any(), any(QueryCond.class))).thenReturn(makeQueryRes());
        final ObjIndicatorList query = service.query("600570.SH.SH.L2", "123", makeQueryCond());
    }

    @Test
    public void updateBatch() {
        service.getStorageKey();
        service.updateBatch(makeDOs());
        Mockito.doThrow(new RuntimeException()).when(storageClient).zadd(any(), any(), any());
        try {
            service.updateBatch(makeDOs());
        } catch (Exception e) {
            // nothing
        }
    }

    @Test
    public void updateBatchEmpty() {
        service.getStorageKey();
        service.updateBatch(Collections.EMPTY_LIST);
        Mockito.doThrow(new RuntimeException()).when(storageClient).zadd(any(), any(), any());
        try {
            service.updateBatch(makeDOs());
        } catch (Exception e) {
            // nothing
        }
    }

    @Test
    public void delete() {
        SimpleIndicatorServiceImpl service = new SimpleIndicatorServiceImpl();
        service.setStorageKey("store_key");
        StorageClient storageClient = mock(StorageClient.class);
        service.setStorageClient(storageClient);
        service.delete(makeDOs());
        Mockito.doThrow(new RuntimeException()).when(storageClient).zrem(any(), any(), any());
        try {
            service.delete(makeDOs());
        } catch (Exception e) {
            // nothing
        }
    }

    @Test
    public void setStorageKey() {
        service.setStorageKey("store_key");
        Assert.assertNotNull(service.getStorageKey());
    }

    @Test
    public void setStorageClient() {
        service.setStorageClient(storageClient);
        Assert.assertNotNull(service.getStorageClient());
    }

    @Test
    public void getStorageKey() {
        Assert.assertNotNull(service.getStorageKey());
    }

    @Test
    public void getStorageClient() {
        Assert.assertNotNull(service.getStorageKey());
    }

    private List<ObjIndicatorList> makeDOs() {
        return Arrays.asList(
                makeDO("600570.SH.SH.L2"),
                makeDO("600571.SH.SH.L2")
        );
    }

    private QueryCond makeQueryCond() {
        return new QueryCond();
    }

    private ObjIndicatorList makeDO(String symbol) {
        return ObjIndicatorList.newBuilder()
                .setSymbol(symbol)
                .addList(IndicatorList.newBuilder().setDate(System.currentTimeMillis()).setSnapshotDate(System.currentTimeMillis())
                        .addItems(IndicatorItem.newBuilder().setIndicatorName("123").setValue(12.2).build()).build())
                .build();
    }

    private DataWrapper makeDataWraper() {
        return DataWrapper.builder()
                .data(makeDO("600570.SH.SH.L2").toByteArray())
                .build();
    }

    private HulkCacheScoredValue<HulkCacheValue> makeHulkCacheScoredValue() {
        HulkCacheScoredValue<HulkCacheValue> hulkCacheScoredValue = new HulkCacheScoredValue<>();
        hulkCacheScoredValue.setScore(123);
        hulkCacheScoredValue.setValue(makeDataWraper());
        return hulkCacheScoredValue;
    }

    private List<HulkCacheScoredValue<HulkCacheValue>> makeQueryRes() {
        return Arrays.asList(makeHulkCacheScoredValue(), makeHulkCacheScoredValue());
    }
}