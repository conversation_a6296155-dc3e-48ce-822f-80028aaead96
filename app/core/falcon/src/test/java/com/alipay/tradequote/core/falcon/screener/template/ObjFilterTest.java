/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.template;

import com.alibaba.common.lang.StringUtil;
import com.alipay.quot.commons.facade.model.SymbolDTO;
import com.alipay.tradequote.core.falcon.screener.AbstractScreenerTest;
import com.alipay.tradequote.core.falcon.screener.ScreenRepoService;
import com.alipay.tradequote.core.falcon.screener.ScreenSymbolService;
import com.alipay.tradequote.core.falcon.screener.config.ScreenConfigService;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.IndicatorDataWrapper;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ObjIdKeyWrapper;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ScreenCacheImpl;
import com.alipay.tradequote.core.falcon.screener.model.template.Context;
import com.alipay.tradequote.core.falcon.screener.model.template.FromRequest;
import com.alipay.tradequote.core.falcon.screener.model.template.FromResult;
import com.alipay.tradequote.core.falcon.screener.model.template.ScreenObj;
import com.alipay.tradequote.core.falcon.screener.model.template.strategy.ScreenStrategy;
import com.alipay.tradequote.core.falcon.screener.template.impl.ObjFilter;
import com.google.common.collect.ImmutableSetMultimap;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static com.alipay.tradequote.core.falcon.screener.model.DataType.DS_SECTOR_COMP_LIST;
import static com.alipay.tradequote.core.falcon.screener.model.DataType.DS_OBJ_BELONG_LIST;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
public class ObjFilterTest extends AbstractScreenerTest {
    @InjectMocks
    ObjFilter objFilter;

    @Mock
    ScreenRepoService screenRepoService;

    @Mock
    ScreenSymbolService screenSymbolService;

    @Mock
    ScreenConfigService screenConfigService;

    @Mock
    ScreenCacheImpl screenCache;

    Context context = Context.builder().build();

    ImmutableSetMultimap<String, String> compList;

    ImmutableSetMultimap<String, String> belongList;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        objFilter = new ObjFilter();
        objFilter.setScreenRepoService(screenRepoService);
        objFilter.setScreenSymbolService(screenSymbolService);
        objFilter.setScreenConfigService(screenConfigService);

        screenCache = new ScreenCacheImpl();
        screenCache.init();
        compList = ImmutableSetMultimap.<String, String>builder()
                .put("812204.CNS", "300384.SZ")
                .put("812204.CNS", "600346.SH")
                .put("813704.CNS", "300937.SZ")
                .put("813704.CNS", "605266.SH")
                .build();
        screenCache.put(new ObjIdKeyWrapper(DS_SECTOR_COMP_LIST, DS_SECTOR_COMP_LIST), new IndicatorDataWrapper(compList));

        belongList = ImmutableSetMultimap.<String, String>builder()
                .put("300384.SZ", "812204.CNS")
                .put("600346.SH", "812204.CNS")
                .put("300937.SZ", "813704.CNS")
                .put("605266.SH", "813704.CNS")
                .build();
        screenCache.put(new ObjIdKeyWrapper(DS_OBJ_BELONG_LIST, DS_OBJ_BELONG_LIST), new IndicatorDataWrapper(belongList));
        objFilter.setScreenCache(screenCache);
    }

    @Test
    public void test1() throws Exception {
        FromRequest request = FromRequest.builder()
                .strategyId("test_1")
                .build();
        doReturn(false).when(screenConfigService).isOpenUdfFilter();
        when(screenRepoService.getScreenStrategy(anyString())).thenReturn(null);
        try {
            objFilter.execute(context, request);
        } catch (Exception e) {
            assertTrue(e instanceof IllegalStateException);
        }
    }

    @Test
    public void test2() {
        FromRequest request = FromRequest.builder()
                .strategyId("test_1")
                .build();
        ScreenStrategy strategy = ScreenStrategy.builder().build();
        doReturn(false).when(screenConfigService).isOpenUdfFilter();
        when(screenRepoService.getScreenStrategy(anyString())).thenReturn(strategy);
        try {
            objFilter.execute(context, request);
        } catch (Exception e) {
            assertTrue(e instanceof IllegalStateException);
        }
    }

    @Test
    public void test3() throws Exception {
        ScreenStrategy s = getStrategy("strategy1.json");
        List<String> objIds = s.getObjFilter().getObjIds();
        List<SymbolDTO> objSyms = makeObjSymbolDTOs(objIds);
        String gql = "obj=" + String.join(",", objIds);

        FromRequest request = FromRequest.builder()
                .strategyId(s.getStrategyId())
                .objFilter(null)
                .build();

        doReturn(objSyms).when(screenSymbolService).query(gql);
        doReturn(s).when(screenRepoService).getScreenStrategy(s.getStrategyId());
        doReturn(false).when(screenConfigService).isOpenUdfFilter();

        FromResult result = objFilter.execute(context, request);
        assertNotNull(result);
        assertEquals(objIds.size(), result.getObjs().size());
        for (String objId : objIds) {
            assertTrue(contains(result.getObjs(), objId));
        }
    }

    @Test
    public void test4() throws Exception {
        ScreenStrategy s = getStrategy("s2.json");

        FromRequest request = FromRequest.builder()
                .strategyId(s.getStrategyId())
                .objFilter(null)
                .build();

        doReturn(false).when(screenConfigService).isOpenUdfFilter();
        doReturn(s).when(screenRepoService).getScreenStrategy(s.getStrategyId());
        FromResult result = objFilter.execute(context, request);
    }

    @Test
    public void test5() throws Exception {
        ScreenStrategy s = getStrategy("s3.json");

        FromRequest request = FromRequest.builder()
                .strategyId(s.getStrategyId())
                .objFilter(null)
                .build();

        doReturn(true).when(screenConfigService).isOpenUdfFilter();
        doReturn(s).when(screenRepoService).getScreenStrategy(s.getStrategyId());
        FromResult result = objFilter.execute(context, request);
    }

    @Test
    public void test6() throws Exception {
        ScreenStrategy s = getStrategy("strategy1.json");
        List<String> objIds = s.getObjFilter().getObjIds();
        List<SymbolDTO> objSyms = makeObjSymbolDTOs(objIds);
        String gql = "obj=" + String.join(",", objIds);

        FromRequest request = FromRequest.builder()
                .strategyId(s.getStrategyId())
                .objFilter(null)
                .build();

        doReturn(objSyms).when(screenSymbolService).query(gql);
        doReturn(s).when(screenRepoService).getScreenStrategy(s.getStrategyId());
        doReturn(true).when(screenConfigService).isOpenUdfFilter();

        FromResult result = objFilter.execute(context, request);
        assertNotNull(result);
        assertEquals(objIds.size(), result.getObjs().size());
        for (String objId : objIds) {
            assertTrue(contains(result.getObjs(), objId));
        }
    }

    @Test
    public void test7() throws Exception {
        ScreenStrategy s = getStrategy("s4.json");

        FromRequest request = FromRequest.builder()
                .strategyId(s.getStrategyId())
                .objFilter(null)
                .build();

        doReturn(true).when(screenConfigService).isOpenUdfFilter();
        doReturn(s).when(screenRepoService).getScreenStrategy(s.getStrategyId());
        FromResult result = objFilter.execute(context, request);
    }

    @Test
    public void test8() throws Exception {
        ScreenStrategy s = getStrategy("s5.json");

        FromRequest request = FromRequest.builder()
                .strategyId(s.getStrategyId())
                .objFilter(null)
                .build();

        doReturn(false).when(screenConfigService).isOpenUdfFilter();
        doReturn(s).when(screenRepoService).getScreenStrategy(s.getStrategyId());
        FromResult result = objFilter.execute(context, request);
    }

    @Test
    public void test9() throws Exception {
        ScreenStrategy s = getStrategy("s5.json");

        FromRequest request = FromRequest.builder()
                .strategyId(s.getStrategyId())
                .objFilter(null)
                .build();

        doReturn(true).when(screenConfigService).isOpenUdfFilter();
        doReturn(s).when(screenRepoService).getScreenStrategy(s.getStrategyId());
        FromResult result = objFilter.execute(context, request);
    }

    public boolean contains(List<ScreenObj> objs, String objId) {
        return objs.stream().anyMatch(o -> StringUtil.equals(o.getObjId(), objId));
    }
}
