/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.service.impl;

import com.alipay.quote.hulk.thread.Coordinator;
import com.alipay.quote.log.logger.QuoteLog;
import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerJobContext;
import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerJobDescriptor;
import com.alipay.screenerquote.scannerprod.core.scanner.scheduler.model.ScannerTaskItem;
import com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerConfigService;
import com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerJob;
import com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerJobManager;
import com.alipay.screenerquote.scannerprod.log.DedicateLog;
import com.google.common.base.Preconditions;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL1_SCANNER;
import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL2_SCHED;
import static com.alipay.tradequote.util.Functors.IS_NOT_EMPTY;

/**
 * <AUTHOR>
 * @version ScannerTaskServiceImpl.java, v 0.1 2025年02月21日 17:25 lzt
 */
@Setter
public class ScannerJobManagerImpl implements ScannerJobManager {

    private static final Logger LOGGER = DedicateLog.PROD_SCHED_LOGGER;

    /**
     * Series任务线程池
     */
    private Coordinator jobCoordinator;

    /**
     * scannerConfigService
     */
    private ScannerConfigService scannerDrm;

    /**
     * 任务配置
     */
    private Map<String, ScannerJob> namedJobs;

    /**
     * 任务配置
     */
    private List<ScannerJob> jobs;

    /**
     * trigger
     */
    private ScannerJobTrigger scannerJobTrigger;

    /**
     * init
     */
    public void init() {
        Preconditions.checkArgument(IS_NOT_EMPTY.test(jobs), "empty jobs");
        namedJobs = new HashMap<>();
        jobs.forEach(job -> namedJobs.put(job.getType(), job));
    }

    @Override
    public List<ScannerJobDescriptor> getTriggeredJob(Date triggerTime) {
        Map<String, ScannerJobDescriptor> taskConfigMap = scannerDrm.getTaskConfigMap();
        List<ScannerJobDescriptor> jobs = taskConfigMap.values().stream()
                .filter(descriptor -> scannerJobTrigger.isTriggered(descriptor, triggerTime))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(jobs)) {
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SCHED)
                    .ext("筛选出待执行任务,size:{0}", jobs.size())
                    .build()
                    .info();
        }
        return jobs;
    }

    @Override
    public void addJob(ScannerTaskItem item) {

        ScannerJobDescriptor descriptor = scannerDrm.getScannerJobDescriptor(item.getTaskId());
        QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SCHED)
                .ext("新增任务,descriptor:{0}", descriptor)
                .build()
                .info();
        if (descriptor == null) {
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SCHED)
                    .ext("任务配置不存在,taskItem:{0}", item)
                    .build()
                    .warn();
            return;
        }

        try {
            String taskType = descriptor.getTaskType();
            ScannerJob scannerJob = namedJobs.get(taskType);
            if (scannerJob == null) {
                QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SCHED)
                        .ext("未找到对应处理任务,taskItem:{0}", item)
                        .build()
                        .warn();
                return;
            }

            ScannerJobContext context = ScannerJobContext.builder()
                    .jobDescriptor(descriptor)
                    .scannerTaskItem(item)
                    .build();
            // 线程池异步执行
            jobCoordinator.executor().execute(() -> {
                try {
                    scannerJob.execute(context);
                } catch (Exception e) {
                    QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SCHED)
                            .bizSuccess(false)
                            .ext("任务放入线程池失败,taskItem:{0},descriptor:{1}，exception:{2}", item, descriptor)
                            .throwable(e)
                            .build()
                            .error();
                    throw e;
                }
            });
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SCHED)
                    .ext("任务添加成功,descriptor:{0}", descriptor)
                    .build()
                    .info();
        } catch (Exception e) {
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SCHED)
                    .bizSuccess(false)
                    .ext("任务添加失败,taskItem:{0},descriptor:{1}", item, descriptor)
                    .throwable(e)
                    .build()
                    .error();
        }
    }
}