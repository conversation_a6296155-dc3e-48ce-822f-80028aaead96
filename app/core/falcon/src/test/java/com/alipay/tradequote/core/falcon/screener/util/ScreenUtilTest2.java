/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.util;

import com.alipay.logicview.sdk.model.response.IndicatorViewData;
import com.alipay.logicview.sdk.model.response.IndicatorViewQueryResponse;
import com.alipay.logicview.sdk.model.response.IndicatorViewSeries;
import com.alipay.quot.commons.models.Common;
import com.alipay.quot.commons.models.ScreenerProto;
import com.alipay.quot.commons.models.querycond.PageCondOuterClass;
import com.alipay.quot.commons.models.querycond.SortCondOuterClass;
import com.alipay.quote.nerve.qsql.lib.value.primitive.LongValue;
import com.alipay.quote.nerve.qsql.lib.viewCell.ViewCell;
import com.alipay.quote.nerve.qsql.lib.value.primitive.StringValue;
import com.alipay.quote.nerve.qsql.lib.value.primitive.DoubleValue;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorFrame;
import com.alipay.tradequote.core.falcon.screener.model.template.ScreenObj;
import com.google.common.collect.Sets;
import org.junit.Test;

import java.util.*;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 */
public class ScreenUtilTest2 {

    @Test
    public void testHasIndicators_withNullFilter_returnsFalse() {
        Set<String> indicators = Sets.newHashSet("indicator1", "indicator2");
        assertFalse(ScreenUtil.hasIndicators(null, indicators));
    }

    @Test
    public void testHasIndicators_withNullIndicators_returnsFalse() {
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder().build();
        assertFalse(ScreenUtil.hasIndicators(filter, null));
    }

    @Test
    public void testHasIndicators_withEmptyIndicators_returnsFalse() {
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder().build();
        assertFalse(ScreenUtil.hasIndicators(filter, new HashSet<>()));
    }

    @Test
    public void testHasIndicators_withMatchingIndicator_returnsTrue() {
        Common.VCell operand = Common.VCell.newBuilder().setSv("indicator1").build();
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand)
                .build();
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .build();
        
        Set<String> indicators = Sets.newHashSet("indicator1", "indicator2");
        assertTrue(ScreenUtil.hasIndicators(filter, indicators));
    }

    @Test
    public void testHasIndicators_withNoMatchingIndicator_returnsFalse() {
        Common.VCell operand = Common.VCell.newBuilder().setSv("indicator3").build();
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand)
                .build();
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .build();
        
        Set<String> indicators = Sets.newHashSet("indicator1", "indicator2");
        assertFalse(ScreenUtil.hasIndicators(filter, indicators));
    }

    @Test
    public void testHasIndicators_SortCond() {
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .setSortCond(SortCondOuterClass.SortCond.newBuilder().setField("indicator1").build())
                .build();

        Set<String> indicators = Sets.newHashSet("indicator1", "indicator2");
        assertTrue(ScreenUtil.hasIndicators(filter, indicators));

        filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .setSortCond(SortCondOuterClass.SortCond.newBuilder().setField("indicator3").build())
                .build();
        assertFalse(ScreenUtil.hasIndicators(filter, indicators));
    }

    @Test
    public void testGroupConditionByIndicator_withMixedConditions() {
        Common.VCell operand1 = Common.VCell.newBuilder().setSv("indicator1").build();
        Common.VCell operand2 = Common.VCell.newBuilder().setSv("indicator3").build();
        
        ScreenerProto.ScreenCondition condition1 = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand1)
                .build();
        ScreenerProto.ScreenCondition condition2 = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand2)
                .build();
        
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition1)
                .addConditions(condition2)
                .build();
        
        Set<String> indicators = Sets.newHashSet("indicator1", "indicator2");
        Map<Boolean, List<ScreenerProto.ScreenCondition>> result = 
                ScreenUtil.groupConditionByIndicator(filter, indicators);
        
        assertEquals(1, result.get(true).size());
        assertEquals(1, result.get(false).size());
    }

    @Test
    public void testGetIndicatorsFromCompoundFilter_withConditionsAndSort() {
        Common.VCell operand1 = Common.VCell.newBuilder().setSv("indicator1").build();
        Common.VCell operand2 = Common.VCell.newBuilder().setSv("indicator2").build();
        
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand1)
                .addOperands(operand2)
                .build();
        
        SortCondOuterClass.SortCond sortCond = SortCondOuterClass.SortCond.newBuilder()
                .setField("sortIndicator")
                .build();
        
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .setSortCond(sortCond)
                .build();
        
        Set<String> indicators = ScreenUtil.getIndicatorsFromCompoundFilter(filter);
        
        assertEquals(3, indicators.size());
        assertTrue(indicators.contains("indicator1"));
        assertTrue(indicators.contains("indicator2"));
        assertTrue(indicators.contains("sortIndicator"));
    }

    @Test
    public void testGetIndicatorsFromCompoundFilter_withEmptySortField() {
        Common.VCell operand = Common.VCell.newBuilder().setSv("indicator1").build();
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand)
                .build();
        
        SortCondOuterClass.SortCond sortCond = SortCondOuterClass.SortCond.newBuilder()
                .setField("")
                .build();
        
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .setSortCond(sortCond)
                .build();
        
        Set<String> indicators = ScreenUtil.getIndicatorsFromCompoundFilter(filter);
        
        assertEquals(1, indicators.size());
        assertTrue(indicators.contains("indicator1"));
    }

    @Test
    public void testConvertToScreenObjList_withValidData() {
        ViewCell cell = ViewCell.builder().value(new StringValue("test")).build();

        IndicatorViewSeries series = new IndicatorViewSeries();
        series.setName("indicator1");
        series.setCells(Collections.singletonList(cell));
        
        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSymbol("600519.SH");
        viewData.setName("TestStock");
        viewData.setSeries(Collections.singletonList(series));
        
        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Collections.singletonList(viewData));
        
        List<ScreenObj> result = ScreenUtil.convertToScreenObjList(response, true);
        
        assertEquals(1, result.size());
        assertEquals("600519.SH", result.get(0).getObjId());
        assertTrue(result.get(0).isFilterFromZSearch());
    }

    @Test
    public void testConvertToScreenObjList_withNullData() {
        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Collections.singletonList((IndicatorViewData) null));
        
        List<ScreenObj> result = ScreenUtil.convertToScreenObjList(response, false);
        
        assertEquals(0, result.size());
    }

    @Test
    public void testConvertToScreenObj_withValidData() {
        ViewCell cell = ViewCell.builder().value(new DoubleValue(123.45)).build();

        IndicatorViewSeries series = new IndicatorViewSeries();
        series.setName("PE_TTM");
        series.setCells(Collections.singletonList(cell));
        
        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSymbol("000001.SZ");
        viewData.setName("TestStock");
        viewData.setSeries(Collections.singletonList(series));
        
        ScreenObj result = ScreenUtil.convertToScreenObj(viewData, false);
        
        assertNotNull(result);
        assertEquals("000001.SZ", result.getObjId());
        assertEquals("TestStock", result.getName());
        assertFalse(result.isFilterFromZSearch());
        assertNotNull(result.getFrame());
    }

    @Test
    public void testConvertToSectionFrame_withValidSeries() {
        ViewCell cell1 = ViewCell.builder().value(new StringValue("test1")).build();
        ViewCell cell2 = ViewCell.builder().value(new LongValue(100L)).build();

        IndicatorViewSeries series1 = new IndicatorViewSeries();
        series1.setName("indicator1");
        series1.setCells(Collections.singletonList(cell1));
        
        IndicatorViewSeries series2 = new IndicatorViewSeries();
        series2.setName("indicator2");
        series2.setCells(Collections.singletonList(cell2));
        
        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSeries(Arrays.asList(series1, series2));
        
        IndicatorFrame result = ScreenUtil.convertToSectionFrame(viewData);
        
        assertNotNull(result);
        assertEquals(2, result.getData().size());
        assertEquals("test1", result.getData().get("indicator1").getValue());
        assertEquals(100L, result.getData().get("indicator2").getValue());
    }

    @Test
    public void testConvertToSectionFrame_withEmptyCells() {
        IndicatorViewSeries series = new IndicatorViewSeries();
        series.setName("indicator1");
        series.setCells(new ArrayList<>());
        
        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSeries(Collections.singletonList(series));
        
        IndicatorFrame result = ScreenUtil.convertToSectionFrame(viewData);
        
        assertNotNull(result);
        assertEquals(0, result.getData().size());
    }

    @Test
    public void testRearrangeFilters_withNullFilters_returnsEmpty() {
        Set<String> zsearchIndicators = Sets.newHashSet("indicator1");
        List<ScreenerProto.ScreenCompoundFilter> result = 
                ScreenUtil.rearrangeFilters(null, zsearchIndicators);
        
        assertTrue(result.isEmpty());
    }

    @Test
    public void testRearrangeFilters_withEmptyZsearchIndicators_returnsOriginal() {
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder().build();
        List<ScreenerProto.ScreenCompoundFilter> filters = Collections.singletonList(filter);
        
        List<ScreenerProto.ScreenCompoundFilter> result = 
                ScreenUtil.rearrangeFilters(filters, new HashSet<>());
        
        assertEquals(filters, result);
    }

    @Test
    public void testRearrangeFilters_withZsearchIndicators() {
        Common.VCell operand1 = Common.VCell.newBuilder().setSv("zsearch_indicator").build();
        Common.VCell operand2 = Common.VCell.newBuilder().setSv("normal_indicator").build();
        
        ScreenerProto.ScreenCondition condition1 = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand1)
                .build();
        ScreenerProto.ScreenCondition condition2 = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand2)
                .build();
        
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition1)
                .addConditions(condition2)
                .build();
        
        Set<String> zsearchIndicators = Sets.newHashSet("zsearch_indicator");
        List<ScreenerProto.ScreenCompoundFilter> result = 
                ScreenUtil.rearrangeFilters(Collections.singletonList(filter), zsearchIndicators);
        
        assertEquals(2, result.size());
    }

    @Test
    public void testHasZSearchIndicator_withNullZsearchIndicators_returnsFalse() {
        ScreenerProto.ScreenCompoundFilterRequest request = 
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder().build();
        
        assertFalse(ScreenUtil.hasZSearchIndicator(request, null));
    }

    @Test
    public void testHasZSearchIndicator_withEmptyZsearchIndicators_returnsFalse() {
        ScreenerProto.ScreenCompoundFilterRequest request = 
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder().build();
        
        assertFalse(ScreenUtil.hasZSearchIndicator(request, new HashSet<>()));
    }

    @Test
    public void testHasZSearchIndicator_withMatchingFilterAndSort_returnsTrue() {
        Common.VCell operand = Common.VCell.newBuilder().setSv("zsearch_indicator").build();
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand)
                .build();
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .build();
        
        SortCondOuterClass.SortCond sortCond = SortCondOuterClass.SortCond.newBuilder()
                .setField("zsearch_sort")
                .build();
        
        ScreenerProto.ScreenCompoundFilterRequest request = 
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder()
                        .addFilters(filter)
                        .addSortConds(sortCond)
                        .build();
        
        Set<String> zsearchIndicators = Sets.newHashSet("zsearch_indicator", "zsearch_sort");
        assertTrue(ScreenUtil.hasZSearchIndicator(request, zsearchIndicators));
    }

    @Test
    public void testHasZSearchIndicator_withOnlyMatchingFilter_returnsFalse() {
        Common.VCell operand = Common.VCell.newBuilder().setSv("zsearch_indicator").build();
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand)
                .build();
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .build();
        
        ScreenerProto.ScreenCompoundFilterRequest request = 
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder()
                        .addFilters(filter)
                        .build();
        
        Set<String> zsearchIndicators = Sets.newHashSet("zsearch_indicator");
        assertFalse(ScreenUtil.hasZSearchIndicator(request, zsearchIndicators));
    }

    @Test
    public void testGetRequestIndicators_withAllTypes() {
        Common.VCell operand = Common.VCell.newBuilder().setSv("filter_indicator").build();
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand)
                .build();
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .build();
        
        SortCondOuterClass.SortCond sortCond = SortCondOuterClass.SortCond.newBuilder()
                .setField("sort_indicator")
                .build();
        
        ScreenerProto.ScreenCompoundFilterRequest request = 
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder()
                        .addFilters(filter)
                        .addSortConds(sortCond)
                        .addIndicators("request_indicator")
                        .build();
        
        Set<String> indicators = ScreenUtil.getRequestIndicators(request);
        
        assertEquals(3, indicators.size());
        assertTrue(indicators.contains("filter_indicator"));
        assertTrue(indicators.contains("sort_indicator"));
        assertTrue(indicators.contains("request_indicator"));
    }

    @Test
    public void testGetRequestIndicators_withEmptyRequest() {
        ScreenerProto.ScreenCompoundFilterRequest request =
                ScreenerProto.ScreenCompoundFilterRequest.newBuilder().build();

        Set<String> indicators = ScreenUtil.getRequestIndicators(request);

        assertTrue(indicators.isEmpty());
    }

    // ========== convertToScreenObj 边界情况和异常测试 ==========

    @Test
    public void testConvertToScreenObj_withNullViewData_returnsNull() {
        ScreenObj result = ScreenUtil.convertToScreenObj(null, false);
        assertNull(result);
    }

    @Test
    public void testConvertToScreenObj_withNullSymbol_handlesGracefully() {
        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSymbol(null);
        viewData.setName("TestStock");
        viewData.setSeries(new ArrayList<>());

        ScreenObj result = ScreenUtil.convertToScreenObj(viewData, true);

        // 应该能处理null symbol的情况
        if (result != null) {
            assertNull(result.getObjId());
            assertTrue(result.isFilterFromZSearch());
        }
    }

    @Test
    public void testConvertToScreenObj_withNullName_handlesGracefully() {
        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSymbol("600519.SH");
        viewData.setName(null);
        viewData.setSeries(new ArrayList<>());

        ScreenObj result = ScreenUtil.convertToScreenObj(viewData, false);

        if (result != null) {
            assertEquals("600519.SH", result.getObjId());
            assertNull(result.getName());
            assertFalse(result.isFilterFromZSearch());
        }
    }

    @Test
    public void testConvertToScreenObj_whenConvertToSectionFrameReturnsNull_returnsNull() {
        // 创建一个会导致convertToSectionFrame返回null的viewData
        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSymbol("600519.SH");
        viewData.setName("TestStock");
        viewData.setSeries(null); // 这可能导致convertToSectionFrame返回null

        ScreenObj result = ScreenUtil.convertToScreenObj(viewData, true);

        assertNull(result);
    }

    // ========== convertToSectionFrame 边界情况和异常测试 ==========

    @Test
    public void testConvertToSectionFrame_withNullViewData_returnsNull() {
        IndicatorFrame result = ScreenUtil.convertToSectionFrame(null);
        assertNull(result);
    }

    @Test
    public void testConvertToSectionFrame_withNullSeries_returnsNull() {
        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSeries(null);

        IndicatorFrame result = ScreenUtil.convertToSectionFrame(viewData);
        assertNull(result);
    }

    @Test
    public void testConvertToSectionFrame_withEmptySeries_returnsEmptyFrame() {
        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSeries(new ArrayList<>());

        IndicatorFrame result = ScreenUtil.convertToSectionFrame(viewData);

        assertNotNull(result);
        assertTrue(result.getData().isEmpty());
    }

    @Test
    public void testConvertToSectionFrame_withNullSeriesName_skipsIndicator() {
        IndicatorViewSeries series = new IndicatorViewSeries();
        series.setName(null);
        series.setCells(Collections.singletonList(
                ViewCell.builder().value(new StringValue("test")).build()));

        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSeries(Collections.singletonList(series));

        IndicatorFrame result = ScreenUtil.convertToSectionFrame(viewData);

        assertNotNull(result);
        // 由于name为null，应该不会添加到indicators中
        assertTrue(result.getData().isEmpty() || result.getData().containsKey(null));
    }

    @Test
    public void testConvertToSectionFrame_withNullCells_skipsIndicator() {
        IndicatorViewSeries series = new IndicatorViewSeries();
        series.setName("indicator1");
        series.setCells(null);

        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSeries(Collections.singletonList(series));

        IndicatorFrame result = ScreenUtil.convertToSectionFrame(viewData);

        assertNull(result);
    }

    @Test
    public void testConvertToSectionFrame_withNullCellValue_skipsIndicator() {
        ViewCell cellWithNullValue = ViewCell.builder().value(null).build();

        IndicatorViewSeries series = new IndicatorViewSeries();
        series.setName("indicator1");
        series.setCells(Collections.singletonList(cellWithNullValue));

        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSeries(Collections.singletonList(series));

        IndicatorFrame result = ScreenUtil.convertToSectionFrame(viewData);

        assertNotNull(result);
        assertEquals(0, result.getData().size());
    }

    @Test
    public void testConvertToSectionFrame_withMultipleSeriesAndMixedData() {
        ViewCell validCell = ViewCell.builder().value(new StringValue("valid")).build();
        ViewCell nullValueCell = ViewCell.builder().value(null).build();

        IndicatorViewSeries series1 = new IndicatorViewSeries();
        series1.setName("valid_indicator");
        series1.setCells(Collections.singletonList(validCell));

        IndicatorViewSeries series2 = new IndicatorViewSeries();
        series2.setName("null_value_indicator");
        series2.setCells(Collections.singletonList(nullValueCell));

        IndicatorViewSeries series3 = new IndicatorViewSeries();
        series3.setName(null);
        series3.setCells(Collections.singletonList(validCell));

        IndicatorViewData viewData = new IndicatorViewData();
        viewData.setSeries(Arrays.asList(series1, series2, series3));

        IndicatorFrame result = ScreenUtil.convertToSectionFrame(viewData);

        assertNotNull(result);
        assertEquals(1, result.getData().size()); // 只有valid_indicator应该被添加
        assertTrue(result.getData().containsKey("valid_indicator"));
        assertEquals("valid", result.getData().get("valid_indicator").getValue());
    }

    // ========== rearrangeFilters 边界情况和异常测试 ==========

    @Test
    public void testRearrangeFilters_withFilterHavingNoConditions_addsDirectly() {
        ScreenerProto.ScreenCompoundFilter emptyFilter = ScreenerProto.ScreenCompoundFilter.newBuilder().build();
        List<ScreenerProto.ScreenCompoundFilter> filters = Collections.singletonList(emptyFilter);
        Set<String> zsearchIndicators = Sets.newHashSet("zsearch_indicator");

        List<ScreenerProto.ScreenCompoundFilter> result =
                ScreenUtil.rearrangeFilters(filters, zsearchIndicators);

        assertEquals(1, result.size());
        assertEquals(emptyFilter, result.get(0));
    }

    @Test
    public void testRearrangeFilters_withFilterHavingOnlyZsearchConditions() {
        Common.VCell operand = Common.VCell.newBuilder().setSv("zsearch_indicator").build();
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand)
                .build();
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .build();

        Set<String> zsearchIndicators = Sets.newHashSet("zsearch_indicator");
        List<ScreenerProto.ScreenCompoundFilter> result =
                ScreenUtil.rearrangeFilters(Collections.singletonList(filter), zsearchIndicators);

        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getConditionsCount());
        assertEquals("zsearch_indicator", result.get(0).getConditions(0).getOperands(0).getSv());
    }

    @Test
    public void testRearrangeFilters_withFilterHavingOnlyNonZsearchConditions() {
        Common.VCell operand = Common.VCell.newBuilder().setSv("normal_indicator").build();
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand)
                .build();
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .build();

        Set<String> zsearchIndicators = Sets.newHashSet("zsearch_indicator");
        List<ScreenerProto.ScreenCompoundFilter> result =
                ScreenUtil.rearrangeFilters(Collections.singletonList(filter), zsearchIndicators);

        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getConditionsCount());
        assertEquals("normal_indicator", result.get(0).getConditions(0).getOperands(0).getSv());
    }

    @Test
    public void testRearrangeFilters_withMultipleFiltersAndComplexScenario() {
        // 第一个filter：只有zsearch条件
        Common.VCell zsearchOperand = Common.VCell.newBuilder().setSv("zsearch_indicator").build();
        ScreenerProto.ScreenCondition zsearchCondition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(zsearchOperand)
                .build();
        ScreenerProto.ScreenCompoundFilter zsearchOnlyFilter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(zsearchCondition)
                .build();

        // 第二个filter：只有普通条件
        Common.VCell normalOperand = Common.VCell.newBuilder().setSv("normal_indicator").build();
        ScreenerProto.ScreenCondition normalCondition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(normalOperand)
                .build();
        ScreenerProto.ScreenCompoundFilter normalOnlyFilter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(normalCondition)
                .build();

        // 第三个filter：混合条件
        ScreenerProto.ScreenCompoundFilter mixedFilter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(zsearchCondition)
                .addConditions(normalCondition)
                .build();

        List<ScreenerProto.ScreenCompoundFilter> filters = Arrays.asList(
                zsearchOnlyFilter, normalOnlyFilter, mixedFilter);
        Set<String> zsearchIndicators = Sets.newHashSet("zsearch_indicator");

        List<ScreenerProto.ScreenCompoundFilter> result =
                ScreenUtil.rearrangeFilters(filters, zsearchIndicators);

        // 应该有4个filter：zsearchOnly, normalOnly, mixed拆分成的zsearch部分, mixed拆分成的normal部分
        assertEquals(4, result.size());
    }

    @Test
    public void testRearrangeFilters_withEmptyConditionsAfterGrouping_skipsEmptyFilters() {
        // 创建一个条件，但操作数为空
        ScreenerProto.ScreenCondition emptyCondition = ScreenerProto.ScreenCondition.newBuilder().build();
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(emptyCondition)
                .build();

        Set<String> zsearchIndicators = Sets.newHashSet("zsearch_indicator");
        List<ScreenerProto.ScreenCompoundFilter> result =
                ScreenUtil.rearrangeFilters(Collections.singletonList(filter), zsearchIndicators);

        // 由于条件没有操作数，不会匹配任何指标，应该直接添加原filter
        assertEquals(1, result.size());
        assertEquals(filter, result.get(0));
    }

    @Test
    public void testRearrangeFilters_preservesOtherFilterProperties() {
        Common.VCell zsearchOperand = Common.VCell.newBuilder().setSv("zsearch_indicator").build();
        Common.VCell normalOperand = Common.VCell.newBuilder().setSv("normal_indicator").build();

        ScreenerProto.ScreenCondition zsearchCondition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(zsearchOperand)
                .build();
        ScreenerProto.ScreenCondition normalCondition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(normalOperand)
                .build();

        SortCondOuterClass.SortCond sortCond = SortCondOuterClass.SortCond.newBuilder()
                .setField("sort_field")
                .build();

        ScreenerProto.ScreenCompoundFilter originalFilter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(zsearchCondition)
                .addConditions(normalCondition)
                .setSortCond(sortCond)
                .build();

        Set<String> zsearchIndicators = Sets.newHashSet("zsearch_indicator");
        List<ScreenerProto.ScreenCompoundFilter> result =
                ScreenUtil.rearrangeFilters(Collections.singletonList(originalFilter), zsearchIndicators);

        assertEquals(2, result.size());

        // 检查两个拆分后的filter都保留了sortCond
        for (ScreenerProto.ScreenCompoundFilter filter : result) {
            assertTrue(filter.hasSortCond());
            assertEquals("sort_field", filter.getSortCond().getField());
        }
    }

    // ========== rearrangeFilters 关键Bug测试和边界情况补充 ==========

    @Test
    public void testRearrangeFilters_withEmptyConditionsFilter_shouldNotDuplicate() {
        // 测试空条件filter是否会被重复添加（这是一个重要的bug测试）
        ScreenerProto.ScreenCompoundFilter emptyFilter = ScreenerProto.ScreenCompoundFilter.newBuilder().build();
        List<ScreenerProto.ScreenCompoundFilter> filters = Collections.singletonList(emptyFilter);
        Set<String> zsearchIndicators = Sets.newHashSet("zsearch_indicator");

        List<ScreenerProto.ScreenCompoundFilter> result =
                ScreenUtil.rearrangeFilters(filters, zsearchIndicators);

        // 关键测试：空条件filter应该只出现一次，不应该重复
        assertEquals("Empty condition filter should appear only once", 1, result.size());
        assertEquals(emptyFilter, result.get(0));
    }

    @Test
    public void testRearrangeFilters_withNullZsearchIndicators_returnsOriginal() {
        // 测试zsearchIndicators为null的情况
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder().build();
        List<ScreenerProto.ScreenCompoundFilter> filters = Collections.singletonList(filter);

        List<ScreenerProto.ScreenCompoundFilter> result =
                ScreenUtil.rearrangeFilters(filters, null);

        assertEquals(filters, result);
        assertSame("Should return the same list object", filters, result);
    }

    @Test
    public void testRearrangeFilters_withEmptyFilters_returnsEmpty() {
        // 测试空filters列表
        List<ScreenerProto.ScreenCompoundFilter> emptyFilters = Collections.emptyList();
        Set<String> zsearchIndicators = Sets.newHashSet("indicator1");

        List<ScreenerProto.ScreenCompoundFilter> result =
                ScreenUtil.rearrangeFilters(emptyFilters, zsearchIndicators);

        assertTrue(result.isEmpty());
    }

    @Test
    public void testRearrangeFilters_withMixedEmptyAndNonEmptyFilters() {
        // 测试混合空条件和非空条件的filters
        ScreenerProto.ScreenCompoundFilter emptyFilter = ScreenerProto.ScreenCompoundFilter.newBuilder().build();

        Common.VCell operand = Common.VCell.newBuilder().setSv("normal_indicator").build();
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand)
                .build();
        ScreenerProto.ScreenCompoundFilter nonEmptyFilter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .build();

        List<ScreenerProto.ScreenCompoundFilter> filters = Arrays.asList(emptyFilter, nonEmptyFilter);
        Set<String> zsearchIndicators = Sets.newHashSet("zsearch_indicator");

        List<ScreenerProto.ScreenCompoundFilter> result =
                ScreenUtil.rearrangeFilters(filters, zsearchIndicators);

        // 应该有2个filter：空filter + 非空filter（因为非空filter不包含zsearch指标）
        assertEquals(2, result.size());

        // 验证空filter只出现一次
        long emptyFilterCount = result.stream()
                .filter(f -> f.getConditionsCount() == 0)
                .count();
        assertEquals("Empty filter should appear only once", 1, emptyFilterCount);
    }

    @Test
    public void testRearrangeFilters_withFilterContainingOnlyEmptyConditions() {
        // 测试包含空条件（没有操作数）的filter
        ScreenerProto.ScreenCondition emptyCondition1 = ScreenerProto.ScreenCondition.newBuilder().build();
        ScreenerProto.ScreenCondition emptyCondition2 = ScreenerProto.ScreenCondition.newBuilder().build();

        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(emptyCondition1)
                .addConditions(emptyCondition2)
                .build();

        Set<String> zsearchIndicators = Sets.newHashSet("zsearch_indicator");
        List<ScreenerProto.ScreenCompoundFilter> result =
                ScreenUtil.rearrangeFilters(Collections.singletonList(filter), zsearchIndicators);

        // 由于条件没有操作数，hasIndicators应该返回false，filter应该被直接添加
        assertEquals(1, result.size());
        assertEquals(filter, result.get(0));
    }

    @Test
    public void testRearrangeFilters_preservesPageCondition() {
        // 测试PageCond是否被正确保留
        Common.VCell zsearchOperand = Common.VCell.newBuilder().setSv("zsearch_indicator").build();
        Common.VCell normalOperand = Common.VCell.newBuilder().setSv("normal_indicator").build();

        ScreenerProto.ScreenCondition zsearchCondition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(zsearchOperand)
                .build();
        ScreenerProto.ScreenCondition normalCondition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(normalOperand)
                .build();

        PageCondOuterClass.PageCond pageCond = PageCondOuterClass.PageCond.newBuilder()
                .setLimit(100)
                .setOffset(10)
                .build();

        ScreenerProto.ScreenCompoundFilter originalFilter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(zsearchCondition)
                .addConditions(normalCondition)
                .setPageCond(pageCond)
                .build();

        Set<String> zsearchIndicators = Sets.newHashSet("zsearch_indicator");
        List<ScreenerProto.ScreenCompoundFilter> result =
                ScreenUtil.rearrangeFilters(Collections.singletonList(originalFilter), zsearchIndicators);

        assertEquals(2, result.size());

        // 检查两个拆分后的filter都保留了pageCond
        for (ScreenerProto.ScreenCompoundFilter filter : result) {
            assertTrue("PageCond should be preserved", filter.hasPageCond());
            assertEquals(100, filter.getPageCond().getLimit());
            assertEquals(10, filter.getPageCond().getOffset());
        }
    }

    @Test
    public void testRearrangeFilters_withLargeNumberOfFilters() {
        // 测试大量filters的性能和正确性
        List<ScreenerProto.ScreenCompoundFilter> filters = new ArrayList<>();
        Set<String> zsearchIndicators = Sets.newHashSet("zsearch_indicator");

        // 创建100个不同类型的filters
        for (int i = 0; i < 100; i++) {
            if (i % 3 == 0) {
                // 空filter
                filters.add(ScreenerProto.ScreenCompoundFilter.newBuilder().build());
            } else if (i % 3 == 1) {
                // 只有zsearch条件的filter
                Common.VCell operand = Common.VCell.newBuilder().setSv("zsearch_indicator").build();
                ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                        .addOperands(operand)
                        .build();
                filters.add(ScreenerProto.ScreenCompoundFilter.newBuilder()
                        .addConditions(condition)
                        .build());
            } else {
                // 只有普通条件的filter
                Common.VCell operand = Common.VCell.newBuilder().setSv("normal_indicator").build();
                ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                        .addOperands(operand)
                        .build();
                filters.add(ScreenerProto.ScreenCompoundFilter.newBuilder()
                        .addConditions(condition)
                        .build());
            }
        }

        List<ScreenerProto.ScreenCompoundFilter> result =
                ScreenUtil.rearrangeFilters(filters, zsearchIndicators);

        // 验证结果数量正确（空filter和普通filter各34个，zsearch filter 33个，总共101个）
        assertEquals(100, result.size());

        // 验证没有重复的空filter
        long emptyFilterCount = result.stream()
                .filter(f -> f.getConditionsCount() == 0)
                .count();
        assertEquals("Empty filters should not be duplicated", 34, emptyFilterCount);
    }
}
