/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.service.job;

import com.alipay.quot.commons.facade.result.Result;
import com.alipay.quot.commons.facade.result.Status;
import com.alipay.quote.log.logger.QuoteLog;
import com.alipay.screenerquote.prod.model.ScreenerStrategyRequestDTO;
import com.alipay.screenerquote.prod.model.ScreenerStrategyResultDTO;
import com.alipay.screenerquote.scannerprod.core.scanner.model.*;
import com.alipay.screenerquote.scannerprod.core.scanner.scheduler.model.ScannerTaskItem;
import com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerJob;
import com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerWriteService;
import com.alipay.screenerquote.scannerprod.integration.screenerprod.ScreenerStrategyQueryClient;
import com.alipay.screenerquote.scannerprod.log.DedicateLog;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL1_SCANNER;
import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL2_SCHED;

/**
 * <AUTHOR>
 * @version StrategySeriesJob.java, v 0.1 2025年02月24日 15:12 lzt
 */
@Setter
public class ScannerStrategyJob implements ScannerJob {

    private static final Logger LOGGER = DedicateLog.PROD_SCHED_LOGGER;

    /**
     * 策略写入服务
     */
    private ScannerWriteService scannerWriteService;

    /**
     * 策略结果查询
     */
    private ScreenerStrategyQueryClient screenerStrategyQueryClient;

    /**
     * 预期延迟时间，20分钟
     */
    public static final long EXPECTED_LATENCY_TIME = 1000L * 60 * 20;

    @Override
    public String getType() {
        return "SCANNER_STRATEGY_JOB";
    }

    @Override
    public void execute(ScannerJobContext context) {

        long start = System.currentTimeMillis();
        ScannerJobDescriptor jobDescriptor = context.getJobDescriptor();
        ScannerTaskItem scannerTaskItem = context.getScannerTaskItem();
        Date expectedTriggerTime = scannerTaskItem.getExpectedTriggerTime();
        long triggerTimeStamp = expectedTriggerTime.getTime();

        if (start - triggerTimeStamp > EXPECTED_LATENCY_TIME) {
            //延迟超过预期,打印日志
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SCHED)
                    .ext("任务延迟超过预期,type:{0},taskId:{1},context:{2}", getType(), jobDescriptor.getTaskId(), context)
                    .build()
                    .warn();
        }

        String strategyId = jobDescriptor.getStrategyId();
        String srcId = jobDescriptor.getSrcId();
        ScreenerStrategyRequestDTO request = buildRequest(strategyId, srcId);

        try {
            Result<ScreenerStrategyResultDTO> result = screenerStrategyQueryClient.queryByStrategy(request);
            long snapshotDate = System.currentTimeMillis();
            if (!result.isSuccess()) {
                if (result.getStatus().equals(Status.BAD_REQUEST)) {
                    throw new IllegalArgumentException("查询参数有误");
                } else {
                    throw new IllegalStateException("查询异常");
                }
            }

            ScreenerStrategyResultDTO data = result.getData();
            ScannerPortfolio portfolioResult = ScannerPortfolio.builder()
                    .beginDate(getBeginDate(snapshotDate, jobDescriptor.getPeriod()))
                    .bizDate(snapshotDate)
                    .snapshotDate(snapshotDate)
                    .strategyId(strategyId)
                    .srcId(srcId)
                    .objs(data.getObjs())
                    .count(data.getCount())
                    .objCount(data.getObjCount())
                    .build();

            if (CollectionUtils.isEmpty(jobDescriptor.getWriteConfigs())) {
                //如果未传入写入配置，默认写入zsearch
                scannerWriteService.writePortfolio(portfolioResult);
            } else {
                for (ScannerWriteConfig writeConfig : jobDescriptor.getWriteConfigs()) {
                    scannerWriteService.write(writeConfig, portfolioResult);
                }
            }


        } catch (Exception e) {
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SCHED)
                    .ext("job execute failed,type:{0},taskId:{1}", getType(), jobDescriptor.getTaskId())
                    .bizSuccess(false)
                    .throwable(e)
                    .build()
                    .error();
            throw e;
        }

        QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SCHED)
                .ext("job execute success,type:{0},taskId:{1}", getType(), jobDescriptor.getTaskId())
                .bizUseTime(System.currentTimeMillis() - start)
                .bizSuccess(true)
                .build()
                .info();
    }

    /**
     * 构建查询请求
     *
     * @param strategyId
     * @param srcId
     * @return
     */
    private ScreenerStrategyRequestDTO buildRequest(String strategyId, String srcId) {
        return ScreenerStrategyRequestDTO.builder()
                .strategyId(strategyId)
                .sceneId("SCANNER")
                .srcId(srcId)
                .build();
    }

    /**
     * 获取归档时间
     *
     * @return
     */
    public long getBeginDate(long timestamp, ScannerPeriodEnum period) {

        LocalDateTime time = Instant.ofEpochMilli(timestamp)
                .atZone(ZoneId.systemDefault()) // 指定系统默认时区
                .toLocalDateTime();

        LocalDateTime truncatedTime;
        switch (period) {
            case P_Day1: // 日初（当天的 00:00:00）
                truncatedTime = time.toLocalDate().atStartOfDay();
                break;
            case P_Min1: // 分钟初（当前分钟的 00 秒）
                truncatedTime = time.withSecond(0).withNano(0);
                break;
            default:
                throw new IllegalArgumentException("无效的时间精度: " + period);
        }
        return truncatedTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
}