/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener;

import com.alibaba.common.lang.StringUtil;
import com.alipay.quote.nerve.qsql.lib.value.primitive.DoubleValue;
import com.alipay.quote.nerve.qsql.lib.viewCell.ViewCell;
import com.alipay.tradequote.core.falcon.screener.util.ScreenUtil;
import com.alipay.tradequote.core.falcon.util.Validator;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 */
public class ValidatorTest {

    @Test
    public void test1() {

        try {
            Validator.of("abc")
                    .validate(String::isEmpty, "invalid")
                    .get();
        } catch (Exception e) {
            assertTrue(e instanceof IllegalStateException);
            assertEquals(1, e.getSuppressed().length);
        }

        String obj = Validator.of("abc")
                .validate(StringUtil::isNotEmpty, "invalid")
                .validate(StringUtil::isNotBlank, "invalid")
                .get();
        assertEquals("abc", obj);

        try {
            Validator.of("abc")
                    .validate(StringUtil::isNotEmpty, "invalid")
                    .validate(StringUtil::isNotBlank, "invalid")
                    .validate("abcd"::equals, "invalid")
                    .get();
        } catch (Exception e) {
            assertTrue(e instanceof IllegalStateException);
            assertEquals(1, e.getSuppressed().length);
        }
    }

    @Test
    public void test2() {

        String obj = Validator.of("abc")
                .validate(String::length, l -> l == 3, "invalid")
                .get();
        assertEquals("abc", obj);


        try {
            Validator.of("abc")
                    .validate(String::length, l -> l == 3, "invalid")
                    .validate(String::length, l -> l == 2, "invalid")
                    .validate(String::length, l -> l == 1, "invalid")
                    .get();
        } catch (Exception e) {
            assertTrue(e instanceof IllegalStateException);
            assertEquals(2, e.getSuppressed().length);
        }
    }

    @Test
    public void parallelStream() {
        Map<String, ViewCell> map = new HashMap<>();
        for (int i = 0; i < 120; i++) {
            map.put("key" + i, ViewCell.builder().value(DoubleValue.of(1.3d)).snapshotDate(123L).build());
        }

        long start = System.currentTimeMillis();
        for (int i = 0; i < 5000; i++) {
            map.entrySet().stream().collect(Collectors.toMap(
                    entry -> ScreenUtil.getNameWithIndicatorViewName("viewName", entry.getKey()),
                    entry -> entry.getValue().getValue()));
        }
        System.out.println("stream cost:" + (System.currentTimeMillis() - start));

        start = System.currentTimeMillis();
        for (int i = 0; i < 5000; i++) {
            map.entrySet().parallelStream().collect(Collectors.toMap(
                    entry -> ScreenUtil.getNameWithIndicatorViewName("viewName", entry.getKey()),
                    entry -> entry.getValue().getValue()));
        }
        System.out.println("parallel stream cost:" + (System.currentTimeMillis() - start));

        start = System.currentTimeMillis();
        for (int i = 0; i < 5000; i++) {
            map.entrySet().stream().filter(entry -> entry.getKey().endsWith("5")).collect(Collectors.toMap(
                    entry -> ScreenUtil.getNameWithIndicatorViewName("viewName", entry.getKey()),
                    entry -> entry.getValue().getValue()));
        }
        System.out.println("filter stream cost:" + (System.currentTimeMillis() - start));

        start = System.currentTimeMillis();
        for (int i = 0; i < 5000; i++) {
            map.entrySet().parallelStream().filter(entry -> entry.getKey().endsWith("5")).collect(Collectors.toMap(
                    entry -> ScreenUtil.getNameWithIndicatorViewName("viewName", entry.getKey()),
                    entry -> entry.getValue().getValue()));
        }
        System.out.println("filter parallel stream cost:" + (System.currentTimeMillis() - start));
    }
}
