/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.sched;

import com.alipay.quot.commons.models.BaseIndicator;
import com.alipay.quot.commons.models.Symbol;
import com.alipay.tradequote.core.falcon.screener.AbstractScreenerTest;
import com.alipay.tradequote.core.falcon.screener.indicator.IndicatorFrameService;
import com.alipay.tradequote.core.falcon.screener.indicator.IndicatorFrameServiceImpl;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ScreenCacheImpl;
import com.alipay.tradequote.core.falcon.screener.model.DataType;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorDescriptor;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorFrame;
import com.alipay.tradequote.core.falcon.screener.model.JobDescriptor;
import com.alipay.tradequote.core.falcon.screener.sched.job.DsBaseIndicatorJob;
import com.alipay.tradequote.core.falcon.service.indicator.FalconIndicatorService;
import com.alipay.tradequote.meta.service.SymbolGqlService;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
public class DsBaseSnapshotJobTest extends AbstractScreenerTest {
    @InjectMocks
    DsBaseIndicatorJob dsBaseIndicatorJob;

    @Mock
    SymbolGqlService symbolGqlService;

    @Mock
    FalconIndicatorService falconIndicatorService;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        dsBaseIndicatorJob = new DsBaseIndicatorJob();
        dsBaseIndicatorJob.setIndicatorService(falconIndicatorService);
        dsBaseIndicatorJob.setSymbolGqlService(symbolGqlService);
    }

    @Test
    public void test1() {
        assertEquals("DS_BASE_INDICATOR_JOB", dsBaseIndicatorJob.getName());
    }

    @Test
    public void test2() throws Exception {
        List<String> symbols = Lists.newArrayList("600519.SH",
                "000002.SZ",
                "601318.SH");
        String gql = "gql=" + String.join(",", symbols);
        JobDescriptor jobDsp = new JobDescriptor();
        jobDsp.setName("DS_BASE_INDICATOR_JOB");
        jobDsp.setGql(gql);
        ScreenJobContext context = ScreenJobContext.builder()
                .jobDescriptor(jobDsp)
                .listTypes(listTypes())
                .build();

        IndicatorFrameService frameService = Mockito.mock(IndicatorFrameService.class);
        dsBaseIndicatorJob.setIndicatorFrameService(frameService);

        List<Symbol.SymbolDO> objSymbols = makeObjSymbols(symbols);
        doReturn(objSymbols).when(symbolGqlService).querySymbol(gql);
        doReturn(null).when(falconIndicatorService).mgetLatestIndicators(anyList());

        dsBaseIndicatorJob.execute(context);

        verify(symbolGqlService, Mockito.times(1)).querySymbol(anyString());
        verify(symbolGqlService, Mockito.times(1)).querySymbol(gql);
        verify(falconIndicatorService, Mockito.times(3)).mgetLatestIndicators(anyList());
        verify(frameService, Mockito.times(0)).putFrame(any(), anyString(), any());
    }

    @Test
    public void test3() throws Exception {
        List<String> symbols = Lists.newArrayList("600519.SH",
                "000002.SZ",
                "601318.SH");
        List<Symbol.SymbolDO> objSymbols = makeObjSymbols(symbols);
        String gql = "gql=" + String.join(",", symbols);
        JobDescriptor jobDsp = new JobDescriptor();
        jobDsp.setName(DataType.DS_BASE_INDICATOR);
        jobDsp.setGql(gql);
        IndicatorDescriptor i1 = new IndicatorDescriptor();
        i1.setName("PE_TTM");
        i1.setField("peRatioTTM");
        IndicatorDescriptor i2 = new IndicatorDescriptor();
        i2.setName("MKT_CAP");
        i2.setField("marketCapTotal");

        jobDsp.setIndicators(Lists.newArrayList(i1, i2));
        ScreenJobContext context = ScreenJobContext.builder()
                .jobDescriptor(jobDsp)
                .listTypes(listTypes())
                .build();

        ScreenCacheImpl screenCache = new ScreenCacheImpl();
        screenCache.init();
        IndicatorFrameServiceImpl indicatorFrameService = new IndicatorFrameServiceImpl();
        indicatorFrameService.setScreenCache(screenCache);
        dsBaseIndicatorJob.setIndicatorFrameService(indicatorFrameService);

        doReturn(objSymbols).when(symbolGqlService).querySymbol(gql);
        doReturn(Lists.newArrayList(BaseIndicator.BaseIndicatorDO.newBuilder()
                .setPeRatioTTM(33)
                .setMarketCapTotal(1000000d)
                .build()))
                .when(falconIndicatorService)
                .mgetLatestIndicators(argThat(new SymbolMatcher(objSymbols.get(0))));
        doReturn(Lists.newArrayList(BaseIndicator.BaseIndicatorDO.newBuilder()
                .setPeRatioTTM(33)
                .setMarketCapTotal(1000000d)
                .build()))
                .when(falconIndicatorService)
                .mgetLatestIndicators(argThat(new SymbolMatcher(objSymbols.get(1))));
        doThrow(new RuntimeException())
                .when(falconIndicatorService)
                .mgetLatestIndicators(argThat(new SymbolMatcher(objSymbols.get(2))));

        dsBaseIndicatorJob.execute(context);

        verify(symbolGqlService, Mockito.times(1)).querySymbol(anyString());
        verify(symbolGqlService, Mockito.times(1)).querySymbol(gql);
        verify(falconIndicatorService, Mockito.times(3)).mgetLatestIndicators(anyList());

        IndicatorFrame frame = indicatorFrameService.getFrame(DataType.DS_BASE_INDICATOR, objSymbols.get(0).getAliSymbol());
        assertNotNull(frame);
        assertTrue(frame.getData().containsKey("PE_TTM"));
        assertTrue(frame.getData().containsKey("MKT_CAP"));
        assertEquals(33, frame.getData().get("PE_TTM").asDouble(), 0.001d);
        assertEquals(1000000d, frame.getData().get("MKT_CAP").asDouble(), 0.001d);

        indicatorFrameService.getFrame(DataType.DS_BASE_INDICATOR, objSymbols.get(1).getAliSymbol());
        assertNotNull(frame);
        assertTrue(frame.getData().containsKey("PE_TTM"));
        assertTrue(frame.getData().containsKey("MKT_CAP"));
        assertEquals(33, frame.getData().get("PE_TTM").asDouble(), 0.001d);
        assertEquals(1000000d, frame.getData().get("MKT_CAP").asDouble(), 0.001d);

    }

    @Test
    public void test4() throws Exception {
        List<String> symbols = Lists.newArrayList("600519.SH",
                "000002.SZ",
                "601318.SH");
        String gql = "gql=" + String.join(",", symbols);
        JobDescriptor jobDsp = new JobDescriptor();
        jobDsp.setName("DS_BASE_INDICATOR_JOB");
        jobDsp.setGql(gql);
        ScreenJobContext context = ScreenJobContext.builder()
                .jobDescriptor(jobDsp)
                .listTypes(listTypes())
                .build();

        IndicatorFrameService frameService = Mockito.mock(IndicatorFrameService.class);
        dsBaseIndicatorJob.setIndicatorFrameService(frameService);

        List<Symbol.SymbolDO> objSymbols = makeObjSymbols(symbols);
        doReturn(objSymbols).when(symbolGqlService).querySymbol(gql);
        doReturn(Lists.newArrayList(BaseIndicator.BaseIndicatorDO.newBuilder()
                .setPeRatioTTM(33)
                .setMarketCapTotal(1000000d)
                .build()))
                .when(falconIndicatorService)
                .mgetLatestIndicators(anyList());
        doThrow(new RuntimeException()).when(frameService).putFrame(any(), anyString(), any());

        dsBaseIndicatorJob.execute(context);

        verify(symbolGqlService, Mockito.times(1)).querySymbol(anyString());
        verify(symbolGqlService, Mockito.times(1)).querySymbol(gql);
        verify(falconIndicatorService, Mockito.times(3)).mgetLatestIndicators(anyList());
    }
}
