/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.model;

/**
 * <AUTHOR>
 * @version ScannerPeriodEnum.java, v 0.1 2025年03月17日 16:58 lzt
 */
public enum ScannerPeriodEnum {

    P_None(0),
    P_Day1(1),
    P_Min1(2);

    /**
     * code
     */
    private int code;

    /**
     *
     */
    ScannerPeriodEnum(int code) {
        this.code = code;
    }


    public static ScannerPeriodEnum getPeriodByName(String name) {
        for (ScannerPeriodEnum s : ScannerPeriodEnum.values()) {
            if (s.name().equals(name)) {
                return s;
            }
        }
        return P_None;
    }

    /**
     * Getter method for property <tt>code</tt>.
     *
     * @return property value of code
     */
    public int getCode() {
        return code;
    }
}