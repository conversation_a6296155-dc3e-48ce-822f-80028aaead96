/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener;

import com.alipay.logicview.sdk.service.entry.ViewCacheEntrySdk;
import com.alipay.quot.commons.facade.model.SymbolDTO;
import com.alipay.tradequote.core.falcon.screener.config.ScreenConfigServiceImpl;
import com.alipay.tradequote.core.falcon.screener.config.ScreenDrm;
import com.alipay.tradequote.core.falcon.screener.model.template.strategy.ScreenStrategy;
import com.alipay.tradequote.core.falcon.screener.view.drm.ScreenerViewDrm;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;


import java.util.HashSet;

import static org.junit.Assert.*;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 */
public class ScreenConfigServiceTest {
    ScreenConfigServiceImpl service = new ScreenConfigServiceImpl();
    ScreenDrm drm = new ScreenDrm();

    @Mock
    ViewCacheEntrySdk viewCacheEntrySdk ;

    ScreenerViewDrm viewDrm = new ScreenerViewDrm("screenerquote");

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        drm.setAppName("screenerquote");
        drm.init();
        service.setScreenDrm(drm);
        service.setScreenerViewDrm(viewDrm);
        service.setViewCacheEntrySdk(viewCacheEntrySdk);
    }

    @Test
    public void test1() {
        drm.setStrategyConf("[\n" +
                "  {\n" +
                "    \"from\": {\n" +
                "      \"gql\": \"mkt=SH\"\n" +
                "    },\n" +
                "    \"filter\": {\n" +
                "      \"filter\": \"(> PE_TTM 10)\"\n" +
                "    }\n" +
                "  },\n" +
                "  {\n" +
                "    \"from\": {\n" +
                "      \"strategyId\": \"test\",\n" +
                "      \"gql\": \"mkt=SH\"\n" +
                "    },\n" +
                "    \"filter\": {\n" +
                "      \"filter\": \"(> PE_TTM 10)\"\n" +
                "    },\n" +
                "    \"metric\": {\n" +
                "      \"fieldMetric\": {\n" +
                "        \"PE_TTM\": [\"min\", \"max\"]\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "]\n");

        ScreenStrategy strategy = service.getScreenStrategy("test");
        assertNotNull(strategy);
    }

    @Test
    public void test2() {
        SymbolDTO s = new SymbolDTO();
        s.setName("*ST盈方");
        boolean f = service.isST(s);
        assertTrue(f);

        s.setName("贵州茅台");
        f = service.isST(s);
        assertFalse(f);
    }

    @Test
    public void test3() {
        drm.setSymBlist("600519.SH,601318.SH");
        drm.update("symBlist", "600519.SH,601318.SH");
        assertTrue(service.isBlocked(makeSymObj("600519.SH")));
        assertTrue(service.isBlocked(makeSymObj("601318.SH")));
        assertFalse(service.isBlocked(makeSymObj("000001.SZ")));

        drm.setSymBlist("");
        drm.update("symBlist", "");
        assertFalse(service.isBlocked(makeSymObj("600519.SH")));
        assertFalse(service.isBlocked(makeSymObj("601318.SH")));
        assertFalse(service.isBlocked(makeSymObj("000001.SZ")));
    }

    @Test
    public void test4(){
        drm.setOpenNewComparator(false);
        assertFalse(service.isOpenNewComparator());
        drm.setOpenNewComparator(true);
        assertTrue(service.isOpenNewComparator());
    }

    @Test
    public void test5(){
        drm.setPlateGrayRegex("");
        assertFalse(service.isPlateGraySymbol("810000.CNS"));
        assertFalse(service.isPlateGraySymbol("810001.CNS.90"));
        drm.setPlateGrayRegex("81*.CNS");
        assertTrue(service.isPlateGraySymbol("810000.CNS"));
        drm.setPlateGrayRegex("!810000.CNS|81*.CNS");
        assertFalse(service.isPlateGraySymbol("810000.CNS"));
        assertTrue(service.isPlateGraySymbol("810001.CNS"));
        drm.setPlateGrayRegex("81*.CNS|!810001.CNS|82*.CNS|83*|*90");
        assertTrue(service.isPlateGraySymbol("810002.CNS"));
        assertFalse(service.isPlateGraySymbol("810001.CNS"));
        assertTrue(service.isPlateGraySymbol("820001.CNS"));
        assertTrue(service.isPlateGraySymbol("830001.CNS"));
        assertTrue(service.isPlateGraySymbol("810001.CNS.90"));
        assertFalse(service.isPlateGraySymbol(""));
        drm.setPlateGrayRegex("||");
        assertFalse(service.isPlateGraySymbol(""));
        drm.setPlateGrayRegex("|a|");
        assertTrue(service.isPlateGraySymbol("a"));
    }

    @Test
    public void test6(){
        drm.setOpenPercentCond(true);
        assertTrue(service.isOpenPercentCond());
        drm.setOpenPercentCond(false);
        assertFalse(service.isOpenPercentCond());
        drm.setOpenUdfFilter(true);
        assertTrue(service.isOpenUdfFilter());
        drm.setOpenUdfFilter(false);
        assertFalse(service.isOpenUdfFilter());
    }

    private SymbolDTO makeSymObj(String sym) {
        SymbolDTO symObj = new SymbolDTO();
        symObj.setSymbol(sym);
        return symObj;
    }


    @Test
    public void testCacheStartUp(){
        drm.setCacheStartUpLoad(true);
        assertTrue(service.isCacheStartUpLoad());
    }

    @Test
    public void testViewDrmLogic(){
        viewDrm.setScreenerTriggerLogicView(true);
        when(viewCacheEntrySdk.indicatorAllowSet()).thenReturn(new HashSet<>());
        assertTrue(service.getAllowIndicators().contains("PE_TTM"));
    }
}
