/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.integration.zsearch;

import com.alipay.zsearch.core.DocumentResult;
import com.alipay.zsearch.core.SearchResult;
import com.alipay.zsearch.core.search.SearchSourceBuilder;

/**
 * <AUTHOR>
 * @version ZsearchClient.java, v 0.1 2025年02月25日 14:46 lzt
 */
public interface ZsearchClient {

    /**
     * execute zsearch search query
     *
     * @param index index name
     * @param source search source
     * @return result
     */
    SearchResult search(String index, SearchSourceBuilder source);

    /**
     * update
     *
     * @param index index
     * @param id id
     * @param object object
     * @param version version
     * @return result
     */
    DocumentResult index(String index, String id, Object object, long version);

    /**
     * 获取业务类型
     * @return
     */
    default String getBizType(){
        throw new UnsupportedOperationException("不支持的操作类型");
    }
}