package com.alipay.tradequote.core.falcon.service.indicator;

import com.alipay.quot.commons.models.BaseIndicator;
import com.alipay.quote.hulk.model.CacheContext;
import com.alipay.quote.hulk.model.HulkCacheKey;
import com.alipay.quote.hulk.model.HulkCacheScoredValue;
import com.alipay.quote.hulk.model.QueryCond;
import com.alipay.tradequote.core.falcon.model.DataWrapper;
import com.alipay.tradequote.core.falcon.storage.client.StorageClient;
import com.alipay.tradequote.core.falcon.storage.client.StorageClientImpl;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

@RunWith(MockitoJUnitRunner.class)
public class IndicatorListServiceImplTest {
    @InjectMocks
    protected IndicatorListServiceImpl indicatorListService = new IndicatorListServiceImpl();

    @Mock
    protected StorageClient storageClient;

    @Test
    public void query() {
        BaseIndicator.BaseIndicatorDO snapshotObj = BaseIndicator.BaseIndicatorDO.newBuilder()
            .setSymbol("000001.SH")
            .setEpsTTM(0.1d)
            .build();
        DataWrapper dataWrapper = DataWrapper.builder()
            .data(snapshotObj.toByteArray())
            .build();

        HulkCacheScoredValue hulCacheScoredValue = new HulkCacheScoredValue(1, dataWrapper);
        doReturn(Lists.newArrayList(hulCacheScoredValue)).when(storageClient).zrange(any(CacheContext.class),
            any(HulkCacheKey.class), any(QueryCond.class));

        QueryCond queryCond = QueryCond.builder()
            .rangeCond(QueryCond.RangeCond.builder()
                .max(100)
                .min(10)
                .maxExcluded(true)
                .minExcluded(true)
                .build())
            .build();
        indicatorListService.query("00001.SH.SH.L1", queryCond);

        dataWrapper = DataWrapper.builder()
            .data("11".getBytes())
            .build();
        hulCacheScoredValue = new HulkCacheScoredValue(1, dataWrapper);
        doReturn(Lists.newArrayList(hulCacheScoredValue)).when(storageClient).zrange(any(CacheContext.class),
            any(HulkCacheKey.class), any(QueryCond.class));
        indicatorListService.query("00001.SH.SH.L1", queryCond);
    }

    @Test
    public void batchQuery() {
        BaseIndicator.BaseIndicatorDO snapshotObj = BaseIndicator.BaseIndicatorDO.newBuilder()
            .setSymbol("000001.SH")
            .setEpsTTM(0.1d)
            .build();
        DataWrapper dataWrapper = DataWrapper.builder()
            .data(snapshotObj.toByteArray())
            .build();

        HulkCacheScoredValue hulCacheScoredValue1 = new HulkCacheScoredValue(1, dataWrapper);
        HulkCacheScoredValue hulCacheScoredValue2 = new HulkCacheScoredValue(2, dataWrapper);
        List<HulkCacheScoredValue> hulkCacheScoredValueList = Lists.newArrayList(hulCacheScoredValue1,
            hulCacheScoredValue2);
        doReturn(Lists.newArrayList(hulkCacheScoredValueList, hulkCacheScoredValueList)).when(storageClient).zrange(
            any(CacheContext.class),
            any(QueryCond.class),
            any(List.class));

        QueryCond queryCond = QueryCond.builder()
            .rangeCond(QueryCond.RangeCond.builder()
                .max(100)
                .min(10)
                .maxExcluded(true)
                .minExcluded(true)
                .build())
            .build();
        indicatorListService.query(queryCond, Lists.newArrayList("000001.SH.SH.L1", "000002.SH.SH.L1"));

        dataWrapper = DataWrapper.builder()
            .data("11".getBytes())
            .build();
        hulCacheScoredValue1 = new HulkCacheScoredValue(1, dataWrapper);
        hulCacheScoredValue2 = new HulkCacheScoredValue(2, dataWrapper);
        hulkCacheScoredValueList = Lists.newArrayList(hulCacheScoredValue1, hulCacheScoredValue2);
        doReturn(Lists.newArrayList(hulkCacheScoredValueList, hulkCacheScoredValueList)).when(storageClient).zrange(any(CacheContext.class),
            any(QueryCond.class),
            any(List.class));
        indicatorListService.query(queryCond, Lists.newArrayList("000001.SH.SH.L1", "000002.SH.SH.L1"));
    }

    @Test
    public void update() {
        //empty list
        List list = Lists.newArrayList();
        indicatorListService.update(list);

        BaseIndicator.BaseIndicatorDO build = BaseIndicator.BaseIndicatorDO.newBuilder().
            setAmplitude(1.1).build();
        list.add(build);
        indicatorListService.update(list);

        //wrong list number
        list.add(1);
        indicatorListService.update(list);
    }

    @Test
    public void delete() {
        //empty list
        List list = Lists.newArrayList();
        indicatorListService.delete(list);

        BaseIndicator.BaseIndicatorDO build = BaseIndicator.BaseIndicatorDO.newBuilder().
            setAmplitude(1.1).build();
        list.add(build);
        indicatorListService.delete(list);

        //wrong list number
        list.add(1);
        indicatorListService.delete(list);
    }

    @Test
    public void getStorageKey() {
        indicatorListService.getStorageKey();
    }

    @Test
    public void getStorageClient() {
        indicatorListService.getStorageClient();
    }

    @Test
    public void setStorageKey() {
        indicatorListService.setStorageKey("test");
    }

    @Test
    public void setStorageClient() {
        indicatorListService.setStorageClient(new StorageClientImpl());
    }
}

