/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.scheduler.model;

import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version ScannerTaskItem.java, v 0.1 2025年02月14日 16:16 lzt
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScannerTaskItem implements Serializable {

    private static final long serialVersionUID = -3459423210822987163L;

    /**
     * 定时任务id
     */
    private String taskId;

    /**
     * 定时任务期望执行时间
     */
    private Date expectedTriggerTime;
}