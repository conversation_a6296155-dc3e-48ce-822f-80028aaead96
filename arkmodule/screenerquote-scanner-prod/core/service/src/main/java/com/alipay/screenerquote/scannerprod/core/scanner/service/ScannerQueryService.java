/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.service;

import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerPortfolioBO;
import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerPortfolio;

import java.util.List;

/**
 * <AUTHOR>
 * @version ScannerQueryService.java, v 0.1 2025年02月25日 14:31 lzt
 */
public interface ScannerQueryService {

    /**
     * 查询策略返回的股票组合
     * @param request
     * @return
     */
    List<ScannerPortfolio> queryPortfolio(ScannerPortfolioBO request);
}