package com.alipay.tradequote.core.falcon.screener.sched;

import com.alipay.tradequote.core.falcon.screener.model.IndicatorDescriptor;
import com.alipay.tradequote.core.falcon.screener.model.JobDescriptor;
import com.alipay.tradequote.core.falcon.screener.sched.job.DsScreenerViewJob;
import com.alipay.tradequote.core.falcon.screener.view.drm.ScreenerViewDrm;
import com.alipay.tradequote.core.falcon.screener.view.sched.IndicatorViewScheduler;
import com.alipay.tradequote.meta.service.SymbolGqlService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;

public class DsScreenerViewJobTest {

    @InjectMocks
    DsScreenerViewJob dsScreenerViewJob;

    @Mock
    SymbolGqlService symbolGqlService;

    @Mock
    ScreenerViewDrm screenerViewDrm;

    @Mock
    IndicatorViewScheduler indicatorViewSchedulerService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void test(){
        Mockito.when(screenerViewDrm.getViewNameList()).thenReturn(new ArrayList<>());
        Mockito.when(symbolGqlService.querySymbol(any())).thenReturn(new ArrayList<>());
        try {
            dsScreenerViewJob.execute(getScreenJobContext(new ArrayList<>()));
        }catch (Exception e){
            Assert.assertNotNull(e);
        }

        Mockito.when(screenerViewDrm.getViewNameList()).thenReturn(Lists.newArrayList("test"));
        try {
            dsScreenerViewJob.execute(getScreenJobContext(new ArrayList<>()));
        }catch (Exception e){
            Assert.assertNotNull(e);
        }

        doThrow(new RuntimeException()).when(indicatorViewSchedulerService).execute(any());

        try {
            dsScreenerViewJob.execute(getScreenJobContext(new ArrayList<>()));
        }catch (Exception e){
            Assert.assertNotNull(e);
        }
    }


    private ScreenJobContext getScreenJobContext(List<String> symbols) {
        String gql = "gql=" + String.join(",", symbols);
        JobDescriptor jobDsp = new JobDescriptor();
        jobDsp.setName("DS_INDICATOR_VIEW_JOB");
        jobDsp.setGql(gql);

        IndicatorDescriptor i1 = new IndicatorDescriptor();
        i1.setName("PE_TTM");
        i1.setField("1");
        IndicatorDescriptor i2 = new IndicatorDescriptor();
        i2.setName("MKT_CAP");
        i2.setField("2");
        jobDsp.setIndicators(Lists.newArrayList(i1, i2));

        Map<String, Object> map = new HashMap<>();
        map.put("rsDataSet", "INDICATOR");
        map.put("dimKeys", "INDICATOR");
        jobDsp.setExtProps(map);

        return ScreenJobContext.builder()
                .jobDescriptor(jobDsp)
                .listTypes(listTypes())
                .build();
    }

    public Set<String> listTypes() {
        return Sets.newHashSet("ListType_D", "ListType_F");
    }

}
