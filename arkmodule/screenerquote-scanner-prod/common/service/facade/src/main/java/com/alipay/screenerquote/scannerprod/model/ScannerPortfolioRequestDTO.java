/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.model;

import com.alipay.quot.commons.facade.model.querycond.PageCond;
import com.alipay.quot.commons.facade.model.querycond.RangeCond;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version ScannerQueryRequestDTO.java, v 0.1 2025年02月25日 11:46 lzt
 */
@Setter
@Getter
@ToString
public class ScannerPortfolioRequestDTO implements Serializable {

    private static final long serialVersionUID = 1493408268636657529L;

    /**
     * 策略Id
     */
    private String strategyId;

    /**
     * 场景id
     */
    private String sceneId;

    /**
     * 起始标的
     */
    private String srcId;

    /**
     * queryRange
     */
    private RangeCond rangeCond;

    /**
     * pageCond
     */
    private PageCond pageCond;

    /**
     * 是否倒序，默认为true
     */
    private Boolean reversed;
}