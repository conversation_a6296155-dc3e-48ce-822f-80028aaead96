package com.alipay.tradequote.core.falcon.screener.view.service;

import com.alipay.tradequote.core.falcon.screener.view.ViewContext;
import com.alipay.tradequote.core.falcon.screener.view.sched.IndicatorViewAssetScheduler;
import com.alipay.tradequote.core.falcon.service.dataassets.DataAssetViewService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class IndicatorViewAssetTest {

    @Mock
    DataAssetViewService dataAssetViewService;

    @InjectMocks
    IndicatorViewAssetScheduler scheduler;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    String validJson = "{\n" +
            "    \"indicatorViewReferenceList\": [\n" +
            "        {\n" +
            "            \"aliasName\": \"tor_grow_rate\",\n" +
            "            \"assetCategory\": \"FINANCE\",\n" +
            "            \"assetName\": \"FINANCE\",\n" +
            "            \"fieldName\": \"tor_grow_rate\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"net_profit\",\n" +
            "            \"assetCategory\": \"FINANCE\",\n" +
            "            \"assetName\": \"FINANCE\",\n" +
            "            \"fieldName\": \"net_profit\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"net_profit_grow_rate\",\n" +
            "            \"assetCategory\": \"FINANCE\",\n" +
            "            \"assetName\": \"FINANCE\",\n" +
            "            \"fieldName\": \"net_profit_grow_rate\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"operating_reene\",\n" +
            "            \"assetCategory\": \"FINANCE\",\n" +
            "            \"assetName\": \"FINANCE\",\n" +
            "            \"fieldName\": \"operating_reene\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"net_profit_yoy_per_quarter\",\n" +
            "            \"assetCategory\": \"FINANCE\",\n" +
            "            \"assetName\": \"FINANCE\",\n" +
            "            \"fieldName\": \"net_profit_yoy_per_quarter\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"price\",\n" +
            "            \"assetCategory\": \"SNAPSHOT\",\n" +
            "            \"assetName\": \"SNAPSHOT\",\n" +
            "            \"fieldName\": \"price\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"high_price\",\n" +
            "            \"assetCategory\": \"SNAPSHOT\",\n" +
            "            \"assetName\": \"SNAPSHOT\",\n" +
            "            \"fieldName\": \"high_price\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"low_price\",\n" +
            "            \"assetCategory\": \"SNAPSHOT\",\n" +
            "            \"assetName\": \"SNAPSHOT\",\n" +
            "            \"fieldName\": \"low_price\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"amount\",\n" +
            "            \"assetCategory\": \"SNAPSHOT\",\n" +
            "            \"assetName\": \"SNAPSHOT\",\n" +
            "            \"fieldName\": \"amount\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"change_percent\",\n" +
            "            \"assetCategory\": \"SNAPSHOT\",\n" +
            "            \"assetName\": \"SNAPSHOT\",\n" +
            "            \"fieldName\": \"change_percent\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"pe_ttm\",\n" +
            "            \"assetCategory\": \"VALUATION\",\n" +
            "            \"assetName\": \"VALUATION_DAY\",\n" +
            "            \"fieldName\": \"pe_ttm\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"ps_ttm\",\n" +
            "            \"assetCategory\": \"VALUATION\",\n" +
            "            \"assetName\": \"VALUATION_DAY\",\n" +
            "            \"fieldName\": \"ps_ttm\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"pb\",\n" +
            "            \"assetCategory\": \"VALUATION\",\n" +
            "            \"assetName\": \"VALUATION_DAY\",\n" +
            "            \"fieldName\": \"pb\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"dividend_ratio_ttm\",\n" +
            "            \"assetCategory\": \"VALUATION\",\n" +
            "            \"assetName\": \"VALUATION_DAY\",\n" +
            "            \"fieldName\": \"dividend_ratio_ttm\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"total_mv\",\n" +
            "            \"assetCategory\": \"INDICATOR\",\n" +
            "            \"assetName\": \"INDICATOR\",\n" +
            "            \"fieldName\": \"market_cap_total\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"total_fund\",\n" +
            "            \"assetCategory\": \"STOCK_FUND_NUM\",\n" +
            "            \"assetName\": \"STOCK_FUND_NUM\",\n" +
            "            \"fieldName\": \"total_fund\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"inflow_days\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_consecutiveRiseDays\",\n" +
            "            \"fieldName\": \"riseDays\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"highly_change_percent\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_highPriceChangePercent\",\n" +
            "            \"fieldName\": \"changePercent\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"three_day_change_percent\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"threeday_changepercent\",\n" +
            "            \"fieldName\": \"changePercent\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"five_day_change_percent\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"fiveday_changepercent\",\n" +
            "            \"fieldName\": \"changePercent\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"seven_day_change_percent\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"sevenday_changepercent\",\n" +
            "            \"fieldName\": \"changePercent\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"thirty_day_high_price\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"30day_beforesplit_highlow_eod\",\n" +
            "            \"fieldName\": \"highPrice\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"thirty_day_low_price\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"30day_beforesplit_highlow_eod\",\n" +
            "            \"fieldName\": \"lowPrice\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"sixty_day_high_price\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"60day_beforesplit_highlow_eod\",\n" +
            "            \"fieldName\": \"highPrice\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"sixty_day_low_price\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"60day_beforesplit_highlow_eod\",\n" +
            "            \"fieldName\": \"lowPrice\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"ninety_day_high_price\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"90day_beforesplit_highlow_eod\",\n" +
            "            \"fieldName\": \"highPrice\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"ninety_day_low_price\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"90day_beforesplit_highlow_eod\",\n" +
            "            \"fieldName\": \"lowPrice\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"history_high_price\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"history_beforesplit_highlow_eod\",\n" +
            "            \"fieldName\": \"highPrice\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"history_low_price\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"history_beforesplit_highlow_eod\",\n" +
            "            \"fieldName\": \"lowPrice\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"one_day_north_net_in\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_northNetIn1D\",\n" +
            "            \"fieldName\": \"northNetIn1D\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"three_day_north_net_in\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_northNetIn3D\",\n" +
            "            \"fieldName\": \"netIn\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"five_day_north_net_in\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_northNetIn5D\",\n" +
            "            \"fieldName\": \"northNetIn5D\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"seven_day_north_net_in\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_northNetIn7D\",\n" +
            "            \"fieldName\": \"netIn\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"twenty_day_north_net_in\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_northNetIn20D\",\n" +
            "            \"fieldName\": \"northNetIn20D\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"sixty_day_north_net_in\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_northNetIn60D\",\n" +
            "            \"fieldName\": \"netIn\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"north_capital_inflow_days\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_northCapitalConsecutiveNetInflow\",\n" +
            "            \"fieldName\": \"inflowDays\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"north_capital_inflow_sum\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_northCapitalConsecutiveNetInflow\",\n" +
            "            \"fieldName\": \"inflowSum\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"main_capital_inflow_days\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_mainCapitalConsecutiveNetInflow\",\n" +
            "            \"fieldName\": \"inflowDays\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"main_capital_inflow_sum\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_mainCapitalConsecutiveNetInflow\",\n" +
            "            \"fieldName\": \"inflowSum\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"three_day_main_net_in\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_mainCapitalConsecutiveNetInflow\",\n" +
            "            \"fieldName\": \"3DaySum\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"five_day_main_net_in\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_mainCapitalConsecutiveNetInflow\",\n" +
            "            \"fieldName\": \"5DaySum\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"seven_day_main_net_in\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_mainCapitalConsecutiveNetInflow\",\n" +
            "            \"fieldName\": \"7DaySum\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"one_day_main_net_in\",\n" +
            "            \"assetCategory\": \"MAIN_CAPITAL\",\n" +
            "            \"assetName\": \"main_capital_1D\",\n" +
            "            \"fieldName\": \"main_net_in\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"etf_indi_symbol\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"etf_indi_symbol\",\n" +
            "            \"fieldName\": \"indi_symbol\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"etf_indi_name\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"etf_indi_symbol\",\n" +
            "            \"fieldName\": \"indi_name\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"has_indi_plate\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"etf_indi_symbol\",\n" +
            "            \"fieldName\": \"has_indi_plate\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"etf_top_cns_symbol\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"etf_top_cns_symbol\",\n" +
            "            \"fieldName\": \"top_cns_symbol\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"etf_top_cns_name\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"etf_top_cns_symbol\",\n" +
            "            \"fieldName\": \"top_cns_name\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"belonged_plates\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"stock_belonged_plates\",\n" +
            "            \"fieldName\": \"belonged_plates\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"has_related_otc_fund\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"plate_related_fund\",\n" +
            "            \"fieldName\": \"has_related_otc_fund\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"has_related_etf\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"plate_related_fund\",\n" +
            "            \"fieldName\": \"has_related_etf\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"asset_size_fund\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"fund_archive\",\n" +
            "            \"fieldName\": \"asset_size\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"hand_price\",\n" +
            "            \"assetCategory\": \"SNAPSHOT\",\n" +
            "            \"assetName\": \"SNAPSHOT\",\n" +
            "            \"fieldName\": \"hand_price\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"etf_hot_score\",\n" +
            "            \"assetCategory\": \"PUPULARITY\",\n" +
            "            \"assetName\": \"HOT_ETF\",\n" +
            "            \"fieldName\": \"score\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"max_weights_plate_symbol\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"stock_belonged_plates\",\n" +
            "            \"fieldName\": \"max_weights_plate_symbol\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"max_weights_plate_name\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"stock_belonged_plates\",\n" +
            "            \"fieldName\": \"max_weights_plate_name\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"max_weights_indi_symbol\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"stock_belonged_plates\",\n" +
            "            \"fieldName\": \"max_weights_indi_symbol\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"max_weights_indi_name\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"stock_belonged_plates\",\n" +
            "            \"fieldName\": \"max_weights_indi_name\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"etf_top_related_cns_symbols\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"etf_top_related_cns_symbols\",\n" +
            "            \"fieldName\": \"top_related_cns_symbols\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"etf_indi_north_net_in\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"etf_indi_north_net_in\",\n" +
            "            \"fieldName\": \"north_net_in\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"etf_indi_main_net_in\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"etf_indi_main_net_in\",\n" +
            "            \"fieldName\": \"main_net_in\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"tracing_index_symbol\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_relatedIndex\",\n" +
            "            \"fieldName\": \"relatedIndex\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"tracing_index_name\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_relatedIndex\",\n" +
            "            \"fieldName\": \"relatedIndexName\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"tracing_index_valuation_scope\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_relatedIndex\",\n" +
            "            \"fieldName\": \"valuationScope\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"tracing_index_valuation_level_name\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_relatedIndex\",\n" +
            "            \"fieldName\": \"valuationLevelName\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"tracing_index_roe_tag_name\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_relatedIndex\",\n" +
            "            \"fieldName\": \"roeTagName\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"tracing_index_roe_percentile\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_relatedIndex\",\n" +
            "            \"fieldName\": \"roePercentile\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"tracing_index_be_broad_based\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_relatedIndex\",\n" +
            "            \"fieldName\": \"beBroadBased\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"one_year_change_percent\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"oneyear_changepercent\",\n" +
            "            \"fieldName\": \"changePercent\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"asset_mixed_type_desc\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"fund_archive\",\n" +
            "            \"fieldName\": \"asset_mixed_type_desc\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"volatility_rate\",\n" +
            "            \"assetCategory\": \"SNAPSHOT\",\n" +
            "            \"assetName\": \"SNAPSHOT\",\n" +
            "            \"fieldName\": \"volatility_rate\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"volatility_price\",\n" +
            "            \"assetCategory\": \"SNAPSHOT\",\n" +
            "            \"assetName\": \"SNAPSHOT\",\n" +
            "            \"fieldName\": \"volatility_price\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"previous_close\",\n" +
            "            \"assetCategory\": \"SNAPSHOT\",\n" +
            "            \"assetName\": \"SNAPSHOT\",\n" +
            "            \"fieldName\": \"previous_close\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"stock_hot_score\",\n" +
            "            \"assetCategory\": \"PUPULARITY\",\n" +
            "            \"assetName\": \"HOT_STOCK\",\n" +
            "            \"fieldName\": \"score1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"stock_total_discuss\",\n" +
            "            \"assetCategory\": \"PUPULARITY\",\n" +
            "            \"assetName\": \"HOT_STOCK\",\n" +
            "            \"fieldName\": \"total_discuss\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"volume\",\n" +
            "            \"assetCategory\": \"SNAPSHOT\",\n" +
            "            \"assetName\": \"SNAPSHOT\",\n" +
            "            \"fieldName\": \"volume\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"hand\",\n" +
            "            \"assetCategory\": \"SNAPSHOT\",\n" +
            "            \"assetName\": \"SNAPSHOT\",\n" +
            "            \"fieldName\": \"hand\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"etf_following_offset_one_year\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"etf_following_offset_one_year\",\n" +
            "            \"fieldName\": \"track_error\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"etf_index_market_tag\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"index_etf_basic_information\",\n" +
            "            \"fieldName\": \"index_market_tag\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"is_t_plus_one_trade\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"index_etf_basic_information\",\n" +
            "            \"fieldName\": \"is_t_plus_one_trade\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"oversea_index_name_abbr\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"index_etf_basic_information\",\n" +
            "            \"fieldName\": \"ovesea_index_name_abbr\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"market_cap_circulation\",\n" +
            "            \"assetCategory\": \"INDICATOR\",\n" +
            "            \"assetName\": \"INDICATOR\",\n" +
            "            \"fieldName\": \"market_cap_circulation\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"limit_up_minutes_today\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_limit_up_stat\",\n" +
            "            \"fieldName\": \"limit_up_minutes_today\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"limit_up_times\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_limit_up_stat\",\n" +
            "            \"fieldName\": \"limit_up_times\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"limit_up_cost_days\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_limit_up_stat\",\n" +
            "            \"fieldName\": \"limit_up_cost_days\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"yearly_limit_up_count\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"yearly_limit_up_count\",\n" +
            "            \"fieldName\": \"count\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"one_year_stock_change_percent\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"oneYear_stock_changepercent\",\n" +
            "            \"fieldName\": \"oneYear_stock_changepercent\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"belonged_all_plates\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"stock_belonged_all_plates\",\n" +
            "            \"fieldName\": \"belonged_plates\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"dividend_three_year_rank_percent\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"dividend_three_year_rank_percent\",\n" +
            "            \"fieldName\": \"dividend_rank\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"gold_num_recent_month\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"gold_stock_num\",\n" +
            "            \"fieldName\": \"gold_num_recent_month\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"institutional_number_one_month\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"onemonth_investor_count\",\n" +
            "            \"fieldName\": \"investorCount\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"egrowratefloorc_income\",\n" +
            "            \"assetCategory\": \"FINANCE\",\n" +
            "            \"assetName\": \"FINANCE_FORECAST\",\n" +
            "            \"fieldName\": \"egrowratefloorc_income\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"egrowratefloorc_net_profit\",\n" +
            "            \"assetCategory\": \"FINANCE\",\n" +
            "            \"assetName\": \"FINANCE_FORECAST\",\n" +
            "            \"fieldName\": \"egrowratefloorc_net_profit\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"egrowthrateceilc_income\",\n" +
            "            \"assetCategory\": \"FINANCE\",\n" +
            "            \"assetName\": \"FINANCE_FORECAST\",\n" +
            "            \"fieldName\": \"egrowthrateceilc_income\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"egrowthrateceilc_net_profit\",\n" +
            "            \"assetCategory\": \"FINANCE\",\n" +
            "            \"assetName\": \"FINANCE_FORECAST\",\n" +
            "            \"fieldName\": \"egrowthrateceilc_net_profit\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"forecast_enddate_quarter\",\n" +
            "            \"assetCategory\": \"FINANCE\",\n" +
            "            \"assetName\": \"FINANCE_FORECAST\",\n" +
            "            \"fieldName\": \"enddate_quarter\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"forecast_enddate_year\",\n" +
            "            \"assetCategory\": \"FINANCE\",\n" +
            "            \"assetName\": \"FINANCE_FORECAST\",\n" +
            "            \"fieldName\": \"enddate_year\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"eprofitceiling\",\n" +
            "            \"assetCategory\": \"FINANCE\",\n" +
            "            \"assetName\": \"FINANCE_FORECAST\",\n" +
            "            \"fieldName\": \"eprofitceiling\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"eprofitfloor\",\n" +
            "            \"assetCategory\": \"FINANCE\",\n" +
            "            \"assetName\": \"FINANCE_FORECAST\",\n" +
            "            \"fieldName\": \"eprofitfloor\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"forecast_expiration_time\",\n" +
            "            \"assetCategory\": \"FINANCE\",\n" +
            "            \"assetName\": \"FINANCE_FORECAST\",\n" +
            "            \"fieldName\": \"expiration_time\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"incomeceiling\",\n" +
            "            \"assetCategory\": \"FINANCE\",\n" +
            "            \"assetName\": \"FINANCE_FORECAST\",\n" +
            "            \"fieldName\": \"incomeceiling\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"incomefloor\",\n" +
            "            \"assetCategory\": \"FINANCE\",\n" +
            "            \"assetName\": \"FINANCE_FORECAST\",\n" +
            "            \"fieldName\": \"incomefloor\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"forecast_public_date\",\n" +
            "            \"assetCategory\": \"FINANCE\",\n" +
            "            \"assetName\": \"FINANCE_FORECAST\",\n" +
            "            \"fieldName\": \"info_public_date\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"plate_valuation_level_num\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"plate_valuation_level_metrics\",\n" +
            "            \"fieldName\": \"plate_valuation_level\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"plate_valuation_scope\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"plate_valuation_level_metrics\",\n" +
            "            \"fieldName\": \"plate_valuation_scope\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"plate_valuation_name\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"plate_valuation_level_metrics\",\n" +
            "            \"fieldName\": \"plate_valuation_name\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"plate_property_level_num\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"plate_property_level_metrics\",\n" +
            "            \"fieldName\": \"plate_property_level\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"property_rank_pct\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"plate_property_level_metrics\",\n" +
            "            \"fieldName\": \"property_rank_pct\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"property_level_name\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"plate_property_level_metrics\",\n" +
            "            \"fieldName\": \"property_level_name\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"thirty_day_netinflow\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"30day_netinflow\",\n" +
            "            \"fieldName\": \"netInFlow\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"daily_close\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_beforesplit_close_eod\",\n" +
            "            \"fieldName\": \"close\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"aliasName\": \"daily_close_SNAPSHOT_DATE\",\n" +
            "            \"assetCategory\": \"INDEX\",\n" +
            "            \"assetName\": \"daily_beforesplit_close_eod\",\n" +
            "            \"fieldName\": \"close\"\n" +
            "        }\n" +
            "    ]\n" +
            "}";


    @Test
    public void testExecute_invalidJsonList() {
        // Mock the JSON list returned by queryAssetViewAliasList
        String viewName = "testView";
        String json = "{\"indicatorViewReferenceList\":[]}";
        when(dataAssetViewService.queryAssetViewAliasList(viewName)).thenReturn(Arrays.asList(json));

        // Mock the view context
        ViewContext context = mock(ViewContext.class);
        when(context.getViewName()).thenReturn(viewName);

        // Perform the execution
        scheduler.execute(context);

        assertTrue(scheduler.getAliasMap().isEmpty());
    }

    @Test
    public void testExecute_validJsonList() {
        String viewName = "testView";
        when(dataAssetViewService.queryAssetViewAliasList(viewName)).thenReturn(Arrays.asList(validJson));

        // Mock the view context
        ViewContext context = mock(ViewContext.class);
        when(context.getViewName()).thenReturn(viewName);

        // Perform the execution, expecting an exception to be thrown
        scheduler.execute(context);

        assertFalse(scheduler.getAliasMap().isEmpty());
        assertNotNull(scheduler.getAliasMap());
    }
}

