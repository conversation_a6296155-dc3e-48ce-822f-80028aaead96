/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.sched;

import com.alipay.quot.commons.facade.request.NimitzDatasetRequest;
import com.alipay.quot.commons.models.Symbol;
import com.alipay.tradequote.core.falcon.nimitz.service.NimitzService;
import com.alipay.tradequote.core.falcon.screener.AbstractScreenerTest;
import com.alipay.tradequote.core.falcon.screener.indicator.IndicatorFrameService;
import com.alipay.tradequote.core.falcon.screener.indicator.IndicatorFrameServiceImpl;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ScreenCacheImpl;
import com.alipay.tradequote.core.falcon.screener.model.DataType;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorDescriptor;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorFrame;
import com.alipay.tradequote.core.falcon.screener.model.JobDescriptor;
import com.alipay.tradequote.core.falcon.screener.sched.job.DsMoneyFlowJob;
import com.alipay.tradequote.meta.service.SymbolGqlService;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;
import tech.tablesaw.api.DoubleColumn;
import tech.tablesaw.api.StringColumn;
import tech.tablesaw.api.Table;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
public class DsMoneyFlowJobTest extends AbstractScreenerTest {
    @InjectMocks
    DsMoneyFlowJob dsMoneyFlowJob;

    @Mock
    SymbolGqlService symbolGqlService;

    @Mock
    NimitzService nimitzCoreService;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        dsMoneyFlowJob = new DsMoneyFlowJob();
        dsMoneyFlowJob.setSymbolGqlService(symbolGqlService);
        dsMoneyFlowJob.setNimitzCoreService(nimitzCoreService);
    }

    @Test
    public void test1() {
        assertEquals("DS_MONEY_FLOW_JOB", dsMoneyFlowJob.getName());
    }

    @Test
    public void test2() {
        List<String> symbols = Lists.newArrayList("600519.SH",
                "000002.SZ",
                "601318.SH");
        String gql = "gql=" + String.join(",", symbols);
        JobDescriptor jobDsp = new JobDescriptor();
        jobDsp.setName("DS_MONEY_FLOW");
        jobDsp.setGql(gql);
        List<Symbol.SymbolDO> objSymbols = makeObjSymbols(symbols);

        doReturn(objSymbols).when(symbolGqlService).querySymbol(gql);
        doReturn(null).when(nimitzCoreService).queryDataset(any());
        IndicatorFrameService indicatorFrameService = Mockito.mock(IndicatorFrameService.class);
        dsMoneyFlowJob.setIndicatorFrameService(indicatorFrameService);

        ScreenJobContext context = ScreenJobContext.builder()
                .jobDescriptor(jobDsp)
                .listTypes(listTypes())
                .build();
        dsMoneyFlowJob.execute(context);

        verify(symbolGqlService, Mockito.times(1)).querySymbol(gql);
        verify(symbolGqlService, Mockito.times(1)).querySymbol(any());
        verify(nimitzCoreService, Mockito.times(3)).queryDataset(any());
        verify(indicatorFrameService, Mockito.times(0)).putFrame(eq(DataType.DS_MONEY_FLOW), any(), any());
    }

    @Test
    public void test3() {
        List<String> symbols = Lists.newArrayList("600519.SH",
                "000002.SZ",
                "601318.SH");
        String gql = "gql=" + String.join(",", symbols);
        JobDescriptor jobDsp = new JobDescriptor();
        jobDsp.setName("DS_MONEY_FLOW");
        jobDsp.setGql(gql);
        IndicatorDescriptor i1 = new IndicatorDescriptor();
        i1.setName("MAIN_NET_IN");
        i1.setField("main_net_in");
        IndicatorDescriptor i2 = new IndicatorDescriptor();
        i2.setName("MAIN_IN");
        i2.setField("main_in");
        i2.setAlias(Lists.newArrayList("a1", "a2"));
        jobDsp.setIndicators(Lists.newArrayList(
                i1,
                i2));
        List<Symbol.SymbolDO> objSymbols = makeObjSymbols(symbols);

        doReturn(objSymbols).when(symbolGqlService).querySymbol(gql);
        when(nimitzCoreService.queryDataset(any())).thenAnswer(invocation -> {
            NimitzDatasetRequest request = invocation.getArgument(0);
            String symbol = request.getObjDimKeys().get(0);
            return makeMoneyFlow(symbol);
        });
        ScreenCacheImpl screenCache = new ScreenCacheImpl();
        screenCache.init();
        IndicatorFrameServiceImpl indicatorFrameService = new IndicatorFrameServiceImpl();
        indicatorFrameService.setScreenCache(screenCache);
        dsMoneyFlowJob.setIndicatorFrameService(indicatorFrameService);

        ScreenJobContext context = ScreenJobContext.builder()
                .jobDescriptor(jobDsp)
                .listTypes(listTypes())
                .build();
        dsMoneyFlowJob.execute(context);
        for (String symbol : symbols) {
            IndicatorFrame frame = indicatorFrameService.getFrame(DataType.DS_MONEY_FLOW, symbol);
            assertNotNull(frame);
            assertTrue(frame.getData().containsKey("MAIN_NET_IN"));
            assertTrue(frame.getData().containsKey("MAIN_IN"));
            assertTrue(frame.getData().containsKey("a1"));
            assertTrue(frame.getData().containsKey("a2"));
            assertEquals(100d, frame.getData().get("MAIN_NET_IN").asDouble(), 0.0001);
            assertEquals(120d, frame.getData().get("MAIN_IN").asDouble(), 0.0001);
            assertEquals(frame.getData().get("MAIN_IN"), frame.getData().get("a1"));
            assertEquals(frame.getData().get("MAIN_IN"), frame.getData().get("a2"));
        }
    }

    @Test
    public void test4() {
        List<String> symbols = Lists.newArrayList("600519.SH",
                "000002.SZ",
                "601318.SH");
        String gql = "gql=" + String.join(",", symbols);
        JobDescriptor jobDsp = new JobDescriptor();
        jobDsp.setName("DS_MONEY_FLOW");
        jobDsp.setGql(gql);
        List<Symbol.SymbolDO> objSymbols = makeObjSymbols(symbols);

        doReturn(objSymbols).when(symbolGqlService).querySymbol(gql);
        doThrow(new RuntimeException()).when(nimitzCoreService).queryDataset(any());
        IndicatorFrameService indicatorFrameService = Mockito.mock(IndicatorFrameService.class);
        dsMoneyFlowJob.setIndicatorFrameService(indicatorFrameService);

        ScreenJobContext context = ScreenJobContext.builder()
                .jobDescriptor(jobDsp)
                .listTypes(listTypes())
                .build();
        dsMoneyFlowJob.execute(context);

        verify(symbolGqlService, Mockito.times(1)).querySymbol(gql);
        verify(symbolGqlService, Mockito.times(1)).querySymbol(any());
        verify(nimitzCoreService, Mockito.times(3)).queryDataset(any());
        verify(indicatorFrameService, Mockito.times(0)).putFrame(eq(DataType.DS_MONEY_FLOW), any(), any());
    }

    private Table makeMoneyFlow(String symbol) {
        return Table.create(
                StringColumn.create("symbol", symbol),
                DoubleColumn.create("main_net_in", 100d),
                DoubleColumn.create("main_in", 120d)
        );
    }
}
