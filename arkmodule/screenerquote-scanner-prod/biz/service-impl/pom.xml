<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>screenerquote-scanner-prod</artifactId>
        <groupId>com.alipay.scannerprod</groupId>
        <version>1.0.0</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>screenerquote-scanner-prod-biz-service-impl</artifactId>
    <packaging>jar</packaging>

    <dependencies>

        <dependency>
            <groupId>com.alipay.scannerprod</groupId>
            <artifactId>screenerquote-scanner-prod-common-service-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>commons-push-model</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>commons-model</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>quote-hulk</artifactId>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>sofa-middleware-log</artifactId>
                    <groupId>com.alipay.sofa.common.log</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>quote-log</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.alipay.scannerprod</groupId>
            <artifactId>screenerquote-scanner-prod-core-model</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.scannerprod</groupId>
            <artifactId>screenerquote-scanner-prod-core-service</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>commons-utils</artifactId>
            <scope>provided</scope>
        </dependency>

    </dependencies>

</project>