/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.integration.zsearch.impl;

import com.alibaba.common.lang.StringUtil;
import com.alipay.quote.log.logger.QuoteLog;
import com.alipay.quote.search.SearchClient;
import com.alipay.quote.search.common.Constants;
import com.alipay.quote.search.model.SearchContext;
import com.alipay.screenerquote.scannerprod.integration.zsearch.ZsearchClient;
import com.alipay.screenerquote.scannerprod.log.DedicateLog;
import com.alipay.sofa.runtime.api.annotation.SofaReference;
import com.alipay.zsearch.ResponseException;
import com.alipay.zsearch.ZSearchRestClient;
import com.alipay.zsearch.action.DocWriteRequest;
import com.alipay.zsearch.common.lucene.VersionType;
import com.alipay.zsearch.core.DocumentResult;
import com.alipay.zsearch.core.Index;
import com.alipay.zsearch.core.Search;
import com.alipay.zsearch.core.SearchResult;
import com.alipay.zsearch.core.search.SearchSourceBuilder;
import com.google.common.base.Preconditions;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.slf4j.Logger;

import java.io.IOException;

import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL1_ZSEARCH;

/**
 * <AUTHOR>
 * @version ZsearchCilentImpl.java, v 0.1 2025年02月25日 14:46 lzt
 */
@Getter
@Setter
@ToString
public class ZsearchClientImpl implements ZsearchClient {


    private static final Logger LOGGER = DedicateLog.PROD_ZSEARCH_LOGGER;

    /**
     * searchClient 搜索client
     */
    @SofaReference
    private SearchClient searchClient;

    /**
     * bizType
     */
    private String bizType;

    /**
     * index type
     */
    private static final String INDEX_TYPE = "_doc";

    /**
     * 查询文档
     * @param index index name
     * @param source search source
     * @return
     */
    @Override
    public SearchResult search(String index, SearchSourceBuilder source) {

        Preconditions.checkArgument(StringUtil.isNotBlank(index), "索引index为空");
        Preconditions.checkArgument(source != null, "搜索内容为空");

        Search search = new Search.Builder()
                .addIndex(index)
                .addType(INDEX_TYPE)
                .source(source)
                .build();

        try {
            return getSearchZSearchClient().execute(search);
        } catch (ResponseException e0) {
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_ZSEARCH, bizType)
                    .ext("search zsearch 异常,{0},{1}", index, source)
                    .throwable(e0)
                    .build()
                    .error();
        } catch (IOException e1) {
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_ZSEARCH, bizType)
                    .bizScene("search")
                    .ext("timeout,{0},{1}", index, source)
                    .throwable(e1)
                    .build()
                    .error();
        } catch (Exception e2) {
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_ZSEARCH, bizType)
                    .ext("search zsearch 未知异常,{0},{1}", index, source)
                    .throwable(e2)
                    .build()
                    .error();
        }

        return null;
    }

    /**
     * 创建 or 覆盖文档
     */
    @Override
    public DocumentResult index(String index, String id, Object object, long version) {

        DocumentResult documentResult = null;
        try {
            Index searchIndex = makeIndex(index, id, object, version);
            documentResult = getSearchZSearchClient().execute(searchIndex);
            loggingClientExecuteResult("index", documentResult);
        } catch (ResponseException e0) {
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_ZSEARCH, bizType)
                    .ext("index zsearch 异常,{0},{1}", index, id)
                    .throwable(e0)
                    .build()
                    .error();
        } catch (IOException e1) {
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_ZSEARCH, bizType)
                    .bizScene("index")
                    .ext("timeout,{0},{1}", index, id)
                    .throwable(e1)
                    .build()
                    .error();
        } catch (Exception e) {
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_ZSEARCH, bizType)
                    .ext("index zsearch 未知异常,{0},{1}", index, id)
                    .throwable(e)
                    .build()
                    .error();
        }

        return documentResult;
    }

    /**
     * logging
     */
    private void loggingClientExecuteResult(String operation, DocumentResult dr) {

        if (dr != null) {
            boolean isSucceeded = dr.isSucceeded();
            int code = dr.getResponseCode();
            String message = dr.getErrorMessage();
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_ZSEARCH, bizType)
                    .bizSuccess(isSucceeded)
                    .bizScene("logging")
                    .ext("execute,{0},{1},{2},{3},full={4}", operation, isSucceeded, code, message, dr.getJsonString())
                    .build()
                    .info();
        } else {

            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_ZSEARCH, bizType)
                    .bizSuccess(false)
                    .bizScene("logging")
                    .ext("execute_null_dr,{0}", operation)
                    .build()
                    .info();
        }
    }

    /**
     * 获取search操作 zsearch client
     *
     * @return ZSearchRestClient
     */
    private ZSearchRestClient getSearchZSearchClient() {

        ZSearchRestClient zSearchClient = searchClient.getZSearchClient(getSearchContext(bizType, Constants.OPERATION_TYPE_SEARCH));
        Preconditions.checkArgument(zSearchClient != null, "获取到的zsearch client为空");
        return zSearchClient;
    }

    /**
     * 获取搜索上下文
     */
    private SearchContext getSearchContext(String bizType, String operationType) {
        return SearchContext.builder()
                .searchType(Constants.SEARCH_TYPE_ZSEARCH)
                .uniqueId(bizType)
                .operationType(operationType)
                .build();
    }

    /**
     * 构造索引
     * @param index
     * @param id
     * @param object
     * @param version
     * @return
     */
    private Index makeIndex(String index, String id, Object object, long version) {
        Index.Builder indexBuilder = new Index.Builder(index, INDEX_TYPE)
                .id(id)
                .source(object)
                .version(version)
                .versionType(VersionType.EXTERNAL)
                .opType(DocWriteRequest.OpType.INDEX);
        return indexBuilder.build();
    }
}