/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.api;

import com.alipay.quot.commons.facade.result.Result;
import com.alipay.screenerquote.scannerprod.model.ScannerPortfolioRequestDTO;
import com.alipay.screenerquote.scannerprod.model.ScannerPortfolioResponseDTO;

/**
 * <AUTHOR>
 * @version ScannerQueryFacade.java, v 0.1 2025年02月25日 10:54 lzt
 */
public interface ScannerQueryFacade {

    /**
     * 查询策略返回的股票组合
     * @param request
     * @return
     */
    Result<ScannerPortfolioResponseDTO> queryPortfolio(ScannerPortfolioRequestDTO request);
}