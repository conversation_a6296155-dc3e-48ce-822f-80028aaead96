/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.service.shadingvalidate.config;

import com.alipay.tradequote.core.falcon.service.shadingvalidate.model.ValidateDescriptor;
import com.google.common.collect.Sets;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Set;

import static org.mockito.Mockito.doReturn;

/**
 * <AUTHOR>
 * @version ShadingConfigServiceTest.java, v 0.1 2023年06月12日 11:55 lzt
 */
public class ShadingConfigServiceTest {


    @InjectMocks
    private ShadingConfigServiceImpl shadingConfigService;

    @Mock
    private ShadingConfigDrm shadingConfigDrm;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        shadingConfigService.setShadingConfigDrm(shadingConfigDrm);
    }

    @Test
    public void getTradingTimeIdSet() {
        Set<String> expectedOutput = Sets.newHashSet("1", "2", "3");
        doReturn(expectedOutput).when(shadingConfigDrm).getTradingTimeIdSet();
        Set<String> tradingTimeIdSet = shadingConfigService.getTradingTimeIdSet();
        Assert.assertEquals(expectedOutput, tradingTimeIdSet);
    }

    @Test
    public void getRankIdSet() {
        Set<String> expectedOutput = Sets.newHashSet("A", "B", "C");
        doReturn(expectedOutput).when(shadingConfigDrm).getShadingRankSet();
        Set<String> rankIdSet = shadingConfigService.getRankIdSet();
        Assert.assertEquals(expectedOutput, rankIdSet);
    }

    @Test
    public void getDescriptorByRankId() {
        String json = "[\n" +
                "  {\n" +
                "    \"rankId\":\"rankId\", \n" +
                "    \"stop\":false, \n" +
                "    \"isTradingDayValidate\":true, \n" +
                "    \"tradingTimeId\":\"1\",         \n" +
                "    \"zoneId\":\"Asia/Shanghai\",   \n" +
                "    \"dailyTimeValidators\":[\t\t\t\n" +
                "      {\n" +
                "        \"startTime\":\"00:00:00.000000\",\n" +
                "        \"endTime\":\"03:00:00.999999\"\n" +
                "      },{\n" +
                "        \"startTime\":\"16:00:00.000000\",\n" +
                "        \"endTime\":\"23:59:59.999999\"\n" +
                "      }\n" +
                "    ]\n" +
                "  },{\n" +
                "    \"rankId\":\"rankAmerica\", \n" +
                "    \"stop\":false, \n" +
                "    \"isTradingDayValidate\":false, \n" +
                "    \"tradingTimeId\":\"5\",         \n" +
                "    \"zoneId\":\"America/New_York\",   \n" +
                "    \"dailyTimeValidators\":[\t\t\t\n" +
                "      {\n" +
                "        \"startTime\":\"00:00:00.000000\",\n" +
                "        \"endTime\":\"03:00:00.999999\"\n" +
                "      },{\n" +
                "        \"startTime\":\"16:00:00.000000\",\n" +
                "        \"endTime\":\"23:59:59.999999\"\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "]";

        doReturn(json).when(shadingConfigDrm).getShadingValidatorList();

        ValidateDescriptor shadingValidator = shadingConfigService.getDescriptorByRankId("rankId");
        Assert.assertEquals("Asia/Shanghai",shadingValidator.getZoneId());
        Assert.assertEquals("1",shadingValidator.getTradingTimeId());
        Assert.assertTrue(shadingValidator.isTradingDayValidate());

        ValidateDescriptor shadingValidator1 = shadingConfigService.getDescriptorByRankId("rankAmerica");
        Assert.assertEquals("America/New_York",shadingValidator1.getZoneId());
        Assert.assertEquals("5",shadingValidator1.getTradingTimeId());
        Assert.assertFalse(shadingValidator1.isTradingDayValidate());

    }

    @Test
    public void getShadingTagSet(){
        Set<String> expectedOutput = Sets.newHashSet("A", "B", "C");
        doReturn(expectedOutput).when(shadingConfigDrm).getShadingTagSet();
        Set<String> rankIdSet = shadingConfigService.getShadingTagSet();
        Assert.assertEquals(expectedOutput, rankIdSet);
    }

    @Test
    public void getTagObjIdBlackSet(){
        Set<String> expectedOutput = Sets.newHashSet("A", "B", "C");
        doReturn(expectedOutput).when(shadingConfigDrm).getTagObjIdBlackSet();
        Set<String> rankIdSet = shadingConfigService.getTagObjIdBlackSet();
        Assert.assertEquals(expectedOutput, rankIdSet);
    }
}