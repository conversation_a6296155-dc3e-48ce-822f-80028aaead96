<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:sofa="http://schema.alipay.com/sofa/schema/slite"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://schema.alipay.com/sofa/schema/slite http://schema.alipay.com/sofa/slite.xsd"
       default-autowire="byName">

    <!-- 选品模块查询 -->
    <bean id="scannerQueryFacade" class="com.alipay.screenerquote.scannerprod.service.ScannerQueryFacadeImpl"/>
    <sofa:service ref="scannerQueryFacade" interface="com.alipay.screenerquote.scannerprod.api.ScannerQueryFacade">
        <sofa:binding/>
        <sofa:binding.tr/>
        <sofa:binding.ws/>
    </sofa:service>


    <!-- 选品模块查询 -->
    <bean id="scannerMngFacade" class="com.alipay.screenerquote.scannerprod.service.ScannerMngFacadeImpl"/>
    <sofa:service ref="scannerMngFacade" interface="com.alipay.screenerquote.scannerprod.api.ScannerMngFacade">
        <sofa:binding/>
        <sofa:binding.tr/>
        <sofa:binding.ws/>
    </sofa:service>

</beans>