/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.sched;

import com.alipay.quot.commons.falcon.api.Component;
import com.alipay.quot.commons.models.Symbol;
import com.alipay.tradequote.common.dal.model.quotresearch.IndexComponentPO;
import com.alipay.tradequote.core.falcon.nimitz.service.IndexComponentService;
import com.alipay.tradequote.core.falcon.screener.AbstractScreenerTest;
import com.alipay.tradequote.core.falcon.screener.config.ScreenConfigService;
import com.alipay.tradequote.core.falcon.screener.indicator.IndicatorFrameService;
import com.alipay.tradequote.core.falcon.screener.model.JobDescriptor;
import com.alipay.tradequote.core.falcon.screener.sched.job.DsBelongJob;
import com.alipay.tradequote.core.falcon.service.plateindex.ComponentService;
import com.alipay.tradequote.meta.service.SymbolGqlService;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.alipay.tradequote.core.falcon.screener.util.Constants.SECTOR_COMP_LIST;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

/**
 * <AUTHOR>
 * @version DsBelongsJobTest.java, v 0.1 2023年08月23日 10:00 lzt
 */
public class DsBelongsJobTest extends AbstractScreenerTest {

    @InjectMocks
    private DsBelongJob dsBelongJob;

    @Mock
    private SymbolGqlService symbolGqlService;

    @Mock
    private IndexComponentService indexComponentService;

    @Mock
    private IndicatorFrameService indicatorFrameService;

    @Mock
    private ScreenConfigService screenConfigService;

    @Mock
    private ComponentService componentService;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        dsBelongJob = new DsBelongJob();
        dsBelongJob.setSymbolGqlService(symbolGqlService);
        dsBelongJob.setIndexComponentService(indexComponentService);
        dsBelongJob.setIndicatorFrameService(indicatorFrameService);
        dsBelongJob.setScreenConfigService(screenConfigService);
        dsBelongJob.setComponentService(componentService);
    }

    @Test
    public void test1() throws Exception {
        String cnsSym1 = "812204.CNS";
        String cnsSym2 = "813704.CNS";

        // 板块
        List<String> sectorSyms = Lists.newArrayList(cnsSym1, cnsSym2);
        List<Symbol.SymbolDO> sectorObjSyms = makeObjSymbols(sectorSyms);
        String sectorGql = "gql=" + String.join(",", sectorSyms);

        ScreenJobContext context = getScreenJobContext(sectorGql);
        doReturn(sectorObjSyms).when(symbolGqlService).querySymbol(sectorGql);
        doReturn(makeCompos(cnsSym1, Lists.newArrayList("300384.SZ", "600346.SH")))
                .when(indexComponentService)
                .queryIndexComponent(cnsSym1, true, null);
        doReturn(makeCompos(cnsSym2, Lists.newArrayList("300937.SZ", "605266.SH")))
                .when(indexComponentService)
                .queryIndexComponent(cnsSym2, true, null);
        doReturn(false)
                .when(screenConfigService)
                .isPlateGraySymbol(anyString());
        dsBelongJob.execute(context);

        doReturn(true)
                .when(screenConfigService)
                .isPlateGraySymbol(anyString());
        doReturn(makeObjComponent(cnsSym1, Lists.newArrayList("300384.SZ", "600346.SH")))
                .when(componentService)
                .queryComponentBySymbol(cnsSym1, null);
        doReturn(makeObjComponent(cnsSym2, Lists.newArrayList("300937.SZ", "605266.SH")))
                .when(componentService)
                .queryComponentBySymbol(cnsSym2, null);

        dsBelongJob.execute(context);
        doReturn(makeObjComponent(cnsSym1, Lists.newArrayList("300384.SZ", "600346.SH")))
                .when(componentService)
                .queryComponentBySymbol(cnsSym1, null);
        doThrow(new RuntimeException())
                .when(componentService)
                .queryComponentBySymbol(cnsSym2, null);

        dsBelongJob.execute(context);
    }

    private ScreenJobContext getScreenJobContext(String gql) {

        JobDescriptor sectorSched = new JobDescriptor();
        sectorSched.setJobName(dsBelongJob.getName());
        sectorSched.setGql(gql);
        Map<String, Object> props = new HashMap<>();
        props.put("relType", SECTOR_COMP_LIST);
        sectorSched.setExtProps(props);
        return ScreenJobContext.builder()
                .jobDescriptor(sectorSched)
                .listTypes(listTypes())
                .build();
    }

    private List<IndexComponentPO> makeCompos(String indexSym, List<String> compSyms) {
        return compSyms
                .stream()
                .map(s -> {
                    IndexComponentPO i = new IndexComponentPO();
                    i.setIndexSymbol(indexSym);
                    i.setComponentSymbol(s);
                    return i;
                })
                .collect(Collectors.toList());

    }

    private Component.ObjComponent makeObjComponent(String plateSymbol, List<String> componentSymbols) {

        List<Component.ComponentDO> collect = componentSymbols.stream()
                .map(s -> Component.ComponentDO.newBuilder().setComponentSymbol(s).setDelStatus(0).build())
                .collect(Collectors.toList());
        collect.add(Component.ComponentDO.newBuilder()
                .setComponentSymbol("600519.SH")
                .setDelStatus(1)
                .build());
        return Component.ObjComponent.newBuilder()
                .addAllComponents(collect)
                .setSymbol(plateSymbol).build();
    }

}