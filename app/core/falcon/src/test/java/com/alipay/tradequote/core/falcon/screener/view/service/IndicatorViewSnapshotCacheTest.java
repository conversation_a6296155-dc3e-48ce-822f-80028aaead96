package com.alipay.tradequote.core.falcon.screener.view.service;

import com.alipay.quot.commons.models.Snapshot;
import com.alipay.quot.commons.push.models.base.NimitzTablePb;
import com.alipay.quote.nerve.qsql.lib.value.Value;
import com.alipay.quote.nerve.qsql.lib.value.primitive.DoubleValue;
import com.alipay.quote.nerve.qsql.lib.value.primitive.LongValue;
import com.alipay.quote.nerve.qsql.lib.viewCell.ViewCell;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ObjIdKeyWrapper;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ScreenCacheKey;
import com.alipay.tradequote.core.falcon.screener.view.cache.ViewCache;
import com.alipay.tradequote.core.falcon.screener.view.drm.ScreenerViewDrm;
import com.alipay.tradequote.core.falcon.screener.view.realtime.IndicatorViewSnapshotCacheUpdate;
import com.alipay.tradequote.core.falcon.screener.view.sched.IndicatorViewAssetScheduler;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import static org.mockito.ArgumentMatchers.any;

public class IndicatorViewSnapshotCacheTest {

    String suffix = "SNAPSHOT_SNAPSHOT_";

    @InjectMocks
    IndicatorViewSnapshotCacheUpdate indicatorViewSnapshotCacheUpdate;

    @Mock
    ViewCache viewCache;

    @Mock
    ScreenerViewDrm screenerViewDrm;

    @Mock
    IndicatorViewAssetScheduler indicatorViewAssetScheduler;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void test(){
        Mockito.when(screenerViewDrm.getViewNameList()).thenReturn(Lists.newArrayList("view_screener"));
        Mockito.when(screenerViewDrm.getSnapshotSuffix()).thenReturn("SNAPSHOT_SNAPSHOT_");
        Mockito.when(viewCache.get(any())).thenReturn(CompletableFuture.completedFuture(null));
        Mockito.when(indicatorViewAssetScheduler.getAliasMap()).thenReturn(new HashMap<>());
        Mockito.when(viewCache.asMap()).thenReturn(new ConcurrentHashMap<>());

        indicatorViewSnapshotCacheUpdate.cacheUpdate(Snapshot.SnapshotDO.newBuilder()
                .setSymbol("002714.SZ")
                .setName("test")
                .setPrice(9089.00)
                .setAmount(12.00)
                .build());

        Mockito.when(viewCache.viewKeySet()).thenReturn(Sets.newHashSet(Sets.newHashSet(new ObjIdKeyWrapper("view_screener","002714.SZ"))));
        Mockito.when(indicatorViewAssetScheduler.getAliasMap()).thenReturn(aliasMap());
        indicatorViewSnapshotCacheUpdate.cacheUpdate(Snapshot.SnapshotDO.newBuilder()
                .setSymbol("002717.SZ")
                .setName("test")
                .setPrice(9089.00)
                .setAmount(12.00)
                .setSnapshotDate(System.currentTimeMillis())
                .build());

        indicatorViewSnapshotCacheUpdate.cacheUpdate(Snapshot.SnapshotDO.newBuilder()
                .setSymbol("002717.SZ")
                .setName("test")
                .setPrice(9089.00)
                .setAmount(12.00)
                .setSnapshotDate(System.currentTimeMillis())
                .build());

        Mockito.when(viewCache.asMap()).thenReturn(viewCacheResInitial());
        indicatorViewSnapshotCacheUpdate.cacheUpdate(Snapshot.SnapshotDO.newBuilder()
                .setSymbol("002714.SZ")
                .setName("test")
                .setPrice(9089.00)
                .setAmount(12.00)
                .setSnapshotDate(System.currentTimeMillis())
                .build());
    }

    private HashMap<String, String> fieldCastMapper(){
        HashMap<String, String> fieldCastMapper = new HashMap<>();
        fieldCastMapper.put("price", "price");
        fieldCastMapper.put("amount", "amount");
        fieldCastMapper.put("snapshot_date", "snapshotDate");
        return fieldCastMapper;
    }

    private ConcurrentMap<@NonNull ScreenCacheKey, @NonNull CompletableFuture<ConcurrentHashMap<String, ViewCell>>> viewCacheResInitial(){
        ConcurrentHashMap<String, ViewCell> viewCellConcurrentHashMap = new ConcurrentHashMap<>();
        viewCellConcurrentHashMap.put("price",ViewCell.builder()
                .value(DoubleValue.of(9089.00))
                .snapshotDate(System.currentTimeMillis())
                .build());

        viewCellConcurrentHashMap.put("amount",ViewCell.builder()
                .value(DoubleValue.of(19089.00))
                .snapshotDate(1702286493854L)
                .build());
        ConcurrentHashMap<ScreenCacheKey, CompletableFuture<ConcurrentHashMap<String, ViewCell>>> res = new ConcurrentHashMap<>();
        ScreenCacheKey key = new ObjIdKeyWrapper("view_screener","002714.SZ");
        res.put(key ,CompletableFuture.completedFuture(viewCellConcurrentHashMap));
        return res;
    }

    private Map<String, String> aliasMap(){
        Map<String, String> map = new HashMap<>();
        map.put(suffix + "price", "price");
        map.put(suffix + "amount", "amount");
        map.put(suffix + "snapshot_date", "snapshot_date");
        return map;
    }
}
