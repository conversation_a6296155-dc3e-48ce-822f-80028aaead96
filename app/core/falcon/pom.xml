<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.alipay.tradequote</groupId>
        <artifactId>tradequote-parent</artifactId>
        <version>1.0.0-20230621</version>
        <relativePath>../../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>tradequote-core-falcon</artifactId>
    <url>http://home.alipay.net</url>
    <description>Alipay tradequote-core-falcon</description>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.alipay.kgengine</groupId>
            <artifactId>kgengine-open-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.ngfekg</groupId>
            <artifactId>ngfekg-common-service-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.ngfe</groupId>
            <artifactId>ngfe-common-service-facade</artifactId>
        </dependency>

        <!-- 在此处添加依赖 -->
        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>commons-model-ng</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>commons-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>quote-nerve</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-csv</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jmh-core</artifactId>
                    <groupId>org.openjdk.jmh</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>org.springframework.core</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>org.springframework.core</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alipay.tradequote</groupId>
            <artifactId>tradequote-common-dal</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alipay.tradequote</groupId>
            <artifactId>tradequote-common-util</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>antlr4-runtime</artifactId>
                    <groupId>org.antlr</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alipay.tradequote</groupId>
            <artifactId>tradequote-core-meta</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-codec</artifactId>
                    <groupId>apache-codec</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alipay.tradequote</groupId>
            <artifactId>tradequote-core-model</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.zoneclient</groupId>
            <artifactId>zoneclient-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.antvip</groupId>
            <artifactId>antvip-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.antvip</groupId>
            <artifactId>antvip-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java-util</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.searchportal</groupId>
            <artifactId>aisearchclient-common-service-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.kondyle</groupId>
            <artifactId>kondyle-matchrecords-lib</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.kondyle</groupId>
            <artifactId>kondyle-dataframe-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.kondyle</groupId>
            <artifactId>kondyle-dataframe-serialize</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jgrapht</groupId>
            <artifactId>jgrapht-core</artifactId>
        </dependency>

        <!-- 如需与Java对象互转，需引入kondyle-dataframe-mapping -->
        <dependency>
            <groupId>com.alibaba.kondyle</groupId>
            <artifactId>kondyle-dataframe-mapping</artifactId>
        </dependency>

        <dependency>
            <groupId>org.ta4j</groupId>
            <artifactId>ta4j-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>quote-hulk</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alipay.zcache</groupId>
                    <artifactId>zcache</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alipay.zcache</groupId>
                    <artifactId>zcache-smartlocalcache</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alipay.simplehbase</groupId>
                    <artifactId>simplehbase</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alipay.consumecenter</groupId>
                    <artifactId>simplehbase-zdal-adapter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alipay.zdal</groupId>
                    <artifactId>zdal-hbase</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.hbase</groupId>
                    <artifactId>hbase</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.hadoop</groupId>
                    <artifactId>hadoop-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.zookeeper</groupId>
                    <artifactId>zookeeper</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.stephenc.high-scale-lib</groupId>
                    <artifactId>high-scale-lib</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.protobuf</groupId>
                    <artifactId>protobuf-java</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.code.findbugs</groupId>
                    <artifactId>jsr305</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>xerces</groupId>
                    <artifactId>xercesImpl</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>hessian</artifactId>
                    <groupId>com.caucho</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>org.springframework.core</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>org.springframework.context</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>org.springframework.asm</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>org.springframework.aop</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>org.springframework.beans</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>org.springframework.expression</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>org.springframework.jdbc</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>org.springframework.transaction</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>org.springframework.orm</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- search-->
        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>quote-search</artifactId>
        </dependency>
        <!-- search-->

        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>scheduler-alipay-sofa-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>quote-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>commons-utils</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.quartz-scheduler</groupId>
                    <artifactId>quartz</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- tbase begin -->
        <dependency>
            <groupId>com.alibaba.tbase</groupId>
            <artifactId>tbase-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>sofa-middleware-log</artifactId>
                    <groupId>com.alipay.sofa.common.log</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hessian</artifactId>
                    <groupId>com.caucho</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.taobao.tair</groupId>
            <artifactId>tair-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.middleware</groupId>
            <artifactId>logger.api</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <dependency>
            <groupId>biz.paluch.redis</groupId>
            <artifactId>lettuce</artifactId>
        </dependency>
        <dependency>
            <groupId>io.reactivex</groupId>
            <artifactId>rxjava</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.xerial.snappy</groupId>
            <artifactId>snappy-java</artifactId>
        </dependency>
        <!-- tbase end -->

        <!-- DRM -->
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>ddcs-alipay-sofa-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa.common</groupId>
            <artifactId>sofa-common-tools</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>hessian</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.lindorm</groupId>
            <artifactId>lindorm-fat-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>tech.tablesaw</groupId>
            <artifactId>tablesaw-core</artifactId>
        </dependency>
        <dependency>
            <groupId>tech.tablesaw</groupId>
            <artifactId>tablesaw-json</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>commons-push-model</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.jooq/jooq -->
        <dependency>
            <groupId>org.jooq</groupId>
            <artifactId>jooq</artifactId>
        </dependency>

        <!--Support OBKV Start-->
        <dependency>
            <groupId>com.alipay.oceanbase</groupId>
            <artifactId>oceanbase-table-hbase</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.oceanbase</groupId>
            <artifactId>oceanbase-table-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>bolt</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!--Support OBKV End-->

        <dependency>
            <groupId>com.googlecode.protobuf-java-format</groupId>
            <artifactId>protobuf-java-format</artifactId>
            <version>1.2</version>
        </dependency>
        <dependency>
            <groupId>com.alipay.quotstore</groupId>
            <artifactId>bus-client-v2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.quotstore</groupId>
            <artifactId>bus-sdk-v2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofamq-client-all</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>io.dropwizard.metrics</groupId>
            <artifactId>metrics-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.github.ben-manes.caffeine/caffeine -->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <dependency>
            <groupId>org.typemeta</groupId>
            <artifactId>funcj-parser</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.4</version>
        </dependency>

        <!--Support Serverless Start-->
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>ark-serverless-alipay-sofa-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofa-serverless-trigger-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>serverless-flytest</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa.platform</groupId>
            <artifactId>sofa-platform-schedule</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.quotemanager</groupId>
            <artifactId>quotemanager-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.quotefixarclick.datacode</groupId>
            <artifactId>quotefixarclick-datacode-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>logicview-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.zlock</groupId>
            <artifactId>zlock</artifactId>
        </dependency>
        <!--Support Serverless End-->
    </dependencies>

</project>
