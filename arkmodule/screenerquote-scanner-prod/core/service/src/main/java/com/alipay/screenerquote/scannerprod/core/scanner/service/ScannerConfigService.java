/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.service;

import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerJobDescriptor;

import java.util.Map;

/**
 * <AUTHOR>
 * @version ScannerConfigService.java, v 0.1 2025年02月14日 16:26 lzt
 */
public interface ScannerConfigService {

    /**
     * 基于taskId获取任务配置，没有则返回null
     * @param taskId
     * @return
     */
    ScannerJobDescriptor getScannerJobDescriptor(String taskId);

    /**
     * 获取所有任务配置
     * @return
     */
    Map<String, ScannerJobDescriptor> getTaskConfigMap();

    /**
     * 获取写入环境
     * @return
     */
    String getWriteEnv();

    /**
     * 获取查询环境
     * @return
     */
    String getQueryEnv();

    /**
     * 是否开启标签写入
     * @return
     */
    boolean isOpenTagWrite();

    /**
     * 是否本地写入
     * @return
     */
    boolean isLocalWriteTag();
}