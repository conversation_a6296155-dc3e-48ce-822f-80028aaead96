/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.integration.screenerprod;

import com.alipay.screenerquote.prod.model.ScreenerStrategyRequestDTO;
import com.alipay.screenerquote.prod.model.ScreenerStrategyResultDTO;
import com.alipay.quot.commons.facade.result.Result;

/**
 * <AUTHOR>
 * @version ScreenerStrategyEngineClient.java, v 0.1 2025年02月24日 20:05 lzt
 */
public interface ScreenerStrategyQueryClient {

    /**
     * 查询选股策略
     * @param request
     * @return
     */
    Result<ScreenerStrategyResultDTO> queryByStrategy(ScreenerStrategyRequestDTO request);
}