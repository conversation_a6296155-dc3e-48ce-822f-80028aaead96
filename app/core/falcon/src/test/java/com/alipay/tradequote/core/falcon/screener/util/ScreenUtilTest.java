/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.util;

import com.alipay.quot.commons.push.models.base.NimitzTablePb;
import com.alipay.quote.nerve.qsql.lib.value.Value;
import com.alipay.quote.nerve.qsql.lib.value.primitive.*;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorDescriptor;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorFrame;
import com.alipay.tradequote.core.falcon.screener.model.JobDescriptor;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR>
 * @version ScreenUtilTest.java, v 0.1 2023年05月25日 09:33 lzt
 */
public class ScreenUtilTest {

    @Test
    public void convertToScreenObjTest() {
        List<String> symbols = Lists.newArrayList("600519.SH",
                "000002.SZ",
                "601318.SH");
        IndicatorFrame indicatorFrame = ScreenUtil.convertToSection(getNimitzTable(), getJobDescriptor(symbols));
        System.out.println(indicatorFrame.getData());
        assertEquals(indicatorFrame.getData().get("PE_TTM"), new StringValue("123"));
        assertEquals(indicatorFrame.getData().get("MKT_CAP"), LongValue.of(123L));
        assertEquals(indicatorFrame.getData().get("PB"), DoubleValue.of(1.1d));
        assertEquals(indicatorFrame.getData().get("PEG"), BooleanValue.of(true));
    }

    @Test
    public void convertToScreenObjTest2() {
        List<String> symbols = Lists.newArrayList("600519.SH",
                "000002.SZ",
                "601318.SH");
        IndicatorFrame indicatorFrame = ScreenUtil.convertToSection(getNullNimitzTable(), getJobDescriptor(symbols));
        System.out.println(indicatorFrame.getData());
        assertEquals(indicatorFrame.getData().get("PE_TTM").getValue(), "Null");
        assertEquals(indicatorFrame.getData().get("MKT_CAP").getValue(), "Null");
        assertEquals(indicatorFrame.getData().get("PB").getValue(), "Null");
        assertEquals(indicatorFrame.getData().get("PEG").getValue(), "Null");
    }

    @Test
    public void convertToScreenObjTest3() {
        List<String> symbols = Lists.newArrayList("600519.SH",
                "000002.SZ",
                "601318.SH");
        IndicatorFrame indicatorFrame = ScreenUtil.convertToSection(getEmptyCellNimitzTable(), getJobDescriptor(symbols));
        System.out.println(indicatorFrame.getData());
        assertEquals(indicatorFrame.getData().get("PE_TTM").getValue(), "Null");
        assertEquals(indicatorFrame.getData().get("MKT_CAP").getValue(), "Null");
        assertEquals(indicatorFrame.getData().get("PB").getValue(), "Null");
        assertEquals(indicatorFrame.getData().get("PEG").getValue(), "Null");
    }

    @Test
    public void convertWithNullValueTest(){
        List<String> symbols = Lists.newArrayList("600519.SH");

        IndicatorFrame indicatorFrame = ScreenUtil.convertWithNullValue(getJobDescriptor(symbols));
        System.out.println(indicatorFrame.getData());
    }


    @Test
    public void object2Value_nullInput_expectNullValueReturned() {
        // Design: Test the case where input is null.
        Object obj = null;
        Value value = BooleanValue.of(true);
        Value result = ScreenUtil.object2Value(obj, value);
        assertEquals(0, result.type());
    }

    @Test
    public void object2Value_booleanInput_expectBooleanValueReturned() {
        // Design: Test the case where input is a boolean.
        Object obj = true;
        Value value = BooleanValue.of(true);
        Value result = ScreenUtil.object2Value(obj, value);
        assertEquals(BooleanValue.of(true), result);
    }

    @Test
    public void object2Value_doubleInput_expectDoubleValueReturned() {
        // Design: Test the case where input is a double.
        Object obj = 1.23d;
        Value value = DoubleValue.of(1.24d);
        Value result = ScreenUtil.object2Value(obj, value);
        assertEquals(DoubleValue.of(1.23d), result);
    }

    @Test
    public void object2Value_longInput_expectLongValueReturned() {
        // Design: Test the case where input is a long.
        Object obj = 123L;
        Value value = LongValue.of(124L);
        Value result = ScreenUtil.object2Value(obj, value);
        assertEquals(LongValue.of(123L), result);
    }

    @Test
    public void object2Value_stringInput_expectStringValueReturned() {
        // Design: Test the case where input is a string.
        Object obj = "Hello";
        Value value = new StringValue("World");
        Value result = ScreenUtil.object2Value(obj, value);
        assertEquals(new StringValue("Hello"), result);
    }

    private JobDescriptor getJobDescriptor(List<String> symbols) {
        String gql = "gql=" + String.join(",", symbols);
        JobDescriptor jobDsp = new JobDescriptor();
        jobDsp.setName("DS_INDICATOR_VIEW_JOB");
        jobDsp.setGql(gql);

        IndicatorDescriptor i1 = new IndicatorDescriptor();
        i1.setName("PE_TTM");
        i1.setField("1");
        IndicatorDescriptor i2 = new IndicatorDescriptor();
        i2.setName("MKT_CAP");
        i2.setField("2");
        IndicatorDescriptor i3 = new IndicatorDescriptor();
        i3.setName("PB");
        i3.setField("3");
        IndicatorDescriptor i4 = new IndicatorDescriptor();
        i4.setName("PEG");
        i4.setField("4");
        i4.setAlias(Lists.newArrayList("PEGGG","GEP"));
        jobDsp.setIndicators(Lists.newArrayList(i1, i2, i3, i4));

        Map<String, Object> map = new HashMap<>();
        map.put("rsDataSet", "INDICATOR");
        map.put("dimKeys", "INDICATOR");
        jobDsp.setExtProps(map);

        return jobDsp;
    }

    private static NimitzTablePb.NimitzTable getNimitzTable() {
        return NimitzTablePb.NimitzTable.newBuilder()
                .addAllColumns(Lists.newArrayList(
                        getColumn("1", "KString", Lists.newArrayList(NimitzTablePb.NimitzCell.newBuilder()
                                .setSVal("123")
                                .build())),
                        getColumn("2", "KInt64", Lists.newArrayList(NimitzTablePb.NimitzCell.newBuilder()
                                .setI64Val(123L)
                                .build())),
                        getColumn("3", "KDouble", Lists.newArrayList(NimitzTablePb.NimitzCell.newBuilder()
                                .setDVal(1.1d)
                                .build())),
                        getColumn("4", "KBoolean", Lists.newArrayList(NimitzTablePb.NimitzCell.newBuilder()
                                .setBVal(true)
                                .build()))))
                .build();
    }

    private static NimitzTablePb.NimitzTable getNullNimitzTable() {
        return NimitzTablePb.NimitzTable.newBuilder()
                .addAllColumns(Lists.newArrayList(
                        getColumn("1", "KString", null),
                        getColumn("2", "KInt64", null),
                        getColumn("3", "KDouble", null),
                        getColumn("4", "KBoolean", null)))
                .build();
    }

    private static NimitzTablePb.NimitzTable getEmptyCellNimitzTable() {
        return NimitzTablePb.NimitzTable.newBuilder()
                .addAllColumns(Lists.newArrayList(
                        getColumn("1", "KString", Lists.newArrayList(NimitzTablePb.NimitzCell.newBuilder().build())),
                        getColumn("2", "KInt64", Lists.newArrayList(NimitzTablePb.NimitzCell.newBuilder().build())),
                        getColumn("3", "KDouble", Lists.newArrayList(NimitzTablePb.NimitzCell.newBuilder().build())),
                        getColumn("4", "KBoolean", Lists.newArrayList(NimitzTablePb.NimitzCell.newBuilder().build()))))
                .build();
    }

    private static NimitzTablePb.NimitzColumn getColumn(String name, String kind, List<NimitzTablePb.NimitzCell> cells) {
        NimitzTablePb.NimitzColumn.Builder builder = NimitzTablePb.NimitzColumn.newBuilder()
                .setName(name)
                .setKind(kind);
        if (CollectionUtils.isNotEmpty(cells)) {
            builder.addAllCells(cells);
        }
        return builder.build();
    }
}