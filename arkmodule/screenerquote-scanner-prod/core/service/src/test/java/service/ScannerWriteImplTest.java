/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package service;

import com.alipay.quot.commons.facade.model.base.VCell;
import com.alipay.screenerquote.prod.model.ScreenerObjDTO;
import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerPortfolio;
import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerWriteConfig;
import com.alipay.screenerquote.scannerprod.core.scanner.service.ScannerConfigService;
import com.alipay.screenerquote.scannerprod.core.scanner.service.impl.ScannerWriteServiceImpl;
import com.alipay.tradequote.core.falcon.service.tag.TagWriteService;
import org.javers.common.collections.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @version ScannerWriteImplTest.java, v 0.1 2025年06月11日 11:27 lzt
 */

public class ScannerWriteImplTest {

    @InjectMocks
    private ScannerWriteServiceImpl scannerWriteService;

    @Mock
    private ScannerConfigService scannerDrm;

    @Mock
    private TagWriteService tagWriteService;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);

        scannerWriteService.setScannerDrm(scannerDrm);
        scannerWriteService.setTagWriteService(tagWriteService);
    }


    @Test
    public void test(){
        when(scannerDrm.isOpenTagWrite()).thenReturn(true);
        when(scannerDrm.isLocalWriteTag()).thenReturn(true);
        ScannerWriteConfig config = ScannerWriteConfig.builder().dest("TAG").build();
        ScreenerObjDTO obj = new ScreenerObjDTO();

        Map<String, VCell> indicators = new HashMap<>();
        indicators.put("tag_operator", VCell.builder().type(VCell.Type.TBoolean).bv(true).build());
        obj.setSymbol("600519.SH");
        obj.setIndicators(indicators);

        ScannerPortfolio portfolio =ScannerPortfolio.builder()
                .strategyId("test")
                .count(1)
                .objCount(1)
                .objs(Lists.asList(obj))
                .build();

        scannerWriteService.write(config, portfolio);
    }

    @Test
    public void test1(){
        when(scannerDrm.isOpenTagWrite()).thenReturn(true);
        when(scannerDrm.isLocalWriteTag()).thenReturn(true);
        ScannerWriteConfig config = ScannerWriteConfig.builder().dest("TAG").tagExpireTime(1000L).build();
        ScreenerObjDTO obj = new ScreenerObjDTO();

        Map<String, VCell> indicators = new HashMap<>();
        indicators.put("tag_operator", VCell.builder().type(VCell.Type.TBoolean).bv(true).build());
        obj.setSymbol("600519.SH");
        obj.setIndicators(indicators);

        ScannerPortfolio portfolio =ScannerPortfolio.builder()
                .strategyId("test")
                .count(1)
                .objCount(1)
                .objs(Lists.asList(obj))
                .build();

        scannerWriteService.write(config, portfolio);
    }

    @Test
    public void test2(){
        when(scannerDrm.isOpenTagWrite()).thenReturn(true);
        when(scannerDrm.isLocalWriteTag()).thenReturn(true);
        ScannerWriteConfig config = ScannerWriteConfig.builder().dest("TAG").build();
        ScreenerObjDTO obj = new ScreenerObjDTO();

        Map<String, VCell> indicators = new HashMap<>();
        obj.setSymbol("600519.SH");
        obj.setIndicators(indicators);

        ScannerPortfolio portfolio =ScannerPortfolio.builder()
                .strategyId("test")
                .count(1)
                .objCount(1)
                .objs(Lists.asList(obj))
                .build();

        scannerWriteService.write(config, portfolio);
    }

    @Test
    public void test4(){
        when(scannerDrm.isOpenTagWrite()).thenReturn(true);
        when(scannerDrm.isLocalWriteTag()).thenReturn(true);
        ScannerWriteConfig config = ScannerWriteConfig.builder().dest("TAG").build();
        ScreenerObjDTO obj = new ScreenerObjDTO();
        obj.setSymbol("600519.SH");

        ScannerPortfolio portfolio =ScannerPortfolio.builder()
                .strategyId("test")
                .count(1)
                .objCount(1)
                .objs(Lists.asList(obj))
                .build();

        scannerWriteService.write(config, portfolio);
    }
}