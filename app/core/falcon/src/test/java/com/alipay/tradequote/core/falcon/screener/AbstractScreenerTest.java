/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.quot.commons.facade.model.SymbolDTO;
import com.alipay.quot.commons.models.Symbol;
import com.alipay.quot.commons.util.SymbolConvert;
import com.alipay.quot.commons.util.symbol.SymbolUtil;
import com.alipay.quote.nerve.qsql.lib.value.Value;
import com.alipay.quote.nerve.qsql.lib.value.primitive.BooleanValue;
import com.alipay.quote.nerve.qsql.lib.value.primitive.DoubleValue;
import com.alipay.quote.nerve.qsql.lib.value.primitive.StringValue;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorFrame;
import com.alipay.tradequote.core.falcon.screener.model.template.ScreenObj;
import com.alipay.tradequote.core.falcon.screener.model.template.strategy.ScreenStrategy;
import com.alipay.tradequote.meta.service.utils.SymbolUtils;
import com.google.common.base.Charsets;
import com.google.common.base.Preconditions;
import com.google.common.collect.Sets;
import com.google.common.io.CharStreams;
import lombok.AllArgsConstructor;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVRecord;
import org.mockito.ArgumentMatcher;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public abstract class AbstractScreenerTest {
    /**
     * 表头
     */
    private final String[] SYMBOL_HEADERS = {
            "symbol",
            "name",
            "market",
            "type",
            "subType",
            "listType"
    };

    /**
     * 从csv获取标的
     */
    public List<SymbolDTO> getObjs(String filename) throws Exception {
        InputStream is = this.getClass().getClassLoader().getResourceAsStream("screener/symbol/" + filename);
        Preconditions.checkNotNull(is);
        Reader reader = new BufferedReader(new InputStreamReader(is));
        Iterable<CSVRecord> records = CSVFormat.DEFAULT
                .withHeader(SYMBOL_HEADERS)
                .withFirstRecordAsHeader()
                .parse(reader);

        List<SymbolDTO> result = new ArrayList<>();
        for (CSVRecord record : records) {
            SymbolDTO s = new SymbolDTO();
            s.setSymbol(getValueOrNull(record, "symbol"));
            s.setName(getValueOrNull(record, "name"));
            s.setMarket(getValueOrNull(record, "market"));
            s.setType(getValueOrNull(record, "type"));
            s.setSubType(getValueOrNull(record, "subType"));
            s.setListType(getValueOrNull(record, "listType"));
            result.add(s);
        }

        return result;
    }

    public ScreenStrategy getStrategy(String filename) throws Exception {
        InputStream is = this.getClass().getClassLoader().getResourceAsStream("screener/strategy/" + filename);
        Preconditions.checkNotNull(is);
        return JSON.parseObject(is, ScreenStrategy.class);
    }

    @SuppressWarnings("UnstableApiUsage")
    public List<ScreenObj> getScreenObjs(String filename) throws Exception {
        InputStream is = this.getClass().getClassLoader().getResourceAsStream("screener/screenObj/" + filename);
        Preconditions.checkNotNull(is);
        String s = CharStreams.toString(new InputStreamReader(is, Charsets.UTF_8));
        JSONArray jsonArr = JSON.parseArray(s);
        List<ScreenObj> objs = new ArrayList<>();
        for (Object obj : jsonArr) {
            JSONObject jsonObj = (JSONObject) obj;
            String sym = jsonObj.getString("symbol");
            String name = jsonObj.getString("name");
            JSONArray indicator = jsonObj.getJSONArray("indicators");
            Map<String, Value> indicatorValues = getScreenObjIndicators(indicator);
            IndicatorFrame frame = new IndicatorFrame(indicatorValues);
            ScreenObj screenObj = new ScreenObj(sym, name);
            screenObj.setFrame(frame);
            objs.add(screenObj);
        }

        return objs;
    }

    public Map<String, Value> getScreenObjIndicators(JSONArray arr) {
        Map<String, Value> result = new HashMap<>();
        for (Object obj : arr) {
            JSONObject jsonObj = (JSONObject) obj;
            String key = jsonObj.getString("key");
            JSONObject valueObj = jsonObj.getJSONObject("value");
            String valueType = valueObj.getJSONObject("type").getString("name");
            Value value;
            switch (valueType) {
                case "TString":
                    value = new StringValue(valueObj.getString("sv"));
                    break;
                case "TLong":
                    value = new DoubleValue(valueObj.getLongValue("lv"));
                    break;
                case "TDouble":
                    value = new DoubleValue(valueObj.getDoubleValue("dv"));
                    break;
                case "TBoolean":
                    boolean b = valueObj.getBoolean("bv");
                    value = BooleanValue.of(b);
                    break;
                default:
                    throw new IllegalStateException();
            }
            result.put(key, value);
        }

        return result;
    }

    public String getValueOrNull(CSVRecord record, String field) {
        String value = record.get(field);
        if (value == null) {
            return null;
        }

        return Objects.equals("null", value) ? null : value;
    }

    public Symbol.SymbolDO makeObjSymbol(String symbol) {
        return Symbol.SymbolDO.newBuilder()
                .setSymbol(symbol)
                .setAliSymbol(symbol)
                .setChannelLevel("L1")
                .setChannelExchange(SymbolUtil.getMarket(symbol))
                .setName(symbol)
                .build();
    }

    public List<Symbol.SymbolDO> makeObjSymbols(List<String> symbols) {
        return symbols.stream().map(this::makeObjSymbol).collect(Collectors.toList());
    }

    public List<SymbolDTO> makeObjSymbolDTOs(List<String> syms) {
        return makeObjSymbols(syms)
                .stream()
                .map(SymbolConvert::convert2JavaSymbolDTO)
                .collect(Collectors.toList());
    }

    public Set<String> listTypes() {
        return Sets.newHashSet("ListType_D", "ListType_F");
    }

    /**
     * matcher
     */
    @AllArgsConstructor
    public static class SymbolMatcher implements ArgumentMatcher<List<String>> {
        private final Symbol.SymbolDO objSymbol;

        @Override
        public boolean matches(List<String> argument) {
            Preconditions.checkArgument(argument != null);
            Preconditions.checkArgument(argument.size() == 1);
            String oic = SymbolUtils.generateInnerCode(objSymbol);
            return StringUtil.equals(oic, argument.get(0));
        }
    }
}
