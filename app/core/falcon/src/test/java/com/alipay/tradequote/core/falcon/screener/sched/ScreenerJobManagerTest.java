/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.sched;

import com.alipay.quote.hulk.thread.Coordinator;
import com.alipay.tradequote.core.falcon.screener.ScreenRepoService;
import com.alipay.tradequote.core.falcon.screener.model.JobDescriptor;
import com.alipay.tradequote.meta.service.ScreenerTriggerService;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class ScreenerJobManagerTest {
    @InjectMocks
    ScreenerJobManager screenerJobManager;

    @Mock
    ScreenRepoService screenRepoService;

    @Mock
    ScreenerTriggerService screenerTriggerService;

    @Mock
    Coordinator screenJobExecuteCoordinator;

    @Mock
    ScreenJobTrigger screenJobTrigger;

    @Before
    public void init() {
        screenerJobManager = new ScreenerJobManager();
        screenerJobManager.setScreenJobTrigger(screenJobTrigger);
        screenerJobManager.setScreenerTriggerService(screenerTriggerService);
        screenerJobManager.setScreenRepoService(screenRepoService);
        screenerJobManager.setScreenJobExecuteCoordinator(screenJobExecuteCoordinator);
    }

    @Test
    public void test1() {
        ScreenJob mockJob = mock(ScreenJob.class);
        doReturn("test").when(mockJob).getName();
        screenerJobManager.setJobs(Lists.newArrayList(mockJob));
        screenerJobManager.setAppName("screenerquote");
        screenerJobManager.init();
        verify(screenerTriggerService, times(1)).register(screenerJobManager);
    }

    @Test
    public void test2() {
        ScreenJob mockJob1 = mock(ScreenJob.class);
        ScreenJob mockJob2 = mock(ScreenJob.class);
        doReturn("job1").when(mockJob1).getName();
        doReturn("job2").when(mockJob2).getName();

        JobDescriptor dsp1 = new JobDescriptor();
        dsp1.setJobName("job1");
        JobDescriptor dsp2 = new JobDescriptor();
        dsp2.setJobName("job2");
        JobDescriptor dspNotFound = new JobDescriptor();
        dspNotFound.setJobName("jobNotFound");
        doReturn(Lists.newArrayList(dsp1, dsp2, dspNotFound))
                .when(screenRepoService)
                .getSchedDescriptors();

        screenerJobManager.setJobs(Lists.newArrayList(mockJob1, mockJob2));
        screenerJobManager.init();
        screenerJobManager.initExecute();

        verify(mockJob1, times(1)).execute(any());
        verify(mockJob2, times(1)).execute(any());
    }

    @Test
    public void test3() {
        ScreenJob mockJob1 = mock(ScreenJob.class);
        ScreenJob mockJob2 = mock(ScreenJob.class);
        doReturn("job1").when(mockJob1).getName();
        doReturn("job2").when(mockJob2).getName();

        JobDescriptor dsp1 = new JobDescriptor();
        dsp1.setJobName("job1");
        JobDescriptor dsp2 = new JobDescriptor();
        dsp2.setJobName("job2");
        JobDescriptor dspNotFound = new JobDescriptor();
        dspNotFound.setJobName("jobNotFound");
        doReturn(Lists.newArrayList(dsp1, dsp2, dspNotFound))
                .when(screenRepoService)
                .getSchedDescriptors();

        doReturn(true).when(screenJobTrigger).isTriggered(eq(dsp1));
        doReturn(false).when(screenJobTrigger).isTriggered(eq(dsp2));
        ThreadPoolExecutor mockPool = mock(ThreadPoolExecutor.class);
        doReturn(mockPool).when(screenJobExecuteCoordinator).executor();

        screenerJobManager.setJobs(Lists.newArrayList(mockJob1, mockJob2));
        screenerJobManager.init();
        screenerJobManager.execute();

        verify(mockPool, times(1)).execute(any());
    }
    
    @Test
    public void test4() {
        List<String> apps = Lists.newArrayList(
                "tradequote",
                "fundquote",
                "quotewrite",
                "ltquote",
                "ltwrite",
                "nimitzquote"
        );

        for (String app : apps) {
            ScreenJob mockJob = mock(ScreenJob.class);
            doReturn("test").when(mockJob).getName();
            screenerJobManager.setJobs(Lists.newArrayList(mockJob));
            screenerJobManager.setAppName(app);
            screenerJobManager.init();
            verify(screenerTriggerService, times(0)).register(screenerJobManager);
        }
    }
}
