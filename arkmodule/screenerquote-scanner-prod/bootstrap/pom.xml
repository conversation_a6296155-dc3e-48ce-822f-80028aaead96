<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>screenerquote-scanner-prod</artifactId>
        <groupId>com.alipay.scannerprod</groupId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>screenerquote-scanner-prod-bootstrap</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.alipay.scannerprod</groupId>
            <artifactId>screenerquote-scanner-prod-biz-service-impl</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofa-boot-alipay-runtime</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>ddcs-alipay-sofa-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>mobilegw-alipay-sofa-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>mq-alipay-sofa-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>rpc-alipay-sofa-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofamq-alipay-sofa-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofa-serverless-trigger-starter</artifactId>
            <!--最新推荐版本见https://yuque.antfin.com/skbeoq/sofa-serverless-trigger/cozynf -->
            <version>${sofa.serverless.trigger.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-ark-maven-plugin</artifactId>
                <version>2.2.13</version>
                <executions>
                    <execution>
                        <id>default-cli</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <skipArkExecutable>true</skipArkExecutable>
                    <outputDirectory>../target</outputDirectory>
                    <bizName>screenerquote-scanner-prod</bizName>
                    <finalName>${project.artifactId}-${project.version}-${timestamp}</finalName>
                    <bizVersion>${project.version}-${timestamp}</bizVersion>
                    <!--模块所有http服务的根目录-->
                    <webContextPath>/screenerquote-scanner-prod</webContextPath>
                	<packExcludesUrl>https://linglong.alipay.com/api/rest/app/queryAppJarBlackList?app=screenerquote</packExcludesUrl>
                	<baseDir>../..</baseDir>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <version>1.4</version>
                <configuration>
                    <timestampFormat>yyyyMMddHHmmss</timestampFormat>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>create-timestamp</goal>
                        </goals>
                    </execution>
                </executions>
                <inherited>false</inherited>
            </plugin>

            <plugin>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>ark-module-build-plugin</artifactId>
                <configuration>
                    <!--                    构建产物大小限制，默认5MB-->
                    <finalJarMaxSizeMB>5</finalJarMaxSizeMB>
                    <!--                    依赖检查结果输出到指定文件，默认不输出-->
                    <!--                    <depCheckResultToFile>result.log</depCheckResultToFile>-->
                    <!--                    跳过该插件执行，默认不跳过-->
                    <!--                    <skip>true</skip>-->
                    <!--                    忽略运行中素有错误，默认不忽略-->
                    <!--                    <ignoreError>true</ignoreError>-->
                    <!--                    配置依赖检查中可以忽略的artifactId关键字，默认空-->
                    <!--                    <excludes>-->
                    <!--                        <exclude>xxx</exclude>-->
                    <!--                    </excludes>-->
                </configuration>
                <executions>
                    <execution>
                        <id>scan-dependency-with-compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>scanDep</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>check-pmd-with-validate</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>pmd</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>check-dependency-with-package</id>
                        <phase>package</phase>
                        <goals>
                            <goal>check-dependencies</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>

</project>
