/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.template.impl;

import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class Joiner {
    /**
     * 对一个集合进行数据补充（左连接）
     *
     * @param leftCollection   左表数据（需要被补充的数据）
     * @param leftKeyExtractor 从左表元素提取 join key 的函数
     * @param rightBatchFetcher 根据左表的一批 key，批量获取右表数据的函数
     * @param mergeFunction     当左右元素匹配时，如何将它们合并成最终结果的函数
     * @param <L> 左表元素类型
     * @param <R> 右表元素类型
     * @param <K> Join Key 的类型
     * @param <O> 输出结果的元素类型
     * @return 合并后的结果列表
     */
    public static <L, R, K, O> List<O> leftJoin(
            Collection<L> leftCollection,
            Function<L, K> leftKeyExtractor,
            Function<List<K>, Map<K, R>> rightBatchFetcher,
            BiFunction<L, R, O> mergeFunction
    ) {
        if (leftCollection == null || leftCollection.isEmpty()) {
            return Collections.emptyList();
        }

        // 1. 从左表提取所有 keys
        List<K> keys = leftCollection.stream()
                .map(leftKeyExtractor)
                .distinct()
                .collect(Collectors.toList());

        // 2. 将右表数据转为 Map<Key, RightElement> 以便快速查找
        Map<K, R> rightMap = rightBatchFetcher.apply(keys);

        // 3. 遍历左表，与右表 map 进行匹配和合并
        return leftCollection.stream()
                .map(leftElement -> {
                    K key = leftKeyExtractor.apply(leftElement);
                    R rightElement = rightMap.get(key);
                    if (rightElement != null) {
                        return mergeFunction.apply(leftElement, rightElement);
                    }
                    // 根据业务需要，决定未匹配到的左表元素是丢弃还是保留
                    // 这里我们选择丢弃，也可以修改 mergeFunction 接收一个 nullable 的 rightElement
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
