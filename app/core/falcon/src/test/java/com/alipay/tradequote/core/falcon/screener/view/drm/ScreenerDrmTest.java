package com.alipay.tradequote.core.falcon.screener.view.drm;

import com.alibaba.fastjson.JSONObject;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;

import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

public class ScreenerDrmTest {

    @Test
    public void test(){
        ScreenerViewDrm screenerViewDrm = new ScreenerViewDrm("screenerquote");

        screenerViewDrm.setViewNameStr("view_screener, view_indicator");
        Assert.assertNotNull(screenerViewDrm.getViewNameStr());

        screenerViewDrm.setSnapshotSuffix("Test");
        Assert.assertNotNull(screenerViewDrm.getSnapshotSuffix());

        screenerViewDrm.setIndexVersionDate("biz_date");
        Assert.assertNotNull(screenerViewDrm.getIndexVersionDate());

        screenerViewDrm.setViewCacheInvalid("test");

        screenerViewDrm.setScreenerTriggerLogicView(true);
        screenerViewDrm.isScreenerTriggerLogicView();

        screenerViewDrm.setCompareLogicViewSwitch("true");
        screenerViewDrm.setCompareLogicViewSwitch("");
        screenerViewDrm.getCompareLogicViewSwitch();

        screenerViewDrm.setbCompareLogicViewSwitch(false);
        screenerViewDrm.isbCompareLogicViewSwitch();

        screenerViewDrm.setPrintLogicViewValue("true");
        screenerViewDrm.setPrintLogicViewValue("");
        screenerViewDrm.getPrintLogicViewValue();

        screenerViewDrm.setViewFieldCompareWhiteListStr("test");
        screenerViewDrm.setViewFieldCompareWhiteListStr("");
        screenerViewDrm.setViewFieldCompareWhiteListStr("{\"test\":[\"a\", \"b\"]}");
        screenerViewDrm.getViewFieldCompareWhiteListStr();

        screenerViewDrm.setViewFieldCompareWhiteMap(new HashMap<>());
        screenerViewDrm.getViewFieldCompareWhiteMap();

        screenerViewDrm.setViewDataTypeWhiteListStr("list");
        screenerViewDrm.setViewDataTypeWhiteListStr("");
        screenerViewDrm.setViewDataTypeWhiteListStr("{\"test\":[\"a\", \"b\"]}");
        screenerViewDrm.getViewFieldCompareWhiteListStr();

        screenerViewDrm.setViewDataTypeWhiteList(new ArrayList<>());
        screenerViewDrm.getViewDataTypeWhiteList();

        screenerViewDrm.setViewFieldNameMapStr("list");
        screenerViewDrm.setViewFieldNameMapStr("");
        screenerViewDrm.setViewFieldNameMapStr("{\"test\":\"a\"}");
        screenerViewDrm.getViewFieldNameMapStr();

        screenerViewDrm.setViewFieldNameMap(new HashMap<>());
        screenerViewDrm.getViewFieldNameMap();

    }


}
