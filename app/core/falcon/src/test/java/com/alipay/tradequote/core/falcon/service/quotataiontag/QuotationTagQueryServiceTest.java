/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.service.quotataiontag;

import com.alipay.quot.commons.models.TagProto;
import com.alipay.quote.hulk.model.HulkCacheValue;
import com.alipay.tradequote.core.falcon.drm.QuotationTagConfigService;
import com.alipay.tradequote.core.falcon.model.DataWrapper;
import com.alipay.tradequote.core.falcon.service.quotationtag.QuotationTagQueryServiceImpl;
import com.alipay.tradequote.core.falcon.storage.client.StorageClient;
import com.alipay.tradequote.integration.ha3.Ha3SearchClient;
import com.google.protobuf.ByteString;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.internal.util.collections.Sets;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @version QuotationTagQueryServiceTest.java, v 0.1 2023年10月16日 19:10 lzt
 */
public class QuotationTagQueryServiceTest {

    @InjectMocks
    private QuotationTagQueryServiceImpl quotationTagQueryService;

    @Mock
    private StorageClient storageClient;

    @Mock
    private Ha3SearchClient tagHa3SearchClient;

    @Mock
    private QuotationTagConfigService quotationTagConfigService;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        quotationTagQueryService = new QuotationTagQueryServiceImpl();
        quotationTagQueryService.setStorageClient(storageClient);
        quotationTagQueryService.setStorageKey("QUOTATION_TAG_KV");
        quotationTagQueryService.setTagHa3SearchClient(tagHa3SearchClient);
        quotationTagQueryService.setQuotationTagConfigService(quotationTagConfigService);
    }

    @Test
    public void queryTags() throws Exception {
        when(quotationTagConfigService.isOpenSearchQuery()).thenReturn(true);
        when(quotationTagConfigService.isOpenHulkQuery()).thenReturn(false);
        when(quotationTagConfigService.getObjIdsBlackSet()).thenReturn(Sets.newSet("600519.SH"));
        when(tagHa3SearchClient.search(any())).thenReturn(null);
        boolean isException = false;
        try {
            quotationTagQueryService.queryTags(Collections.singletonList("600519.SH"), Collections.singletonList("new_high"), "namespace");
        } catch (Exception e) {
            isException = true;
        }
        assertTrue(isException);
    }

    @Test
    public void queryTags1() throws Exception {
        when(quotationTagConfigService.isOpenSearchQuery()).thenReturn(true);
        when(quotationTagConfigService.isOpenHulkQuery()).thenReturn(false);
        when(tagHa3SearchClient.search(any())).thenReturn(null);
        boolean isException = false;
        try {
            quotationTagQueryService.queryTags(Collections.singletonList("600519.SH"), Collections.singletonList("new_high"), "namespace");
        } catch (Exception e) {
            isException = true;
        }
        assertTrue(isException);
    }

    @Test
    public void queryTags2() throws Exception {
        when(quotationTagConfigService.isOpenSearchQuery()).thenReturn(true);
        when(quotationTagConfigService.isOpenHulkQuery()).thenReturn(false);
        when(tagHa3SearchClient.search(any())).thenThrow(new RuntimeException());
        boolean isException = false;
        try {
            quotationTagQueryService.queryTags(Collections.singletonList("600519.SH"), Collections.singletonList("new_high"), "namespace");
        } catch (Exception e) {
            isException = true;
        }
        assertTrue(isException);
    }

    @Test
    public void queryTags3() throws Exception {
        when(quotationTagConfigService.isOpenSearchQuery()).thenReturn(false);
        when(quotationTagConfigService.isOpenHulkQuery()).thenReturn(true);
        when(storageClient.mget(any(), any())).thenReturn(getHulkValueList());
        when(quotationTagConfigService.getPartitionCount()).thenReturn(200);
        List<TagProto.Tag> tags = quotationTagQueryService.queryTags(Collections.singletonList("600519.SH"), Collections.singletonList("new_high"), "namespace");
        assertEquals(1, tags.size());
        assertEquals("new_high", tags.get(0).getTagCode());
    }

    @Test
    public void queryTags4() throws Exception {
        when(quotationTagConfigService.isOpenSearchQuery()).thenReturn(false);
        when(quotationTagConfigService.isOpenHulkQuery()).thenReturn(true);
        when(storageClient.mget(any(), any())).thenThrow(new RuntimeException());
        when(quotationTagConfigService.getPartitionCount()).thenReturn(200);
        boolean isException = false;
        try {
            quotationTagQueryService.queryTags(Collections.singletonList("600519.SH"), Collections.singletonList("new_high"), "namespace");
        } catch (Exception e) {
            isException = true;
        }
        assertTrue(isException);
    }

    @Test
    public void queryTags5() {
        when(quotationTagConfigService.isOpenSearchQuery()).thenReturn(false);
        when(quotationTagConfigService.isOpenHulkQuery()).thenReturn(false);
        boolean isException = false;
        try {
            quotationTagQueryService.queryTags(Collections.singletonList("600519.SH"), Collections.singletonList("new_high"), "namespace");
        } catch (Exception e) {
            isException = true;
        }
        assertTrue(isException);
    }

    @Test
    public void queryTags6() throws Exception {
        when(quotationTagConfigService.isOpenSearchQuery()).thenReturn(false);
        when(quotationTagConfigService.isOpenHulkQuery()).thenReturn(true);
        when(storageClient.mget(any(), any())).thenReturn(getWrongHulkValueList());
        when(quotationTagConfigService.getPartitionCount()).thenReturn(200);
        boolean isException = false;
        try {
            quotationTagQueryService.queryTags(Collections.singletonList("600519.SH"), Collections.singletonList("new_high"), "namespace");
        } catch (Exception e) {
            isException = true;
        }
        assertTrue(isException);
    }


    private List<HulkCacheValue> getHulkValueList() {
        TagProto.Tag tag = TagProto.Tag.newBuilder()
                .setTagCode("new_high")
                .setObjId("600519.SH")
                .setBeginTime(1L)
                .setEndTime(9697521175176L)
                .setBizTime(1L)
                .setIsValid(true)
                .setName("name")
                .setExtProps("extProps")
                .setRid("<EMAIL>")
                .build();

        TagProto.Tag tag1 = TagProto.Tag.newBuilder()
                .setTagCode("new_low")
                .setObjId("600519.SH")
                .setBeginTime(1L)
                .setEndTime(697521175176L)
                .setBizTime(1L)
                .setIsValid(true)
                .setName("name")
                .setExtProps("extProps")
                .setRid("<EMAIL>")
                .build();

        TagProto.Tag tag2 = TagProto.Tag.newBuilder()
                .setTagCode("new_low")
                .setObjId("600510.SH")
                .setBeginTime(1L)
                .setEndTime(9697521175176L)
                .setBizTime(1L)
                .setIsValid(false)
                .setName("name")
                .setExtProps("extProps")
                .setRid("<EMAIL>")
                .build();

        HulkCacheValue value = DataWrapper.builder().data(tag.toByteArray()).hulkTime(1L).build();
        HulkCacheValue value1 = DataWrapper.builder().data(tag1.toByteArray()).hulkTime(1L).build();
        HulkCacheValue value2 = DataWrapper.builder().data(tag2.toByteArray()).hulkTime(1L).build();
        return Arrays.asList(value, value1, value2);
    }

    private List<HulkCacheValue> getWrongHulkValueList() {
        HulkCacheValue value = DataWrapper.builder().data(ByteString.copyFrom(new byte[] {0}).toByteArray()).hulkTime(1L).build();
        return Collections.singletonList(value);
    }

}