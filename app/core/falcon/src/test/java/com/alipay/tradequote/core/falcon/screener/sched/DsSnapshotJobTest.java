/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.sched;

import com.alipay.quot.commons.models.BaseSnapshot;
import com.alipay.quot.commons.models.Symbol;
import com.alipay.tradequote.core.falcon.screener.AbstractScreenerTest;
import com.alipay.tradequote.core.falcon.screener.indicator.IndicatorFrameService;
import com.alipay.tradequote.core.falcon.screener.indicator.IndicatorFrameServiceImpl;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ScreenCacheImpl;
import com.alipay.tradequote.core.falcon.screener.model.DataType;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorDescriptor;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorFrame;
import com.alipay.tradequote.core.falcon.screener.model.JobDescriptor;
import com.alipay.tradequote.core.falcon.screener.sched.job.DsSnapshotJob;
import com.alipay.tradequote.core.falcon.service.snapshot.FalconSnapshotService;
import com.alipay.tradequote.meta.service.SymbolGqlService;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.Assert.*;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
public class DsSnapshotJobTest extends AbstractScreenerTest {
    @InjectMocks
    DsSnapshotJob dsSnapshotJob;

    @Mock
    SymbolGqlService symbolGqlService;

    @Mock
    FalconSnapshotService falconSnapshotService;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        dsSnapshotJob = new DsSnapshotJob();
        dsSnapshotJob.setSymbolGqlService(symbolGqlService);
        dsSnapshotJob.setSnapshotService(falconSnapshotService);
    }

    @Test
    public void test1() {
        assertEquals("DS_SNAPSHOT_JOB", dsSnapshotJob.getName());
    }


    @Test
    public void test2() throws Exception {
        List<String> symbols = Lists.newArrayList("600519.SH",
                "000002.SZ",
                "601318.SH");
        String gql = "gql=" + String.join(",", symbols);
        JobDescriptor jobDsp = new JobDescriptor();
        jobDsp.setName("DS_SYMBOL_JOB");
        jobDsp.setGql(gql);
        ScreenJobContext context = ScreenJobContext.builder()
                .jobDescriptor(jobDsp)
                .listTypes(listTypes())
                .build();

        IndicatorFrameService frameService = Mockito.mock(IndicatorFrameService.class);
        dsSnapshotJob.setIndicatorFrameService(frameService);
        List<Symbol.SymbolDO> objSymbols = makeObjSymbols(symbols);
        doReturn(objSymbols).when(symbolGqlService).querySymbol(gql);
        doReturn(null).when(falconSnapshotService).mgetLatestQuotation(anyList());

        dsSnapshotJob.execute(context);
        verify(symbolGqlService, Mockito.times(1)).querySymbol(anyString());
        verify(symbolGqlService, Mockito.times(1)).querySymbol(gql);
        verify(falconSnapshotService, Mockito.times(3)).mgetLatestQuotation(anyList());
        verify(frameService, Mockito.times(0)).putFrame(any(), anyString(), any());
    }

    @Test
    public void test3() throws Exception {
        List<String> symbols = Lists.newArrayList("600519.SH",
                "000002.SZ",
                "601318.SH");
        List<Symbol.SymbolDO> objSymbols = makeObjSymbols(symbols);
        String gql = "gql=" + String.join(",", symbols);
        JobDescriptor jobDsp = new JobDescriptor();
        jobDsp.setName(DataType.DS_BASE_INDICATOR);
        jobDsp.setGql(gql);
        IndicatorDescriptor i1 = new IndicatorDescriptor();
        i1.setName("PRICE");
        i1.setField("price");
        IndicatorDescriptor i2 = new IndicatorDescriptor();
        i2.setName("CHANGE_PERCENT");
        i2.setField("changePercent");

        jobDsp.setIndicators(Lists.newArrayList(i1, i2));
        ScreenJobContext context = ScreenJobContext.builder()
                .jobDescriptor(jobDsp)
                .listTypes(listTypes())
                .build();

        ScreenCacheImpl screenCache = new ScreenCacheImpl();
        screenCache.init();
        IndicatorFrameServiceImpl indicatorFrameService = new IndicatorFrameServiceImpl();
        indicatorFrameService.setScreenCache(screenCache);
        dsSnapshotJob.setIndicatorFrameService(indicatorFrameService);

        doReturn(objSymbols).when(symbolGqlService).querySymbol(gql);
        doReturn(Lists.newArrayList(BaseSnapshot.BaseSnapshotDO.newBuilder()
                .setPrice(1024.2)
                .setChangePercent(0.01)
                .build()))
                .when(falconSnapshotService)
                .mgetLatestQuotation(argThat(new SymbolMatcher(objSymbols.get(0))));
        doReturn(Lists.newArrayList(BaseSnapshot.BaseSnapshotDO.newBuilder()
                .setPrice(1024.2)
                .setChangePercent(0.01)
                .build()))
                .when(falconSnapshotService)
                .mgetLatestQuotation(argThat(new SymbolMatcher(objSymbols.get(1))));
        doThrow(new RuntimeException())
                .when(falconSnapshotService)
                .mgetLatestQuotation(argThat(new SymbolMatcher(objSymbols.get(2))));

        dsSnapshotJob.execute(context);

        verify(symbolGqlService, Mockito.times(1)).querySymbol(anyString());
        verify(symbolGqlService, Mockito.times(1)).querySymbol(gql);
        verify(falconSnapshotService, Mockito.times(3)).mgetLatestQuotation(anyList());

        IndicatorFrame frame = indicatorFrameService.getFrame(DataType.DS_SNAPSHOT, objSymbols.get(0).getAliSymbol());
        assertNotNull(frame);
        assertTrue(frame.getData().containsKey("PRICE"));
        assertTrue(frame.getData().containsKey("CHANGE_PERCENT"));
        assertEquals(1024.2, frame.getData().get("PRICE").asDouble(), 0.001d);
        assertEquals(0.01, frame.getData().get("CHANGE_PERCENT").asDouble(), 0.001d);

        frame = indicatorFrameService.getFrame(DataType.DS_SNAPSHOT, objSymbols.get(1).getAliSymbol());
        assertNotNull(frame);
        assertTrue(frame.getData().containsKey("PRICE"));
        assertTrue(frame.getData().containsKey("CHANGE_PERCENT"));
        assertEquals(1024.2, frame.getData().get("PRICE").asDouble(), 0.001d);
        assertEquals(0.01, frame.getData().get("CHANGE_PERCENT").asDouble(), 0.001d);
    }
}
