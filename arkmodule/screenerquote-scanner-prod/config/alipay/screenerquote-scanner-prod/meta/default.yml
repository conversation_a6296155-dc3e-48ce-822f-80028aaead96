version: 1.0
configs:
  # 基础配置
  #   alios(可选，默认6u2) 基础镜像版本 6u2/7u2
  #   lang(可选，默认zh_CN.GB18030) 服务器编码 zh_CN.UTF-8/zh_CN.GB18030
  #   timezone（可选，默认ShangHai） 服务器时区 LosAngeles/ShangHai/UTC
  #   techstack(可选，默认sofa) 技术栈 sofa/sofaboot/chair/sofaserverless
  basics:
    alios: 7u2
    lang: zh_CN.UTF-8
    timezone: Asia/Shanghai
    techstack: sofaserverless
  # 软件配置
  softwares:
  # name 软件名
  # version 软件版本
  - name: maven
    version: 3.8.5
  - name: jdk
    version: 8_5_9-b245-dep
  # SOFA动态模块配置（可选，基座配置base，组件配置module）
  #   type 类型 base（基座）/module（模块）
  #   base 基座配置
  #     name 基座名称
  #     version 基座版本
  #   module 模块配置
  #     baseName 基座名称
  #     baseVersion 基座版本
  #     type 模块类型，目前只有jar
  #     name 模块名称
  #     version 模块版本
  sofaArk:
    type: module
    module:
      baseName: tradequote
      baseServiceGroups:
        - tradequote
      type: jar
      name: screenerquote-scanner-prod
      version: 1.0.0
