/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.model;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version ScannerTaskConfig.java, v 0.1 2025年02月14日 16:57 lzt
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScannerJobDescriptor {

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 任务类型,与job中的type对应
     */
    private String taskType;

    /**
     * 是否停止
     */
    private boolean stop;

    /**
     * 执行时间列表
     */
    private List<String> cronList;

    /**
     * 是否仅交易日执行
     */
    private boolean onlyTradingDay;

    /**
     * 策略id
     */
    private String strategyId;

    /**
     * 起始标的
     */
    private String srcId;

    /**
     * 扩展信息
     */
    private List<String> extInfo;

    /**
     * 归档周期
     * 目前仅支持P_Day1/P_Min1
     */
    private ScannerPeriodEnum period;

    /**
     * 输出配置,可为空，为空则默认输出到zsearch
     */
    private List<ScannerWriteConfig> writeConfigs;
}