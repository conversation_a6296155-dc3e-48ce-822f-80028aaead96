/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.model;

import com.alipay.quot.commons.facade.model.base.VCell;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @version ScannerObjGroupDTO.java, v 0.1 2025年02月27日 10:22 lzt
 */
@Getter
@Setter
@ToString
@Builder
public class ScannerObjGroupDTO implements Serializable {
    private static final long serialVersionUID = 3941934627745040376L;

    /**
     * group Id，标的或者其他唯一id
     */
    private String groupId;

    /**
     * obj在分组的顺序
     */
    private Integer ordinal;

    /**
     * obj在分组的百分位
     */
    private Double percentile;

    /**
     * 其他obj在分组的属性
     */
    private Map<String, VCell> extProps;
}