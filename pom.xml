<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.alipay.sofa</groupId>
        <artifactId>sofaboot-alipay-dependencies</artifactId>
        <version>3.19.0</version>
    </parent>
    <groupId>com.alipay.tradequote</groupId>
    <artifactId>tradequote-parent</artifactId>
    <version>1.0.0-20230621</version>
    <packaging>pom</packaging>
    <properties>
        <java.version>1.8</java.version>
        <surefire.version>2.20</surefire.version>
        <tradequote.version>1.0.0-20230621</tradequote.version>
        <quotcommons.version>1.0.0.20250725-SNAPSHOT</quotcommons.version>
        <!--拆分commons-model、commons-push-model版本，后续不再变化-->
        <commonsmodel.version>1.0.0.20250724</commonsmodel.version>
        <rocksdb.groupId>org.rocksdb</rocksdb.groupId>
        <rocksdb.artifactId>rocksdbjni</rocksdb.artifactId>
        <rocksdb.version>5.15.10</rocksdb.version>
        <druid-version>1.1.9</druid-version>
        <testFailureIgnore>true</testFailureIgnore>
        <isSkipUnitTest>false</isSkipUnitTest>
        <isSkipIntegrationTest>false</isSkipIntegrationTest>
        <!--Support OBKV Start-->
        <ob.table.client.version>1.9.12</ob.table.client.version>
        <ob.table.hbase.version>1.3.12</ob.table.hbase.version>
        <!--Support OBKV end-->
        <sofamq.client.version>*******</sofamq.client.version>

        <!--Support Serverless Start-->
        <sofa.ark.serverless.version>4.8.7</sofa.ark.serverless.version>
        <sofa.serverless.trigger.version>1.2.11</sofa.serverless.trigger.version>
        <sofa.ark.version>2.2.9</sofa.ark.version>
        <!--Support Serverless End-->
        <json.lib.version>2.2.2</json.lib.version>
        <antq.version>2.0.21</antq.version>
     	<zdal.version>5.21.1</zdal.version>
        <drm.version>4.8.5</drm.version>
    </properties>

    <modules>
        <module>app/core/model</module>
        <module>app/biz/shared</module>
        <module>app/biz/service-impl</module>
        <module>app/common/util</module>
        <module>app/common/service/facade</module>
        <module>app/common/service/manage-facade</module>
        <module>app/common/service/nimitz-facade</module>
        <module>app/common/service/integration</module>
        <module>app/common/dal</module>
        <module>app/bootstrap</module>
        <module>app/test</module>
        <module>app/core/meta</module>
        <module>app/core/falcon</module>
        <module>app/biz/falcon-prod</module>
        <module>app/biz/lt-falcon-prod</module>
        <module>app/biz/lt-falcon-write</module>
        <module>app/biz/fund-falcon-prod</module>
        <module>app/biz/quote-falcon-write</module>
        <module>app/biz/nimitz-prod</module>
        <module>app/biz/screener-prod</module>
        <module>app/common/service/screener-facade</module>
        <module>arkmodule/fundquote-prod</module>
        <module>arkmodule/tradequote-prod</module>
        <module>arkmodule/screenerquote-prod</module>
        <module>arkmodule/nimitzquote-prod</module>
        <module>arkmodule/screenerquote-scanner-prod</module>
    </modules>
    <dependencyManagement>
        <dependencies>
            <!-- 知蛛sdk -->
            <dependency>
                <groupId>com.alipay.kgengine</groupId>
                <artifactId>kgengine-open-facade</artifactId>
                <version>1.0.0.20220617</version>
            </dependency>

            <dependency>
                <groupId>com.alipay.tradequote-prod</groupId>
                <artifactId>tradequote-prod-common-service-facade</artifactId>
                <version>1.0.0.20230826</version>
            </dependency>

            <dependency>
                <groupId>com.alipay.ngfekg</groupId>
                <artifactId>ngfekg-common-service-facade</artifactId>
                <version>1.0.0.20230920</version>
            </dependency>

            <dependency>
                <groupId>com.alipay.ngfe</groupId>
                <artifactId>ngfe-common-service-facade</artifactId>
                <version>1.0.0.20230523</version>
            </dependency>
            <!-- 知蛛sdk -->

            <!-- sqlite -->
            <dependency>
                <groupId>org.xerial</groupId>
                <artifactId>sqlite-jdbc</artifactId>
                <version>********</version>
            </dependency>
            <!-- sqlite -->

            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>dds-config</artifactId>
                <version>2.0.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>hessian</artifactId>
                        <groupId>hessian</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- geabase dependencies start -->
            <dependency>
                <groupId>com.antfin.geabase2</groupId>
                <artifactId>dds</artifactId>
                <version>*******</version>
            </dependency>
            <dependency>
                <groupId>com.antfin.geabase2</groupId>
                <artifactId>gdbc2</artifactId>
                <version>*******</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tinkerpop</groupId>
                <artifactId>gremlin-groovy</artifactId>
                <version>3.4.8</version>
            </dependency>
            <!-- geabase dependencies end -->

            <dependency>
                <groupId>net.sf.json-lib</groupId>
                <artifactId>json-lib</artifactId>
                <version>2.2.2</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.common</groupId>
                <artifactId>ambush</artifactId>
                <version>1.0.17.sofaboot.dev</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.4</version>
            </dependency>
            <dependency>
                <groupId>commons-httpclient</groupId>
                <artifactId>commons-httpclient</artifactId>
                <version>3.1</version>
            </dependency>
            <dependency>
                <groupId>commons-cli</groupId>
                <artifactId>commons-cli</artifactId>
                <version>1.2</version>
            </dependency>
            <!-- antq 必要依赖 start-->
            <dependency>
                <groupId>com.alipay.antq</groupId>
                <artifactId>antq-remoting</artifactId>
                <version>${antq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.antq</groupId>
                <artifactId>antq-client</artifactId>
                <version>${antq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.antq</groupId>
                <artifactId>antq-common</artifactId>
                <version>${antq.version}</version>
            </dependency>
            <!-- antq end-->

            <dependency>
                <groupId>com.alipay.checkroute</groupId>
                <artifactId>checkroute-common-service-checkersdk</artifactId>
                <version>1.0.0.20211101</version>
            </dependency>
            <!-- sub projects -->
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-core-model</artifactId>
                <version>${tradequote.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-biz-shared</artifactId>
                <version>${tradequote.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-common-service-facade</artifactId>
                <version>1.0.0.20230601</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-common-service-manage-facade</artifactId>
                <version>1.0.0.20240116</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-common-service-nimitz-facade</artifactId>
                <version>1.0.0.20250515</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-biz-service-impl</artifactId>
                <version>${tradequote.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-common-service-integration</artifactId>
                <version>${tradequote.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-common-dal</artifactId>
                <version>${tradequote.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-common-util</artifactId>
                <version>${tradequote.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-bootstrap</artifactId>
                <version>${tradequote.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-test</artifactId>
                <version>${tradequote.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-core-meta</artifactId>
                <version>${tradequote.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-core-falcon</artifactId>
                <version>${tradequote.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-biz-falcon-prod</artifactId>
                <version>${tradequote.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-biz-lt-falcon-prod</artifactId>
                <version>${tradequote.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-biz-lt-falcon-write</artifactId>
                <version>${tradequote.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-biz-fund-falcon-prod</artifactId>
                <version>${tradequote.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-biz-quote-falcon-write</artifactId>
                <version>${tradequote.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-biz-nimitz-prod</artifactId>
                <version>${tradequote.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.quote</groupId>
                <artifactId>commons-utils</artifactId>
                <version>${quotcommons.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alipay.kepler</groupId>
                        <artifactId>kepler-plugin-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- quote-log -->
            <dependency>
                <groupId>com.alipay.quote</groupId>
                <artifactId>quote-log</artifactId>
                <version>${quotcommons.version}</version>
            </dependency>

            <!-- quote-log -->
            <!-- biz start-->
            <dependency>
                <groupId>com.alipay.quote</groupId>
                <artifactId>commons-model-ng</artifactId>
                <version>${commonsmodel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.quote</groupId>
                <artifactId>commons-model</artifactId>
                <version>${commonsmodel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.quote</groupId>
                <artifactId>commons-model-base</artifactId>
                <version>${commonsmodel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.quote</groupId>
                <artifactId>commons-model-quote</artifactId>
                <version>${commonsmodel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.quote</groupId>
                <artifactId>commons-model-stock</artifactId>
                <version>${commonsmodel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.quote</groupId>
                <artifactId>commons-model-compute</artifactId>
                <version>${commonsmodel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.quote</groupId>
                <artifactId>commons-model-fund</artifactId>
                <version>${commonsmodel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.quote</groupId>
                <artifactId>commons-model-facade</artifactId>
                <version>${commonsmodel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.quote</groupId>
                <artifactId>commons-push-model</artifactId>
                <version>${commonsmodel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.quote</groupId>
                <artifactId>logicview-sdk</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>com.alipay.quote</groupId>
                        <artifactId>tradequote-prod-common-service-facade</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alipay.tradequote</groupId>
                        <artifactId>tradequote-common-service-nimitz-facade</artifactId>
                    </exclusion>
                </exclusions>
                <version>${quotcommons.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.quote</groupId>
                <artifactId>quote-nerve</artifactId>
                <version>${quotcommons.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.quotstore</groupId>
                <artifactId>quotstore-biz-bus-sdk</artifactId>
                <version>1.0.0.20190814</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.quotstore</groupId>
                <artifactId>quotstore-biz-bus-client</artifactId>
                <version>1.0.0.20200212</version>
                <exclusions>
                    <exclusion>
                        <artifactId>sofa-runtime-api</artifactId>
                        <groupId>com.alipay.sofa</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alipay.quotstore</groupId>
                <artifactId>bus-client-v2</artifactId>
                <version>1.0.0.20241101.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alipay.sofa</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alipay.sofa.common.log</groupId>
                        <artifactId>sofa-middleware-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alipay.quotstore</groupId>
                <artifactId>bus-sdk-v2</artifactId>
                <version>1.0.0.20241101.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alipay.sofa</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alipay.sofa.common.log</groupId>
                        <artifactId>sofa-middleware-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- biz end-->
            <!--   hulk  -->
            <dependency>
                <groupId>com.alipay.quote</groupId>
                <artifactId>quote-hulk</artifactId>
                <version>1.0.0.20250625</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alipay.zcache</groupId>
                        <artifactId>zcache</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alipay.zcache</groupId>
                        <artifactId>zcache-smartlocalcache</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.hbase</groupId>
                        <artifactId>hbase</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alipay.simplehbase</groupId>
                        <artifactId>simplehbase</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- hulk -->
            <!-- search-->
            <dependency>
                <groupId>com.alipay.quote</groupId>
                <artifactId>quote-search</artifactId>
                <version>1.0.0.20241211.1</version>
            </dependency>
            <!-- search-->
            <!-- meta-->
            <dependency>
                <groupId>com.alipay.quote</groupId>
                <artifactId>quote-meta</artifactId>
                <version>1.0.0.20230831</version>
            </dependency>
            <!-- meta-->
            <!-- scheduler client -->
            <dependency>
                <groupId>com.alipay.sofa.platform</groupId>
                <artifactId>sofa-platform-schedule</artifactId>
                <version>4.0.7</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.communication</groupId>
                <artifactId>communication-common-lang</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.scheduler</groupId>
                <artifactId>scheduler-common-service-facade</artifactId>
                <version>1.1</version>
            </dependency>
            <!-- scheduler client end-->
            <!-- promeg begin -->
            <dependency>
                <groupId>com.github.promeg</groupId>
                <artifactId>tinypinyin</artifactId>
                <version>2.0.3</version>
            </dependency>
            <!-- promeg end -->
            <!-- mybatis begin -->


            <!-- mybatis end -->
            <!--other start-->
            <dependency>
                <groupId>org.zeromq</groupId>
                <artifactId>jeromq</artifactId>
                <version>0.5.2</version>
            </dependency>
            <!--other end-->
            <!-- antvip-common的依赖 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>com.springsource.org.apache.commons.io</artifactId>
                <version>1.4.0</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>

            <!-- taobao remoting与其间接的依赖-->

            <dependency>
                <groupId>org.ta4j</groupId>
                <artifactId>ta4j-core</artifactId>
                <version>0.15</version>
            </dependency>
            <dependency>
                <groupId>org.apache.mina</groupId>
                <artifactId>mina-core</artifactId>
                <version>1.1.5.bugfix</version>
            </dependency>
            <dependency>
                <groupId>commons-pool</groupId>
                <artifactId>commons-pool</artifactId>
                <version>1.5.4</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.common.monitor</groupId>
                <artifactId>common-monitor</artifactId>
                <version>1.0.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--  antvip  by michael 20180508-->
            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>1.2.2.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java-util</artifactId>
                <version>${protobuf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.protobuf-java-format</groupId>
                <artifactId>protobuf-java-format</artifactId>
                <version>1.2</version>
            </dependency>
            <dependency>
                <groupId>${rocksdb.groupId}</groupId>
                <artifactId>${rocksdb.artifactId}</artifactId>
                <version>${rocksdb.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.hbase</groupId>
                <artifactId>hbase</artifactId>
                <version>0.94-adh3u11.6.4</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>jdk.tools</groupId>
                        <artifactId>jdk.tools</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-core</artifactId>
                <version>0.20.2-cdh3u3</version>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop.thirdparty.guava</groupId>
                <artifactId>guava</artifactId>
                <version>r09-jarjar</version>
            </dependency>
            <dependency>
                <groupId>com.github.stephenc.high-scale-lib</groupId>
                <artifactId>high-scale-lib</artifactId>
                <version>1.1.1</version>
            </dependency>

            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>1.7.1</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>1.7.1</version>
                <scope>test</scope>
            </dependency>

            <!-- aisearch start-->
            <dependency>
                <groupId>com.alipay.searchportal</groupId>
                <artifactId>aisearchclient-common-service-facade</artifactId>
                <version>2.5.4.20241112</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.12.0</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.kondyle</groupId>
                <artifactId>kondyle-matchrecords-lib</artifactId>
                <version>4.4.16-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.kondyle</groupId>
                <artifactId>kondyle-dataframe-core</artifactId>
                <version>4.3.20-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.kondyle</groupId>
                <artifactId>kondyle-dataframe-serialize</artifactId>
                <version>4.3.20-RELEASE</version>
            </dependency>

            <!-- 如需与Java对象互转，需引入kondyle-dataframe-mapping -->
            <dependency>
                <groupId>com.alibaba.kondyle</groupId>
                <artifactId>kondyle-dataframe-mapping</artifactId>
                <version>4.3.20-RELEASE</version>
            </dependency>
            <!--aisearch end-->

            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-runtime-alipay-api</artifactId>
                <version>4.10.3</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>com.springsource.slf4j.api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis-2-spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-rpc-alipay-api</artifactId>
                <version>4.9.6</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>com.springsource.slf4j.api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-rpc-alipay-core</artifactId>
                <version>4.9.6</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-rpc-alipay-ldc</artifactId>
                <version>4.9.6</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>annotations</artifactId>
                <version>3.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-rpc-core</artifactId>
                <version>4.13.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>com.springsource.slf4j.api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alipay.common</groupId>
                <artifactId>alipay-common-error</artifactId>
                <version>1.3</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.middleware</groupId>
                <artifactId>logger.api</artifactId>
                <version>0.2.2</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.zdataconsole</groupId>
                <artifactId>zdataconsole-common-service-facade</artifactId>
                <version>1.0.2.20170414</version>
            </dependency>

            <dependency>
                <groupId>biz.paluch.redis</groupId>
                <artifactId>lettuce</artifactId>
                <version>3.5.1.tbase</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-common</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>${netty.version}</version>
            </dependency>

            <dependency>
                <groupId>org.xerial.snappy</groupId>
                <artifactId>snappy-java</artifactId>
                <version>1.1.8.4</version>
            </dependency>
            <dependency>
                <groupId>org.antlr</groupId>
                <artifactId>antlr4-runtime</artifactId>
                <version>4.7</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid-version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.cqengine</groupId>
                <artifactId>cqengine</artifactId>
                <version>2.12.4</version>
            </dependency>
            <!-- 扫描接口的实现 michael-->
            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>0.9.11</version>
            </dependency>
            <!-- 扫描接口的实现 michael-->
            <dependency>
                <groupId>io.reactivex</groupId>
                <artifactId>rxjava</artifactId>
                <version>1.3.8</version>
            </dependency>

            <!-- other end-->
            <!--utils start-->

            <dependency>
                <groupId>org.jgrapht</groupId>
                <artifactId>jgrapht-core</artifactId>
                <version>1.4.0</version>
            </dependency>
            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>com.springsource.org.testng</artifactId>
                <version>6.4.0</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.12</version>
            </dependency>
            <dependency>
                <groupId>com.google.guice</groupId>
                <artifactId>com.springsource.com.google.inject</artifactId>
                <version>2.0.0alipay</version>
            </dependency>
            <dependency>
                <groupId>au.com.bytecode.opencsv</groupId>
                <artifactId>opencsv</artifactId>
                <version>1.8</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tcenter</groupId>
                <artifactId>tcenter-ats-common</artifactId>
                <version>2.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tcenter</groupId>
                <artifactId>tcenter-ats-core</artifactId>
                <version>2.0.3</version>
            </dependency>

            <dependency>
                <groupId>jaxen</groupId>
                <artifactId>jaxen</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.velocity</groupId>
                <artifactId>sketch-jar</artifactId>
                <version>3.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.acegisecurity</groupId>
                <artifactId>acegi-security</artifactId>
                <version>0.9.0</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>jboss</groupId>
                <artifactId>jbossall-client</artifactId>
                <version>4.0.5.alipay</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>juli</artifactId>
                <version>6.0.29</version>
            </dependency>
            <dependency>
                <groupId>net.spy</groupId>
                <artifactId>spymemcached</artifactId>
                <version>2.10.6</version>
            </dependency>

            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>3.4.2</version>
            </dependency>

            <!-- utils end -->
            <dependency>
                <groupId>com.alibaba.lindorm</groupId>
                <artifactId>lindorm-fat-client</artifactId>
                <version>*******</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.mfinquotationprod</groupId>
                <artifactId>mfinquotationprod-common-service-facade</artifactId>
                <version>1.0.0.20221208</version>
            </dependency>
            <!--Tablesaw-->
            <dependency>
                <groupId>tech.tablesaw</groupId>
                <artifactId>tablesaw-core</artifactId>
                <version>0.42.0</version>
            </dependency>
            <dependency>
                <groupId>tech.tablesaw</groupId>
                <artifactId>tablesaw-json</artifactId>
                <version>0.38.3</version>
            </dependency>
            <!-- sql build辅助工具 -->

            <!--OBKV Dependencies Start-->
            <dependency>
                <groupId>com.alipay.oceanbase</groupId>
                <artifactId>oceanbase-table-client</artifactId>
                <version>${ob.table.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.oceanbase</groupId>
                <artifactId>oceanbase-table-hbase</artifactId>
                <version>${ob.table.hbase.version}</version>
            </dependency>
            <!-- OBKV Dependencies End-->
            <!-- tbase client sdk -->
            <dependency>
                <groupId>com.alipay.quote</groupId>
                <artifactId>quote-tbase-client</artifactId>
                <version>1.0.0.20210812.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alipay.zcache</groupId>
                        <artifactId>zcache</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alipay.zcache</groupId>
                        <artifactId>zcache-smartlocalcache</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.hbase</groupId>
                        <artifactId>hbase</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alipay.simplehbase</groupId>
                        <artifactId>simplehbase</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alipay.search</groupId>
                <artifactId>search-client</artifactId>
                <version>********</version>
            </dependency>
            <!-- sofamq-->

            <dependency>
                <groupId>org.apache.avro</groupId>
                <artifactId>avro</artifactId>
                <version>1.9.2</version>
            </dependency>


            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-biz-screener-prod</artifactId>
                <version>${tradequote.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.tradequote</groupId>
                <artifactId>tradequote-common-service-screener-facade</artifactId>
                <version>1.0.0.20250514</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>2.9.3</version>
            </dependency>
            <dependency>
                <groupId>org.typemeta</groupId>
                <artifactId>funcj-parser</artifactId>
                <version>0.6.16</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-csv</artifactId>
                <version>1.4</version>
            </dependency>
            <!-- open api start -->
            <dependency>
                <groupId>com.alipay.superapi</groupId>
                <artifactId>superapi-common-service-client</artifactId>
                <version>1.0.0.20210318</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.superapi</groupId>
                <artifactId>superapi-common-core-client</artifactId>
                <version>1.0.0.20210624</version>
            </dependency>

            <dependency>
                <groupId>com.alipay.opencore</groupId>
                <artifactId>opencore-common-service-facade</artifactId>
                <version>1.0.0.20190725</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.avatar</groupId>
                <artifactId>validation</artifactId>
                <version>1.0.1.20140508</version>
            </dependency>
            <dependency>
                <groupId>ognl</groupId>
                <artifactId>ognl</artifactId>
                <version>3.0.6</version>
                <exclusions>
                    <exclusion>
                        <groupId>javassist</groupId>
                        <artifactId>javassist</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>


            <!-- open api end -->
            <!-- 开放平台 openapi sdk start -->
            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>4.31.92.DEV</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>1.62</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>1.15.0</version>
            </dependency>
            <!-- 开放平台 openapi sdk end -->
            <!-- kmi start -->
            <dependency>
                <groupId>com.alipay.cryptlib</groupId>
                <artifactId>common-obj</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.cryptlib</groupId>
                <artifactId>common-util</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.cryptlib</groupId>
                <artifactId>algorithm-api</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.cryptlib</groupId>
                <artifactId>algorithm-soft-intl</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.cryptlib</groupId>
                <artifactId>algorithm-soft-sm</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.kmi</groupId>
                <artifactId>kmi-common-service-facade</artifactId>
                <version>1.8.1.20210426</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.kmi</groupId>
                <artifactId>kmi-common-service-client</artifactId>
                <version>1.8.2.20210427</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.kmi</groupId>
                <artifactId>kmi-common-service-crypto</artifactId>
                <version>1.1.8.20160712</version>
            </dependency>
            <dependency>
                <groupId>loccs-bcprov</groupId>
                <artifactId>loccs-bcprov-jdk15on</artifactId>
                <version>1.56-20171215</version>
            </dependency>
            <dependency>
                <groupId>loccs-bcprov</groupId>
                <artifactId>loccs-bcpkix-jdk15on</artifactId>
                <version>1.56-20171215</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>legacy-utils</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk15on</artifactId>
                <version>1.60</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.cryptprod</groupId>
                <artifactId>cryptprod-common-service-crypto</artifactId>
                <version>1.2.2.20180516</version>
            </dependency>
            <!-- kmi end -->
            <!-- https://mvnrepository.com/artifact/org.jetbrains/annotations -->
            <dependency>
                <groupId>org.jetbrains</groupId>
                <artifactId>annotations</artifactId>
                <version>23.1.0</version>
            </dependency>
            <!-- 因sofaboot升级到3.15.3, quartz升级到了2.3.2, 不再包含c3p0包.需要手动添加-->
            <dependency>
                <groupId>c3p0</groupId>
                <artifactId>c3p0</artifactId>
                <version>*******</version>
            </dependency>

            <!--Support Serverless Start-->
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-serverless-trigger-starter</artifactId>
                <version>${sofa.serverless.trigger.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <!--秒级集成测试框架 -->
                <artifactId>serverless-flytest</artifactId>
                <version>1.0.2</version>
            </dependency>
            <!--Support Serverless End-->

            <!--Support sofamq Start-->
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofamq-client-all</artifactId>
                <version>${sofamq.client.version}</version>
            </dependency>
            <!--Support sofamq tracer-->
            <dependency>
                <groupId>com.alipay.common</groupId>
                <artifactId>tracer</artifactId>
                <version>3.1.7</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>tracer-core</artifactId>
                <version>3.1.7</version>
            </dependency>

            <dependency>
                <groupId>com.alipay.quotemanager</groupId>
                <artifactId>quotemanager-facade</artifactId>
                <version>1.0.0.20240919.1</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.quotefixarclick.datacode</groupId>
                <artifactId>quotefixarclick-datacode-facade</artifactId>
                <version>1.0.0.20240621.1</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.zlock</groupId>
                <artifactId>zlock</artifactId>
                <version>1.0.3</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <!--Ant Financial Services Group Main Site Must Dependency healthcheck-sofa-boot-starter when deployed online/offline -->
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>healthcheck-sofa-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--
       为了关闭xml标签强校验
       @link http://gitlab.alipay-inc.com/alipay-SOFALite2/middleware-alipay-starters-parent/issues/102
       -->
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>runtime-alipay-sofa-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.smartunit</groupId>
            <artifactId>smartunit-standalone-runtime</artifactId>
            <version>1.4.0-SNAPSHOT</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-maven-plugin</artifactId>
                <version>1.0.1</version>
                <extensions>true</extensions>
                <executions>
                    <execution>
                        <goals>
                            <goal>ace</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
                <version>3.3</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.8.1</version>
                <configuration>
                    <argLine>-Xmx1024m -XX:PermSize=256m -XX:MaxPermSize=512m -Dfile.encoding=UTF-8</argLine>
                    <skipTests>${isSkipUnitTest}</skipTests>
                    <includes>
                        <!-- 这里需要根据自己的需要指定要跑的单元测试 -->
                        <include>**/*Test*.java</include>
                    </includes>
                    <testFailureIgnore>true</testFailureIgnore>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.7.4.201502262128</version>
                <configuration>
                    <skip>${jacoco.skip}</skip>
                    <destFile>${jacoco.path}</destFile>
                    <dataFile>${jacoco.path}</dataFile>
                    <sessionId>jacoco_coverage</sessionId>
                </configuration>
                <executions>
                    <execution>
                        <id>pre-test</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <propertyName>coverageAgent</propertyName>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.2</version>
                <configuration>
                    <archive>
                        <manifestFile>src/main/resources/META-INF/MANIFEST.MF</manifestFile>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <reporting>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>2.3.1</version>
                <configuration>
                    <xmlOutput>true</xmlOutput>
                    <threshold>High</threshold>
                    <effort>Default</effort>
                    <relaxed>true</relaxed>
                    <findbugsXmlOutput>true</findbugsXmlOutput>
                </configuration>
            </plugin>
        </plugins>
    </reporting>
    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <repositories>
                <repository>
                    <id>central_prod</id>
                    <url>http://mvn.test.alipay.net:8080/artifactory/repo</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>central</id>
                    <url>http://mvn.dev.alipay.net:8080/artifactory/repo</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>snapshots</id>
                    <url>http://mvn.dev.alipay.net:8080/artifactory/repo</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>central</id>
                    <url>http://mvn.dev.alipay.net:8080/artifactory/repo</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
            <distributionManagement>
                <repository>
                    <id>releases</id>
                    <name>Alipay Releases</name>
                    <url>http://mvn.dev.alipay.net/artifactory/content/repositories/Alipay-Releases/</url>
                </repository>
                <snapshotRepository>
                    <id>snapshot</id>
                    <name>snapshot</name>
                    <url>http://mvn.dev.alipay.net/artifactory/content/repositories/Alipay-Snapshot/</url>
                    <layout>default</layout>
                </snapshotRepository>
            </distributionManagement>
        </profile>
        <profile>
            <id>test</id>
            <activation>
                <property>
                    <name>env</name>
                    <value>test</value>
                </property>
            </activation>
            <repositories>
                <repository>
                    <id>central</id>
                    <url>http://mvn.test.alipay.net:8080/artifactory/repo</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>snapshots</id>
                    <url>http://mvn.test.alipay.net:8080/artifactory/repo</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>central</id>
                    <url>http://mvn.test.alipay.net:8080/artifactory/repo</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
        <profile>
            <id>prod</id>
            <activation>
                <property>
                    <name>env</name>
                    <value>prod</value>
                </property>
            </activation>
            <repositories>
                <repository>
                    <id>central</id>
                    <url>http://mvn.test.alipay.net:8080/artifactory/repo</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>snapshots</id>
                    <url>http://mvn.test.alipay.net:8080/artifactory/repo</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>central</id>
                    <url>http://mvn.test.alipay.net:8080/artifactory/repo</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>
</project>