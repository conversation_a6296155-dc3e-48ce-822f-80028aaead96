package com.alipay.tradequote.core.sidecar.impl;


import com.alipay.quote.hulk.drm.HulkCacheDrmModel;
import com.alipay.tradequote.core.falcon.model.DataWrapper;
import com.alipay.tradequote.core.falcon.model.KeyWrapper;
import com.alipay.tradequote.core.falcon.service.snapshot.SnapshotSerialize;
import com.alipay.zcache.impl.RefreshableLdcTbaseCacheManager;
import com.alipay.zcache.impl.RefreshableZoneTbaseCacheManager;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;

@RunWith(MockitoJUnitRunner.class)
public class HulkHATbaseBaseServiceTest {

    /**
     * TBASE高保业务写服务
     */
    @Mock
    private RefreshableZoneTbaseCacheManager hulkHATBaseWriter;

    /**
     * TBASE高保业务读服务
     */
    @Mock
    private RefreshableLdcTbaseCacheManager hulkHATBaseReader;

    /**
     * TBase service
     */

    @Mock
    private SnapshotSerialize snapshotSerializeHelper;

    private HulkHATbaseBaseService hulkHATbaseBaseService = new HulkHATbaseBaseService();

    @Test
    public void testUpdate() {
        HulkCacheDrmModel hulkDrm = new HulkCacheDrmModel();
        hulkDrm.setTbaseTimeout(111L);
        hulkDrm.setTbaseInfluxConf("\t517:3:200:5000:600000:false:true:3:3,512:3:200:5000:600000:false:true:3:3,2:20:250:2000:1150000:false:true:8:8,4:50:300:1000:145000:false:true:8:8");
        hulkHATbaseBaseService.setHulkDrm(hulkDrm);
        hulkHATbaseBaseService.setHulkHATBaseReader(hulkHATBaseReader);
        hulkHATbaseBaseService.setHulkHATBaseWriter(hulkHATBaseWriter);
        hulkHATbaseBaseService.init();
        hulkHATbaseBaseService.update(
                new KeyWrapper(0, "test"),
                DataWrapper.builder().build(),
                10,
                snapshotSerializeHelper
        );
    }

    @Test
    public void testMget() {
        HulkCacheDrmModel hulkDrm = new HulkCacheDrmModel();
        hulkDrm.setTbaseInfluxConf("\t517:3:200:5000:600000:false:true:3:3,512:3:200:5000:600000:false:true:3:3,2:20:250:2000:1150000:false:true:8:8,4:50:300:1000:145000:false:true:8:8");
        hulkHATbaseBaseService.setHulkDrm(hulkDrm);
        hulkHATbaseBaseService.setHulkHATBaseReader(hulkHATBaseReader);
        hulkHATbaseBaseService.setHulkHATBaseWriter(hulkHATBaseWriter);
        hulkHATbaseBaseService.init();
        hulkHATbaseBaseService.mget(
                Collections.singletonList(new KeyWrapper(0, "test")),
                snapshotSerializeHelper
        );
    }

    @Test(expected= RuntimeException.class)
    public void testS() {
        hulkHATbaseBaseService.serializeV(DataWrapper.builder().build(), snapshotSerializeHelper);
        doThrow(new RuntimeException()).when(snapshotSerializeHelper).serializeV(any(), any());
        hulkHATbaseBaseService.serializeV(DataWrapper.builder().build(), snapshotSerializeHelper);
    }

    @Test(expected= RuntimeException.class)
    public void testD() {
        hulkHATbaseBaseService.deserializeV(new KeyWrapper(0, "test").getBytes(), snapshotSerializeHelper);
        doThrow(new RuntimeException()).when(snapshotSerializeHelper).deserializeV(any(), any());
        hulkHATbaseBaseService.deserializeV(new KeyWrapper(0, "test").getBytes(), snapshotSerializeHelper);
    }
}
