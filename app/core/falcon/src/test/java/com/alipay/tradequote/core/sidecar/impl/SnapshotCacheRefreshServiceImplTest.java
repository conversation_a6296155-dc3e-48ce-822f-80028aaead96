package com.alipay.tradequote.core.sidecar.impl;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import com.alipay.quot.commons.models.BaseSnapshot;
import com.alipay.quot.commons.models.Event;
import com.alipay.quot.commons.models.Snapshot;
import com.alipay.tradequote.core.falcon.model.KeyWrapper;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import com.alipay.quot.commons.models.DatagramProto;
import com.alipay.quote.hulk.model.HulkCacheKey;
import com.alipay.tradequote.core.falcon.model.DataWrapper;
import com.alipay.tradequote.core.falcon.service.snapshot.SnapshotSerialize;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SnapshotCacheRefreshServiceImplTest {
    @InjectMocks
    private SnapshotCacheRefreshServiceImpl snapshotCacheRefreshService;

    @Mock
    private HulkHATbaseBaseService hulkHATbaseBaseService;

    @Mock
    private SnapshotSerialize snapshotSerializeHelper;

    BaseSnapshot.BaseSnapshotDO snapshotDO = BaseSnapshot.BaseSnapshotDO.newBuilder().setSnapshotDate(System.currentTimeMillis()).setSymbol("TEST.SH").build();

    /**
     * [单测用例]测试场景：测试正常情况
     */
    @Test
    public void testRefreshKVCache_Normal() {
        DatagramProto.Datagram datagram = DatagramProto.Datagram.newBuilder()
                .setBehaveType(DatagramProto.Datagram.BehaveType.UPDATE)
                .setData(snapshotDO.toByteString())
                .build();
        List<DatagramProto.Datagram> datagramList = Arrays.asList(datagram);

        when(hulkHATbaseBaseService.update(any(HulkCacheKey.class), any(DataWrapper.class), anyInt(), any(SnapshotSerialize.class)))
                .thenReturn(true);
        when(hulkHATbaseBaseService.mget(anyList(), any(SnapshotSerialize.class)))
                .thenReturn(Collections.singletonMap(new KeyWrapper(0, "TEST.SH.."), DataWrapper.builder().data(null).build()));

        boolean result = snapshotCacheRefreshService.refreshKVCache(datagramList);
        assertTrue(result);

        when(hulkHATbaseBaseService.mget(anyList(), any(SnapshotSerialize.class)))
                .thenReturn(Collections.singletonMap(new KeyWrapper(0, "TEST.SH"), DataWrapper.builder().data(null).build()));

        result = snapshotCacheRefreshService.refreshKVCache(datagramList);
        assertTrue(result);

        when(hulkHATbaseBaseService.mget(anyList(), any(SnapshotSerialize.class)))
                .thenReturn(Collections.singletonMap(new KeyWrapper(0, "TEST.SH.."), DataWrapper.builder().data(null).hulkTime(System.currentTimeMillis()).build()));

        result = snapshotCacheRefreshService.refreshKVCache(datagramList);
        assertTrue(result);
    }

    /**
     * [单测用例]测试场景：测试datagramList为空的情况
     */
    @Test
    public void testRefreshKVCache_EmptyList() {
        List<DatagramProto.Datagram> datagramList = Arrays.asList();

        boolean result = snapshotCacheRefreshService.refreshKVCache(datagramList);

        assertTrue(result);
    }

//    /**
//     * [单测用例]测试场景：测试datagramList中没有UPDATE行为类型的情况
//     */
    @Test
    public void testRefreshKVCache_NoUpdateType() {
        DatagramProto.Datagram datagram = DatagramProto.Datagram.newBuilder()
                .setBehaveType(DatagramProto.Datagram.BehaveType.UPDATE)
                .build();
        List<DatagramProto.Datagram> datagramList = Arrays.asList(datagram);

        boolean result = snapshotCacheRefreshService.refreshKVCache(datagramList);

        assertTrue(result);
    }

    /**
     * [单测用例]测试场景：测试datagramList为空的情况
     */
    @Test
    public void testGetDatagram() {
        List<DatagramProto.Datagram> result = snapshotCacheRefreshService
                .getDatagram(Collections.singletonList(Event.NotifyEvent.newBuilder().setGroup(0).setPayload(snapshotDO.toByteString()).
                        setSource(Event.DataSource.DATA_SNAPSHOT)
                        .build()));
        Assert.assertEquals(1, result.size());
    }
}
