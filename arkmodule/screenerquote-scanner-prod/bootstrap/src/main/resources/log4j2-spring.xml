<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
    <Appenders>
        <Console name="STDOUT-APPENDER" target="SYSTEM_OUT">
            <PatternLayout pattern="%-5p %c{2} - %m%n%throwable" charset="UTF-8"/>
        </Console>

        <Console name="STDERR-APPENDER" target="SYSTEM_ERR">
            <PatternLayout pattern="%-5p %c{2} - %m%n%throwable" charset="UTF-8"/>
        </Console>

        <RollingFile name="ERROR-APPENDER"
                     fileName="${ctx:module.logging.path}/${ctx:spring.application.name}/common-error.log"
                     filePattern="${ctx:module.logging.path}/${ctx:spring.application.name}/common-error.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
            <ThresholdFilter level="ERROR"/>
        </RollingFile>

        <!-- 模块的错误日志也打印到基座的common-error中 -->
        <RollingFile name="BASE-ERROR-APPENDER"
                     fileName="${ctx:logging.path}/${ctx:ALIPAY_APP_APPNAME}/common-error.log"
                     filePattern="${ctx:logging.path}/${ctx:ALIPAY_APP_APPNAME}/common-error.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{requestURIWithQueryString}][${ctx:spring.application.name}][%X{serverlessSceneId}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
            <ThresholdFilter level="ERROR"/>
        </RollingFile>

        <RollingFile name="APP-DEFAULT-APPENDER"
                     fileName="${ctx:module.logging.path}/${ctx:spring.application.name}/app-default.log"
                     filePattern="${ctx:module.logging.path}/${ctx:spring.application.name}/app-default.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>

        <RollingFile name="SPRING-APPENDER"
                     fileName="${ctx:module.logging.path}/spring/spring.log"
                     filePattern="${ctx:module.logging.path}/spring/spring.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>

        <RollingFile name="NO-USAGE-APPENDER"
                     fileName="${ctx:module.logging.path}/no-usage/no-usage.log"
                     filePattern="${ctx:module.logging.path}/no-usage/no-usage.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>

        <!-- screenerquote-scanner-prod 业务日志 -->
        <RollingFile name="SCREENERQUOTE-SCANNER-PROD-BIZ-SERVICE-APPENDER"
                     fileName="${ctx:logging.path}/${ctx:ALIPAY_APP_APPNAME}/screenerquote-scanner-prod-biz-service.log"
                     filePattern="${ctx:logging.path}/${ctx:ALIPAY_APP_APPNAME}/screenerquote-scanner-prod-biz-service.log.%i"
                     append="true">
            <PatternLayout pattern="%d [%m]%n" charset="UTF-8"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="512MB"/>
            </Policies>
            <DefaultRolloverStrategy max="2"/>
        </RollingFile>

        <!-- screenerquote-scanner-prod 定时任务层服务日志 -->
        <RollingFile name="SCREENERQUOTE-SCANNER-PROD-SCHED-APPENDER"
                     fileName="${ctx:logging.path}/${ctx:ALIPAY_APP_APPNAME}/screenerquote-scanner-prod-sched.log"
                     filePattern="${ctx:logging.path}/${ctx:ALIPAY_APP_APPNAME}/screenerquote-scanner-prod-sched.log.%i"
                     append="true">
            <PatternLayout pattern="%d [%m]%n" charset="UTF-8"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="512MB"/>
            </Policies>
            <DefaultRolloverStrategy max="2"/>
        </RollingFile>

        <!-- screenerquote-scanner-prod zsearch服务日志 -->
        <RollingFile name="SCREENERQUOTE-SCANNER-PROD-ZSEARCH-APPENDER"
                     fileName="${ctx:logging.path}/${ctx:ALIPAY_APP_APPNAME}/screenerquote-scanner-prod-zsearch.log"
                     filePattern="${ctx:logging.path}/${ctx:ALIPAY_APP_APPNAME}/screenerquote-scanner-prod-zsearch.log.%i"
                     append="true">
            <PatternLayout pattern="%d [%m]%n" charset="UTF-8"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="512MB"/>
            </Policies>
            <DefaultRolloverStrategy max="2"/>
        </RollingFile>
    </Appenders>

    <Loggers>
        <AsyncLogger name="STDOUT" additivity="false" level="info">
            <AppenderRef ref="STDOUT-APPENDER"/>
        </AsyncLogger>

        <AsyncLogger name="STDERR" additivity="false"
                     level="${ctx:logging.level.com.alipay.screenerquote.scannerprod}">
            <AppenderRef ref="STDERR-APPENDER"/>
        </AsyncLogger>

        <AsyncLogger name="com.taobao.tair" additivity="false"
                     level="${ctx:logging.level.com.alipay.screenerquote.scannerprod}">
            <AppenderRef ref="NO-USAGE-APPENDER"/>
        </AsyncLogger>

        <AsyncLogger name="com.taobao.vipserver" additivity="false"
                     level="${ctx:logging.level.com.alipay.screenerquote.scannerprod}">
            <AppenderRef ref="NO-USAGE-APPENDER"/>
        </AsyncLogger>

        <AsyncLogger name="org.springframework" additivity="false"
                     level="${ctx:logging.level.com.alipay.screenerquote.scannerprod}">
            <AppenderRef ref="ERROR-APPENDER"/>
            <AppenderRef ref="SPRING-APPENDER"/>
            <AppenderRef ref="BASE-ERROR-APPENDER"/>
        </AsyncLogger>

        <AsyncRoot level="${ctx:logging.level.com.alipay.screenerquote.scannerprod}">
            <AppenderRef ref="APP-DEFAULT-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
            <AppenderRef ref="BASE-ERROR-APPENDER"/>
        </AsyncRoot>

        <!-- 模块biz层服务查询日志 -->
        <AsyncLogger name="SCREENERQUOTE-SCANNER-PROD-BIZ-SERVICE" additivity="false"
                     level="${ctx:logging.level.com.alipay.screenerquote.scannerprod}">
            <AppenderRef ref="SCREENERQUOTE-SCANNER-PROD-BIZ-SERVICE-APPENDER"/>
        </AsyncLogger>

        <!-- 模块定时任务层服务日志 -->
        <AsyncLogger name="SCREENERQUOTE-SCANNER-PROD-SCHED" additivity="false"
                     level="${ctx:logging.level.com.alipay.screenerquote.scannerprod}">
            <AppenderRef ref="SCREENERQUOTE-SCANNER-PROD-SCHED-APPENDER"/>
        </AsyncLogger>

        <!-- 模块数据写入服务日志 -->
        <AsyncLogger name="SCREENERQUOTE-SCANNER-PROD-ZSEARCH" additivity="false"
                     level="${ctx:logging.level.com.alipay.screenerquote.scannerprod}">
            <AppenderRef ref="SCREENERQUOTE-SCANNER-PROD-ZSEARCH-APPENDER"/>
        </AsyncLogger>
    </Loggers>
</Configuration>
