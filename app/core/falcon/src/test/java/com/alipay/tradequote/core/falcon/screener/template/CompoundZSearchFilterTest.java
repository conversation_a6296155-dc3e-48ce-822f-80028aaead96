/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.template;

import com.alipay.logicview.sdk.model.request.IndicatorViewQueryRequest;
import com.alipay.logicview.sdk.model.response.IndicatorViewData;
import com.alipay.logicview.sdk.model.response.IndicatorViewQueryResponse;
import com.alipay.logicview.sdk.model.response.IndicatorViewSeries;
import com.alipay.logicview.sdk.service.entry.IndicatorViewZSearchQueryService;
import com.alipay.quot.commons.facade.model.querycond.SortCond;
import com.alipay.quot.commons.models.Common;
import com.alipay.quot.commons.models.ScreenerProto;
import com.alipay.quot.commons.models.querycond.PageCondOuterClass;
import com.alipay.quot.commons.models.querycond.SortCondOuterClass;
import com.alipay.quote.nerve.qsql.ast.AstBuilder3;
import com.alipay.quote.nerve.qsql.ast.Node;
import com.alipay.quote.nerve.qsql.lib.value.primitive.DoubleValue;
import com.alipay.quote.nerve.qsql.lib.value.primitive.StringValue;
import com.alipay.quote.nerve.qsql.lib.viewCell.ViewCell;
import com.alipay.quote.nerve.qsql.pushdown.ZSearchQueryTranslator;
import com.alipay.tradequote.core.falcon.screener.model.template.CompoundFilterResult;
import com.alipay.tradequote.core.falcon.screener.model.template.ScreenObj;
import com.alipay.tradequote.core.falcon.screener.template.impl.CompoundZSearchFilter;
import com.alipay.zsearch.core.query.QueryBuilder;
import com.google.common.collect.Sets;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class CompoundZSearchFilterTest {

    @Mock
    private IndicatorViewZSearchQueryService indicatorViewZSearchQueryService;

    @Mock
    private AstBuilder3 astBuilder3;

    @Mock
    private Node node;

    @Mock
    private ZSearchQueryTranslator translator;

    @Mock
    private QueryBuilder queryBuilder;

    private CompoundZSearchFilter compoundZSearchFilter;

    @Before
    public void setUp() {
        compoundZSearchFilter = new CompoundZSearchFilter();
        compoundZSearchFilter.setIndicatorViewZSearchQueryService(indicatorViewZSearchQueryService);
    }

    @Test
    public void testScreen_withValidFilter_returnsSuccessResult() throws Exception {
        // 准备测试数据
        Common.VCell operand = Common.VCell.newBuilder().setSv("test_indicator").build();
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand)
                .build();
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .build();

        List<ScreenObj> inputObjs = Arrays.asList(
                new ScreenObj("600519.SH", "TestStock1"),
                new ScreenObj("000001.SZ", "TestStock2")
        );

        // Mock ZSearch响应
        IndicatorViewData viewData1 = new IndicatorViewData();
        viewData1.setSymbol("600519.SH");
        viewData1.setName("TestStock1");

        IndicatorViewSeries series1 = new IndicatorViewSeries();
        series1.setName("test_indicator");
        series1.setCells(Collections.singletonList(ViewCell.builder().value(new DoubleValue(123.45)).build()));
        viewData1.setSeries(Collections.singletonList(series1));

        IndicatorViewData viewData2 = new IndicatorViewData();
        viewData2.setSymbol("000001.SZ");
        viewData2.setName("TestStock2");

        IndicatorViewSeries series2 = new IndicatorViewSeries();
        series2.setName("test_indicator");
        series2.setCells(Collections.singletonList(ViewCell.builder().value(new StringValue("test_value")).build()));
        viewData2.setSeries(Collections.singletonList(series2));

        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Arrays.asList(viewData1, viewData2));

        when(indicatorViewZSearchQueryService.queryByDsl(any(IndicatorViewQueryRequest.class)))
                .thenReturn(response);

        // 执行测试
        CompoundFilterResult result = compoundZSearchFilter.screen(inputObjs, filter);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getObjs().size());
        assertEquals(Sets.newHashSet("test_indicator"), result.getFilterIndicators());

        // 验证返回的对象都标记为来自ZSearch
        for (ScreenObj obj : result.getObjs()) {
            assertTrue(obj.isFilterFromZSearch());
        }

        verify(indicatorViewZSearchQueryService).queryByDsl(any(IndicatorViewQueryRequest.class));
    }

    @Test
    public void testScreen_withPageCondition_setsCorrectLimit() throws Exception {
        // 准备带分页条件的filter
        PageCondOuterClass.PageCond pageCond = PageCondOuterClass.PageCond.newBuilder()
                .setLimit(100)
                .setOffset(0)
                .build();

        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .setPageCond(pageCond)
                .build();

        List<ScreenObj> inputObjs = Collections.singletonList(new ScreenObj("600519.SH", "TestStock"));

        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Collections.emptyList());

        when(indicatorViewZSearchQueryService.queryByDsl(any(IndicatorViewQueryRequest.class)))
                .thenReturn(response);

        compoundZSearchFilter.screen(inputObjs, filter);

        // 验证请求参数
        verify(indicatorViewZSearchQueryService).queryByDsl(argThat(request ->
                request.getLimit() == 100
        ));
    }

    @Test
    public void testScreen_withoutPageCondition_usesDefaultLimit() throws Exception {
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder().build();
        List<ScreenObj> inputObjs = Collections.singletonList(new ScreenObj("600519.SH", "TestStock"));

        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Collections.emptyList());

        when(indicatorViewZSearchQueryService.queryByDsl(any(IndicatorViewQueryRequest.class)))
                .thenReturn(response);

        compoundZSearchFilter.screen(inputObjs, filter);

        // 验证使用默认limit 5000
        verify(indicatorViewZSearchQueryService).queryByDsl(argThat(request ->
                request.getLimit() == 5000
        ));
    }

    @Test
    public void testScreen_withZeroPageLimit_usesDefaultLimit() throws Exception {
        PageCondOuterClass.PageCond pageCond = PageCondOuterClass.PageCond.newBuilder()
                .setLimit(0) // 无效的limit
                .build();

        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .setPageCond(pageCond)
                .build();

        List<ScreenObj> inputObjs = Collections.singletonList(new ScreenObj("600519.SH", "TestStock"));

        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Collections.emptyList());

        when(indicatorViewZSearchQueryService.queryByDsl(any(IndicatorViewQueryRequest.class)))
                .thenReturn(response);

        compoundZSearchFilter.screen(inputObjs, filter);

        // 验证使用默认limit
        verify(indicatorViewZSearchQueryService).queryByDsl(argThat(request ->
                request.getLimit() == 5000
        ));
    }

    @Test
    public void testScreen_withSortCondition_setsSortCond() throws Exception {
        SortCondOuterClass.SortCond sortCond = SortCondOuterClass.SortCond.newBuilder()
                .setField("sort_field")
                .setDesc(true)
                .build();

        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .setSortCond(sortCond)
                .build();

        List<ScreenObj> inputObjs = Collections.singletonList(new ScreenObj("600519.SH", "TestStock"));

        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Collections.emptyList());

        when(indicatorViewZSearchQueryService.queryByDsl(any(IndicatorViewQueryRequest.class)))
                .thenReturn(response);

        compoundZSearchFilter.screen(inputObjs, filter);

        // 验证排序条件
        verify(indicatorViewZSearchQueryService).queryByDsl(argThat(request -> {
            SortCond requestSortCond = request.getSortCond();
            return requestSortCond != null
                    && "sort_field".equals(requestSortCond.getField())
                    && requestSortCond.isDesc();
        }));
    }

    @Test
    public void testScreen_withEmptySortField_setsNullSortCond() throws Exception {
        SortCondOuterClass.SortCond sortCond = SortCondOuterClass.SortCond.newBuilder()
                .setField("") // 空字段
                .setDesc(true)
                .build();

        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .setSortCond(sortCond)
                .build();

        List<ScreenObj> inputObjs = Collections.singletonList(new ScreenObj("600519.SH", "TestStock"));

        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Collections.emptyList());

        when(indicatorViewZSearchQueryService.queryByDsl(any(IndicatorViewQueryRequest.class)))
                .thenReturn(response);

        compoundZSearchFilter.screen(inputObjs, filter);

        // 验证排序条件为null
        verify(indicatorViewZSearchQueryService).queryByDsl(argThat(request ->
                request.getSortCond() == null
        ));
    }

    @Test
    public void testScreen_withoutSortCondition_setsNullSortCond() throws Exception {
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder().build();
        List<ScreenObj> inputObjs = Collections.singletonList(new ScreenObj("600519.SH", "TestStock"));

        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Collections.emptyList());

        when(indicatorViewZSearchQueryService.queryByDsl(any(IndicatorViewQueryRequest.class)))
                .thenReturn(response);

        compoundZSearchFilter.screen(inputObjs, filter);

        // 验证排序条件为null
        verify(indicatorViewZSearchQueryService).queryByDsl(argThat(request ->
                request.getSortCond() == null
        ));
    }

    @Test
    public void testScreen_withEmptyResponse_returnsEmptyResult() throws Exception {
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder().build();
        List<ScreenObj> inputObjs = Collections.singletonList(new ScreenObj("600519.SH", "TestStock"));

        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Collections.emptyList());

        when(indicatorViewZSearchQueryService.queryByDsl(any(IndicatorViewQueryRequest.class)))
                .thenReturn(response);

        CompoundFilterResult result = compoundZSearchFilter.screen(inputObjs, filter);

        assertNotNull(result);
        assertTrue(result.getObjs().isEmpty());
        assertTrue(result.getFilterIndicators().isEmpty());

        verify(indicatorViewZSearchQueryService).queryByDsl(any(IndicatorViewQueryRequest.class));
    }

    @Test
    public void testScreen_withNullResponse_returnsEmptyResult() throws Exception {
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder().build();
        List<ScreenObj> inputObjs = Collections.singletonList(new ScreenObj("600519.SH", "TestStock"));

        when(indicatorViewZSearchQueryService.queryByDsl(any(IndicatorViewQueryRequest.class)))
                .thenReturn(null);

        CompoundFilterResult result = compoundZSearchFilter.screen(inputObjs, filter);

        assertNotNull(result);
        assertTrue(result.getObjs().isEmpty());
        assertTrue(result.getFilterIndicators().isEmpty());

        verify(indicatorViewZSearchQueryService).queryByDsl(any(IndicatorViewQueryRequest.class));
    }

    @Test
    public void testScreen_withServiceException_returnsEmptyResult() throws Exception {
        Common.VCell operand = Common.VCell.newBuilder().setSv("test_indicator").build();
        ScreenerProto.ScreenCondition condition = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand)
                .build();
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition)
                .build();

        List<ScreenObj> inputObjs = Collections.singletonList(new ScreenObj("600519.SH", "TestStock"));

        when(indicatorViewZSearchQueryService.queryByDsl(any(IndicatorViewQueryRequest.class)))
                .thenThrow(new RuntimeException("ZSearch service exception"));

        CompoundFilterResult result = compoundZSearchFilter.screen(inputObjs, filter);

        assertNotNull(result);
        assertTrue(result.getObjs().isEmpty());
        assertEquals(Sets.newHashSet("test_indicator"), result.getFilterIndicators());

        verify(indicatorViewZSearchQueryService).queryByDsl(any(IndicatorViewQueryRequest.class));
    }

    @Test
    public void testScreen_withAstBuilderException_returnsEmptyResult() throws Exception {
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder().build();
        List<ScreenObj> inputObjs = Collections.singletonList(new ScreenObj("600519.SH", "TestStock"));

        // 由于AstBuilder3是在方法内部创建的，我们无法直接mock它抛异常
        // 但我们可以通过让ZSearch服务抛异常来模拟AST构建失败的情况
        when(indicatorViewZSearchQueryService.queryByDsl(any(IndicatorViewQueryRequest.class)))
                .thenThrow(new IllegalArgumentException("AST build failed"));

        CompoundFilterResult result = compoundZSearchFilter.screen(inputObjs, filter);

        assertNotNull(result);
        assertTrue(result.getObjs().isEmpty());
        assertTrue(result.getFilterIndicators().isEmpty());

        verify(indicatorViewZSearchQueryService).queryByDsl(any(IndicatorViewQueryRequest.class));
    }

    @Test
    public void testScreen_withComplexFilter_extractsAllIndicators() throws Exception {
        // 创建包含多个指标的复杂filter
        Common.VCell operand1 = Common.VCell.newBuilder().setSv("indicator1").build();
        Common.VCell operand2 = Common.VCell.newBuilder().setSv("indicator2").build();

        ScreenerProto.ScreenCondition condition1 = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand1)
                .build();
        ScreenerProto.ScreenCondition condition2 = ScreenerProto.ScreenCondition.newBuilder()
                .addOperands(operand2)
                .build();

        SortCondOuterClass.SortCond sortCond = SortCondOuterClass.SortCond.newBuilder()
                .setField("sort_indicator")
                .build();

        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .addConditions(condition1)
                .addConditions(condition2)
                .setSortCond(sortCond)
                .build();

        List<ScreenObj> inputObjs = Collections.singletonList(new ScreenObj("600519.SH", "TestStock"));

        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Collections.emptyList());

        when(indicatorViewZSearchQueryService.queryByDsl(any(IndicatorViewQueryRequest.class)))
                .thenReturn(response);

        CompoundFilterResult result = compoundZSearchFilter.screen(inputObjs, filter);

        assertNotNull(result);
        assertTrue(result.getObjs().isEmpty());

        // 验证提取了所有指标
        Set<String> expectedIndicators = Sets.newHashSet("indicator1", "indicator2", "sort_indicator");
        assertEquals(expectedIndicators, result.getFilterIndicators());

        verify(indicatorViewZSearchQueryService).queryByDsl(any(IndicatorViewQueryRequest.class));
    }

    @Test
    public void testScreen_withNullViewDataInResponse_filtersOut() throws Exception {
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder().build();
        List<ScreenObj> inputObjs = Collections.singletonList(new ScreenObj("600519.SH", "TestStock"));

        // 创建包含null数据的响应
        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Arrays.asList((IndicatorViewData) null));

        when(indicatorViewZSearchQueryService.queryByDsl(any(IndicatorViewQueryRequest.class)))
                .thenReturn(response);

        CompoundFilterResult result = compoundZSearchFilter.screen(inputObjs, filter);

        assertNotNull(result);
        assertTrue(result.getObjs().isEmpty()); // null数据被过滤掉
        assertTrue(result.getFilterIndicators().isEmpty());

        verify(indicatorViewZSearchQueryService).queryByDsl(any(IndicatorViewQueryRequest.class));
    }

    @Test
    public void testScreen_withMixedValidAndInvalidData_filtersCorrectly() throws Exception {
        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder().build();
        List<ScreenObj> inputObjs = Collections.singletonList(new ScreenObj("600519.SH", "TestStock"));

        // 创建有效数据
        IndicatorViewData validData = new IndicatorViewData();
        validData.setSymbol("600519.SH");
        validData.setName("ValidStock");

        IndicatorViewSeries series = new IndicatorViewSeries();
        series.setName("test_indicator");
        series.setCells(Collections.singletonList(ViewCell.builder().value(new StringValue("test")).build()));
        validData.setSeries(Collections.singletonList(series));

        // 创建无效数据（会导致convertToSectionFrame返回null）
        IndicatorViewData invalidData = new IndicatorViewData();
        invalidData.setSymbol("000001.SZ");
        invalidData.setName("InvalidStock");
        invalidData.setSeries(null); // 这会导致转换失败

        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Arrays.asList(validData, null, invalidData));

        when(indicatorViewZSearchQueryService.queryByDsl(any(IndicatorViewQueryRequest.class)))
                .thenReturn(response);

        CompoundFilterResult result = compoundZSearchFilter.screen(inputObjs, filter);

        assertNotNull(result);
        assertEquals(1, result.getObjs().size()); // 只有有效数据被保留
        assertEquals("600519.SH", result.getObjs().get(0).getObjId());
        assertTrue(result.getObjs().get(0).isFilterFromZSearch());

        verify(indicatorViewZSearchQueryService).queryByDsl(any(IndicatorViewQueryRequest.class));
    }

    @Test
    public void testScreen_withBothPageAndSortConditions_setsCorrectly() throws Exception {
        PageCondOuterClass.PageCond pageCond = PageCondOuterClass.PageCond.newBuilder()
                .setLimit(200)
                .setOffset(10)
                .build();

        SortCondOuterClass.SortCond sortCond = SortCondOuterClass.SortCond.newBuilder()
                .setField("combined_sort_field")
                .setDesc(false)
                .build();

        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .setPageCond(pageCond)
                .setSortCond(sortCond)
                .build();

        List<ScreenObj> inputObjs = Collections.singletonList(new ScreenObj("600519.SH", "TestStock"));

        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Collections.emptyList());

        when(indicatorViewZSearchQueryService.queryByDsl(any(IndicatorViewQueryRequest.class)))
                .thenReturn(response);

        compoundZSearchFilter.screen(inputObjs, filter);

        // 验证请求参数
        verify(indicatorViewZSearchQueryService).queryByDsl(argThat(request -> {
            SortCond requestSortCond = request.getSortCond();
            return request.getLimit() == 200
                    && requestSortCond != null
                    && "combined_sort_field".equals(requestSortCond.getField())
                    && !requestSortCond.isDesc();
        }));
    }

    @Test
    public void testScreen_withNegativePageLimit_usesDefaultLimit() throws Exception {
        PageCondOuterClass.PageCond pageCond = PageCondOuterClass.PageCond.newBuilder()
                .setLimit(-1) // 负数limit
                .build();

        ScreenerProto.ScreenCompoundFilter filter = ScreenerProto.ScreenCompoundFilter.newBuilder()
                .setPageCond(pageCond)
                .build();

        List<ScreenObj> inputObjs = Collections.singletonList(new ScreenObj("600519.SH", "TestStock"));

        IndicatorViewQueryResponse response = new IndicatorViewQueryResponse();
        response.setData(Collections.emptyList());

        when(indicatorViewZSearchQueryService.queryByDsl(any(IndicatorViewQueryRequest.class)))
                .thenReturn(response);

        compoundZSearchFilter.screen(inputObjs, filter);

        // 验证使用默认limit
        verify(indicatorViewZSearchQueryService).queryByDsl(argThat(request ->
                request.getLimit() == 5000
        ));
    }
}
