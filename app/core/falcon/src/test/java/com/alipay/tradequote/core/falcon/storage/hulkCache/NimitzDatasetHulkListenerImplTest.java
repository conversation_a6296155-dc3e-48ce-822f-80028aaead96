/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.storage.hulkCache;

import com.alibaba.fastjson.JSON;
import com.alipay.quote.hulk.cache.provider.NimitzStorageProvider;
import com.alipay.quote.hulk.drm.GuavaInfluxConfig;
import com.alipay.quote.hulk.drm.HulkCacheDrmModel;
import com.alipay.quote.hulk.drm.TbaseInfluxConfig;
import com.alipay.quote.hulk.thread.DefaultCoordinator;
import com.alipay.tradequote.core.falcon.drm.NimitzRsDataConfigService;
import com.alipay.tradequote.core.falcon.nimitz.service.NimitzSerialize;
import com.alipay.tradequote.core.model.nimitz.config.NimitzDatasetHulk;
import com.alipay.tradequote.core.model.nimitz.config.hulk.GuavaConfig;
import com.alipay.tradequote.core.model.nimitz.config.hulk.TBaseConfig;
import com.alipay.tradequote.meta.service.model.Constants;
import com.alipay.zcache.impl.RefreshableLdcTbaseCacheManager;
import com.alipay.zcache.impl.RefreshableZoneTbaseCacheManager;
import com.google.common.collect.ImmutableMap;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.GenericApplicationContext;

import java.util.HashMap;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @version $Id: NimitzDatasetHulkListenerImplTest.java, v 0.1 2021-07-31 18:50 sushuang Exp $$
 */
public class NimitzDatasetHulkListenerImplTest {

    @InjectMocks
    private NimitzDatasetHulkListenerImpl nimitzDatasetHulkListenerImpl;

    private ApplicationContext applicationContext;

    @Mock
    private NimitzProdClientManager nimitzProdClientManager;

    @Mock
    private NimitzRsDataConfigService nimitzRsDataConfigService;

    @Mock
    private HulkCacheDrmModel hulkDrm;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        applicationContext = spy(new GenericApplicationContext());
        // spring context中填充provider
        mockNimitzBean((GenericApplicationContext)applicationContext,
                Constants.NIMITZ_PROVIDER_BEAN_NAME, NimitzStorageProvider.class);
        mockNimitzBean((GenericApplicationContext)applicationContext,
                Constants.NEW_NIMITZ_PROVIDER_BEAN_NAME, NimitzStorageProvider.class);
        // spring context中填充NimitzSerialize 和 DefaultCoordinator
        mockNimitzBean((GenericApplicationContext)applicationContext,
                Constants.NIMITZ_DATASET_SERRIALIZE_BEAN_NAME, NimitzSerialize.class);
        mockNimitzBean((GenericApplicationContext)applicationContext,
                Constants.NIMITZ_DATASET_COORDINATOR_BEAN_NAME, DefaultCoordinator.class);
        // spring context中填充hulkTBaseReader与hulkTBaseWriter
        mockNimitzBean((GenericApplicationContext)applicationContext,
                "hulkTBaseReader", RefreshableLdcTbaseCacheManager.class);
        mockNimitzBean((GenericApplicationContext)applicationContext,
                "hulkTBaseWriter", RefreshableZoneTbaseCacheManager.class);
        mockNimitzBean((GenericApplicationContext)applicationContext,
                "hulkDrm", HulkCacheDrmModel.class);
        ((GenericApplicationContext)applicationContext).refresh();
        when(applicationContext.getBean(Constants.NIMITZ_PROVIDER_BEAN_NAME)).thenReturn(Constants.NIMITZ_PROVIDER_BEAN_NAME);
        when(applicationContext.getBean(Constants.NEW_NIMITZ_PROVIDER_BEAN_NAME)).thenReturn(Constants.NEW_NIMITZ_PROVIDER_BEAN_NAME);

        nimitzDatasetHulkListenerImpl.setApplicationContext(applicationContext);
        nimitzDatasetHulkListenerImpl.setNimitzProdClientManager(nimitzProdClientManager);
        nimitzDatasetHulkListenerImpl.setNimitzRsDataConfigService(nimitzRsDataConfigService);
    }

    @Test
    public void testCreateOrUpdateSeriesBean() {
        try {
            nimitzDatasetHulkListenerImpl.createOrUpdateBean(null);
        } catch (Exception e) {
            assert e != null;
        }

        nimitzDatasetHulkListenerImpl.createOrUpdateBean(new HashMap<>());

        NimitzDatasetHulk h1 = new NimitzDatasetHulk();
        h1.setDatasetCode("a");
        h1.setGuavaInfluxConfig(JSON.parseObject("{\n" +
                "\t\"size\": \"1000\",\n" +
                "\t\"shortAsyncReload\": \"true\",\n" +
                "\t\"shortLen\": \"3\",\n" +
                "\t\"shortRefresh\": \"5000\",\n" +
                "\t\"shortExpire\": \"1\",\n" +
                "\t\"longAsyncReload\": \"true\",\n" +
                "\t\"longLen\": \"200\",\n" +
                "\t\"longRefresh\": \"600000\",\n" +
                "\t\"longExpire\": \"1\"\n" +
                "}", GuavaInfluxConfig.class));
        h1.setTbaseInfluxConfig(JSON.parseObject("{\n" +
                "\t\"shortLock\": \"false\",\n" +
                "\t\"shortLen\": \"3\",\n" +
                "\t\"shortExpire\": \"1\",\n" +
                "\t\"shortRetry\": \"3\",\n" +
                "\t\"longLock\": \"true\",\n" +
                "\t\"longLen\": \"200\",\n" +
                "\t\"longExpire\": \"1\",\n" +
                "\t\"longRetry\": \"3\"\n" +
                "}", TbaseInfluxConfig.class));
        nimitzDatasetHulkListenerImpl.createOrUpdateBean0(ImmutableMap.of(h1.getDatasetCode(), h1));
    }

    @Test
    public void testCreateOrUpdateSeriesBean2() {
        when(nimitzRsDataConfigService.useNewProvider()).thenReturn(Boolean.TRUE);
        testCreateOrUpdateSeriesBean();
    }

    @Test
    public void testCreateOrUpdateKVBean() {

        NimitzDatasetHulk h1 = new NimitzDatasetHulk();
        h1.setDatasetCode("DS_FUND_ARCHIVES");
        h1.setGuavaConfig(JSON.parseObject("{\n" +
                "  \"cacheSize\": \"2000\",\n" +
                "  \"order\": \"true\",\n" +
                "  \"duration\": \"1\",\n" +
                "  \"timeUnit\": \"MINUTES\",\n" +
                "  \"cacheStrategy\": \"ASYNC_LOADALL_CACHE\"\n" +
                "}", GuavaConfig.class));
        h1.setTBaseConfig(JSON.parseObject("{\n" +
                "  \"duration\": \"120\"\n" +
                "}", TBaseConfig.class));
        nimitzDatasetHulkListenerImpl.createOrUpdateBean0(ImmutableMap.of(h1.getDatasetCode(), h1));
    }

    @Test
    public void testCreateOrUpdateKVBeanWithoutThrowEx() {

        NimitzDatasetHulk h1 = new NimitzDatasetHulk();
        h1.setDatasetCode("DS_FUND_ARCHIVES");
        h1.setGuavaConfig(JSON.parseObject("{\n" +
                "  \"cacheSize\": \"2000\",\n" +
                "  \"order\": \"true\",\n" +
                "  \"duration\": \"1\",\n" +
                "  \"timeUnit\": \"MINUTES\",\n" +
                "  \"cacheStrategy\": \"ASYNC_LOADALL_CACHE\"\n" +
                "}", GuavaConfig.class));
        h1.setTBaseConfig(JSON.parseObject("{\n" +
                "  \"duration\": \"120\",\n" +
                "  \"throwEx\": \"true\"\n" +
                "}", TBaseConfig.class));
        nimitzDatasetHulkListenerImpl.createOrUpdateBean0(ImmutableMap.of(h1.getDatasetCode(), h1));
    }

    /**
     * mock nimitz storage provider
     *
     * @param context
     */
    private static void mockNimitzBean(GenericApplicationContext context, String beanName, Class clazz) {
        // Bean的实例工厂
        DefaultListableBeanFactory dbf = (DefaultListableBeanFactory) context.getBeanFactory();
        // Bean构建
        BeanDefinitionBuilder guavaInfluxConfigBuilder = BeanDefinitionBuilder.genericBeanDefinition(clazz);

        //将实例注册spring容器中
        dbf.registerBeanDefinition(beanName, guavaInfluxConfigBuilder.getBeanDefinition());

    }
}