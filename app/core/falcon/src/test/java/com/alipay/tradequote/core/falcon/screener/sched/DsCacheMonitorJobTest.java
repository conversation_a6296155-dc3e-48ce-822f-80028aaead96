package com.alipay.tradequote.core.falcon.screener.sched;

import com.alipay.tradequote.core.falcon.screener.sched.job.DsViewCacheMonitorJob;
import com.alipay.tradequote.core.falcon.screener.view.cache.ViewCache;
import com.alipay.tradequote.core.falcon.screener.view.drm.ScreenerViewDrm;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.concurrent.ConcurrentHashMap;

import static org.mockito.Mockito.when;

public class DsCacheMonitorJobTest {

    @InjectMocks
    DsViewCacheMonitorJob dsViewCacheMonitorJob;

    @Mock
    ViewCache viewCache;

    @Mock
    ScreenerViewDrm screenerViewDrm;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void test(){
        when(viewCache.asMap()).thenReturn(new ConcurrentHashMap<>());
        when(screenerViewDrm.isSnapshotUpdateDetailTrigger()).thenReturn(true);
        when(viewCache.stats()).thenReturn(mockStatic());
        dsViewCacheMonitorJob.execute(null);
        when(viewCache.stats().averageLoadPenalty()).thenThrow(new RuntimeException("mock error"));
        dsViewCacheMonitorJob.execute(null);
        dsViewCacheMonitorJob.execute(null);
    }

    public CacheStats mockStatic(){
        return  new CacheStats(
                10,  // hitCount
                2,   // missCount
                5,   // loadSuccessCount
                1,   // loadExceptionCount
                1000, // totalLoadTime (通常是纳秒)
                3,   // evictionCount
                0    // evictionWeight
        );
    }

}
