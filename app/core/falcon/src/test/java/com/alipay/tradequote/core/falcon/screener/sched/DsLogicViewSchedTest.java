package com.alipay.tradequote.core.falcon.screener.sched;

import com.alipay.logicview.sdk.service.entry.ViewCacheEntrySdk;
import com.alipay.quot.commons.models.Symbol;
import com.alipay.quote.nerve.qsql.lib.value.Value;
import com.alipay.quote.nerve.qsql.lib.value.primitive.DoubleValue;
import com.alipay.quote.nerve.qsql.lib.value.primitive.NullValue;
import com.alipay.quote.nerve.qsql.lib.value.primitive.StringValue;
import com.alipay.quote.nerve.qsql.lib.viewCell.ViewCell;
import com.alipay.quote.nerve.qsql.lib.viewCell.ViewIndex;
import com.alipay.tradequote.core.falcon.screener.indicator.IndicatorFrameService;
import com.alipay.tradequote.core.falcon.screener.model.IndicatorFrame;
import com.alipay.tradequote.core.falcon.screener.model.JobDescriptor;
import com.alipay.tradequote.core.falcon.screener.sched.job.MergeFrameJob;
import com.alipay.tradequote.core.falcon.screener.view.drm.ScreenerViewDrm;
import com.alipay.tradequote.meta.service.SymbolGqlService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static com.alipay.tradequote.core.falcon.screener.model.DataType.DS_LOGIC_VIEW;
import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

public class DsLogicViewSchedTest {

    @InjectMocks
    MergeFrameJob mergeFrameJob;

    @Mock
    IndicatorFrameService indicatorFrameService;

    @Mock
    SymbolGqlService symbolGqlService;

    @Mock
    ViewCacheEntrySdk viewCacheEntrySdk;

    @Mock
    ScreenerViewDrm screenerViewDrm;

    private ScreenJobContext context;

    private JobDescriptor jobDescriptor;

    @Before
    public void setUp() {

        jobDescriptor = new JobDescriptor();
        jobDescriptor.setGql("testGql");
        jobDescriptor.setExtProps(new HashMap<String, Object>() {{
            put("columns", new HashMap<String, List<String>>() {{
                put("dataType1", Arrays.asList("indicator1", "indicator2"));
                put("dataType2", Arrays.asList("view_screener.indicator3", "indicator4"));
            }});
        }});
        Set<String> listType = new HashSet<>();
        listType.add("ListType_E");
        context = new ScreenJobContext(jobDescriptor, listType);

    }


    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

        @Test
    public void executeTest() {
        JobDescriptor jobDescriptor = new JobDescriptor();
        jobDescriptor.setName("MERGE_FRAME_JOB");
        jobDescriptor.setGql("gql");
        jobDescriptor.setExtProps(new HashMap<>());
        ScreenJobContext  context = new ScreenJobContext(jobDescriptor, Sets.newHashSet("SH"));
        when(symbolGqlService.querySymbol(anyString())).thenReturn(Lists.newArrayList(Symbol.SymbolDO.newBuilder().setAliSymbol("600519.SH").build()));
        when(viewCacheEntrySdk.getValueAll(anyString())).thenReturn(viewMapSimulation());
        when(screenerViewDrm.isScreenerTriggerLogicView()).thenReturn(true);
        mergeFrameJob.execute(context);
        when(viewCacheEntrySdk.getValueAll(anyString())).thenReturn(viewMapBlankCellSimulation());
        when(screenerViewDrm.isScreenerTriggerLogicView()).thenReturn(true);
        mergeFrameJob.execute(context);

        }


    public Map<String, Map<String, ViewCell>> viewMapSimulation() {
        Map<String, Map<String, ViewCell>> viewMap = new HashMap<>();
        viewMap.put("600519.SH", Maps.newConcurrentMap());
        Map<String, ViewCell> viewCellMap = Maps.newConcurrentMap();
        viewCellMap.put("close", ViewCell.builder().value(new DoubleValue(Double.valueOf(6.51))).build());
        viewMap.put("002714.SZ", viewCellMap);
        return viewMap;
    }


    public Map<String, Map<String, ViewCell>> viewMapBlankCellSimulation() {
        Map<String, Map<String, ViewCell>> viewMap = new HashMap<>();
        viewMap.put("600519.SH", Maps.newConcurrentMap());
        return viewMap;
    }

    @Test
    public void testExecuteWithScreenerTriggerLogicViewFalse() {

        // test origin
        when(screenerViewDrm.isScreenerTriggerLogicView()).thenReturn(false);
        Symbol.SymbolDO symbolDO = Symbol.SymbolDO.newBuilder().setSymbol("test").setListType(Symbol.SymbolDO.ListType.ListType_E).build();
        when(symbolGqlService.querySymbol(anyString())).thenReturn(Collections.singletonList(symbolDO));
        when(screenerViewDrm.getPrintLogicViewValue()).thenReturn("test");
        mergeFrameJob.execute(context);

        // test switch
        when(screenerViewDrm.isScreenerTriggerLogicView()).thenReturn(true);
        when(indicatorFrameService.getFrame("dataType2", symbolDO.getSymbol())).thenReturn(null);
        List<String> whightMap = new ArrayList<>();
        List<String> whightList = new ArrayList<>();
        whightList.add("indicator3");
        whightList.add("indicator4");
        whightMap.add("dataType2");
        when(screenerViewDrm.getViewDataTypeWhiteList()).thenReturn(whightMap);
        mergeFrameJob.execute(context);

        Value value1 = new StringValue("test");
        Map<String, Value> valueMap = new HashMap<>();
        valueMap.put("indicator1", value1);
        IndicatorFrame frame1 = new IndicatorFrame(valueMap);

        Map<String, Map<String, ViewCell>> viewMap = new HashMap<>();
        ViewCell viewCell = ViewCell.builder()
                .value(value1)
                .viewIndex(new ViewIndex()).build();
        Map<String, ViewCell> viewCellMap = new HashMap<>();
        viewCellMap.put("indicator3", viewCell);
        viewMap.put("test", viewCellMap);

        when(viewCacheEntrySdk.getValueAll(DS_LOGIC_VIEW)).thenReturn(viewMap);
        when(indicatorFrameService.getFrame("dataType1", symbolDO.getSymbol())).thenReturn(null);
        when(indicatorFrameService.getFrame("dataType2", symbolDO.getSymbol())).thenReturn(frame1);
        mergeFrameJob.execute(context);

        // test switch
        when(screenerViewDrm.isScreenerTriggerLogicView()).thenReturn(true);
        when(indicatorFrameService.getFrame("dataType2", symbolDO.getSymbol())).thenReturn(null);
        List<String> whightList2 = new ArrayList<>();
        whightList2.add("indicator3");
        whightList2.add("indicator4");
        List<String> whightMap2 = new ArrayList<>();
        whightMap2.add("datatype3");
        when(screenerViewDrm.getViewDataTypeWhiteList()).thenReturn(whightMap2);
        mergeFrameJob.execute(context);
    }

    @Test
    public void testExecuteWithScreenerTriggerLogicViewCompare() {

        Value value1 = new StringValue("test");
        Map<String, Value> valueMap = new HashMap<>();
        valueMap.put("indicator1", value1);
        IndicatorFrame frame1 = new IndicatorFrame(valueMap);

        Map<String, Map<String, ViewCell>> viewMap = new HashMap<>();
        ViewCell viewCell = ViewCell.builder()
                .value(value1)
                .viewIndex(new ViewIndex()).build();
        Map<String, ViewCell> viewCellMap = new HashMap<>();
        viewCellMap.put("indicator1", viewCell);
        viewCellMap.put("indicator2", viewCell);
        viewMap.put("test", viewCellMap);

        when(viewCacheEntrySdk.getValueAll(DS_LOGIC_VIEW)).thenReturn(viewMap);

        // test no data
        when(screenerViewDrm.isbCompareLogicViewSwitch()).thenReturn(true);
        Symbol.SymbolDO symbolDO = Symbol.SymbolDO.newBuilder().setSymbol("test").setListType(Symbol.SymbolDO.ListType.ListType_E).build();
        when(symbolGqlService.querySymbol(anyString())).thenReturn(Collections.singletonList(symbolDO));
        when(screenerViewDrm.isScreenerTriggerLogicView()).thenReturn(true);
        List<String> whightMap = new ArrayList<>();
        whightMap.add("dataType2");
        whightMap.add("dataType1");

        when(screenerViewDrm.getViewDataTypeWhiteList()).thenReturn(whightMap);
        mergeFrameJob.execute(context);

        // test no conf
        whightMap.clear();
        whightMap.add("dataType2");
        whightMap.add("dataType1");

        when(screenerViewDrm.getViewDataTypeWhiteList()).thenReturn(whightMap);
        mergeFrameJob.execute(context);

        // test miss conf
        whightMap.clear();
        whightMap.add("dataType2");
        whightMap.add("dataType1");

        when(screenerViewDrm.getViewDataTypeWhiteList()).thenReturn(whightMap);
        mergeFrameJob.execute(context);

        // test error conf
        whightMap.clear();
        whightMap.add("dataType2");
        whightMap.add("dataType1");

        viewCellMap.put("view_screener.indicator1", viewCell);
        viewCellMap.put("view_screener.indicator2", viewCell);
        viewCellMap.put("indicator3", viewCell);
        viewCellMap.put("indicator4", viewCell);
        viewMap.put("test", viewCellMap);

        mergeFrameJob.execute(context);

        // test field map
        Map<String, List<String>> fieldMap = new HashMap<>();
        fieldMap.put("dataType1", Lists.newArrayList("view_screener.indicator1"));

        valueMap.clear();
        valueMap.put("indicator1", value1);
        valueMap.put("indicator2", new NullValue());
        frame1 = new IndicatorFrame(valueMap);
        when(indicatorFrameService.getFrame("dataType1", symbolDO.getSymbol())).thenReturn(frame1);

        when(screenerViewDrm.getViewFieldCompareWhiteMap()).thenReturn(fieldMap);
        mergeFrameJob.execute(context);

        // test value
        Value value2 = new StringValue("test2");
        ViewCell viewCell2 = ViewCell.builder()
                .value(value1)
                .viewIndex(new ViewIndex()).build();

        ViewCell nullViewCell = ViewCell.builder()
                .value(new NullValue())
                .viewIndex(new ViewIndex()).build();

        viewCellMap.put("view_screener.indicator1", nullViewCell);
        viewCellMap.put("view_screener.indicator2", viewCell2);
        viewCellMap.put("indicator3", viewCell);
        viewCellMap.put("indicator4", viewCell);
        viewMap.put("test", viewCellMap);

        valueMap.clear();
        valueMap.put("view_screener.indicator1", value1);
        valueMap.put("view_screener.indicator2", value2);

        frame1 = new IndicatorFrame(valueMap);
        when(indicatorFrameService.getFrame("dataType1", symbolDO.getSymbol())).thenReturn(frame1);
        fieldMap.clear();
        fieldMap.put("dataType1", Lists.newArrayList("view_screener.indicator2"));
        when(screenerViewDrm.getViewFieldCompareWhiteMap()).thenReturn(fieldMap);
        mergeFrameJob.execute(context);

        // test ok value
        jobDescriptor.setExtProps(new HashMap<String, Object>() {{
            put("columns", new HashMap<String, List<String>>() {{
                put("dataType1", Arrays.asList("indicator1", "indicator2"));
                put("dataType2", Arrays.asList("indicator3", "indicator4"));
            }});
        }});

        viewCellMap.clear();
        viewCellMap.put("indicator1", viewCell);
        viewCellMap.put("indicator2", viewCell);
        viewCellMap.put("indicator3", viewCell);
        viewCellMap.put("indicator4", viewCell);
        viewMap.put("test", viewCellMap);

        when(viewCacheEntrySdk.getValueAll(DS_LOGIC_VIEW)).thenReturn(viewMap);
        whightMap.add("dataType2");
        whightMap.add("dataType1");
        when(screenerViewDrm.getViewDataTypeWhiteList()).thenReturn(whightMap);

        valueMap.clear();
        valueMap.put("indicator1", value1);
        valueMap.put("indicator2", value1);
        frame1 = new IndicatorFrame(valueMap);

        valueMap.clear();
        valueMap.put("indicator3", value1);
        valueMap.put("indicator4", value1);

        IndicatorFrame frame2 = new IndicatorFrame(valueMap);
        when(indicatorFrameService.getFrame("dataType1", symbolDO.getSymbol())).thenReturn(frame1);
        when(indicatorFrameService.getFrame("dataType2", symbolDO.getSymbol())).thenReturn(frame2);
        mergeFrameJob.execute(context);
    }

    @Test
    public void testExecuteWithScreenerTriggerLogicViewCompare2() {

        // test switch
        when(screenerViewDrm.isScreenerTriggerLogicView()).thenReturn(true);
        // test field check
        Symbol.SymbolDO symbolDO = Symbol.SymbolDO.newBuilder().setSymbol("test").setListType(Symbol.SymbolDO.ListType.ListType_E).build();
        when(screenerViewDrm.isbCompareLogicViewSwitch()).thenReturn(true);
        when(symbolGqlService.querySymbol(anyString())).thenReturn(Collections.singletonList(symbolDO));

        Value value1 = new StringValue("test");
        Map<String, Value> valueMap = new HashMap<>();
        valueMap.put("indicator1", value1);
        IndicatorFrame frame1 = new IndicatorFrame(valueMap);

        Map<String, Map<String, ViewCell>> viewMap = new HashMap<>();
        ViewCell viewCell = ViewCell.builder()
                .value(value1)
                .viewIndex(new ViewIndex()).build();
        Map<String, ViewCell> viewCellMap = new HashMap<>();

        jobDescriptor.setExtProps(new HashMap<String, Object>() {{
            put("columns", new HashMap<String, List<String>>() {{
                put("dataType1", Arrays.asList("indicator1", "indicator2"));
                put("dataType2", Arrays.asList("indicator3", "indicator4"));
            }});
        }});

        ViewCell viewCell2 = ViewCell.builder()
                .value(new NullValue())
                .viewIndex(new ViewIndex()).build();

        Value value3 = new StringValue("test2");
        ViewCell viewCell3 = ViewCell.builder()
                .value(value3)
                .viewIndex(new ViewIndex()).build();

        viewCellMap.clear();
        viewCellMap.put("indicator1", viewCell);
        viewCellMap.put("indicator2", viewCell2);
        viewCellMap.put("indicator3", viewCell);
        viewCellMap.put("indicator4", viewCell3);
        viewMap.put("test", viewCellMap);

        List<String> whightMap = new ArrayList<>();
        when(viewCacheEntrySdk.getValueAll(DS_LOGIC_VIEW)).thenReturn(viewMap);
        whightMap.add("dataType2");
        whightMap.add("dataType1");
        when(screenerViewDrm.getViewDataTypeWhiteList()).thenReturn(whightMap);

        Map<String, List<String>> fieldMap = new HashMap<>();
        fieldMap.put("dataType1", Lists.newArrayList("indicator1", "indicator2"));
        fieldMap.put("dataType2", Lists.newArrayList("indicator3", "indicator4"));

        when(screenerViewDrm.getViewFieldCompareWhiteMap()).thenReturn(fieldMap);

        valueMap.clear();
        valueMap.put("indicator1", value1);
        valueMap.put("indicator2", value1);
        frame1 = new IndicatorFrame(valueMap);

        valueMap.clear();
        valueMap.put("indicator3", value1);
        valueMap.put("indicator4", value1);

        IndicatorFrame frame2 = new IndicatorFrame(valueMap);
        when(indicatorFrameService.getFrame("dataType1", symbolDO.getSymbol())).thenReturn(frame1);
        when(indicatorFrameService.getFrame("dataType2", symbolDO.getSymbol())).thenReturn(frame2);
        mergeFrameJob.execute(context);
    }

    @Test
    public void testExecuteWithScreenerTriggerLogicViewCompare3() {

        // test switch
        when(screenerViewDrm.isScreenerTriggerLogicView()).thenReturn(true);
        // test field check
        Symbol.SymbolDO symbolDO = Symbol.SymbolDO.newBuilder().setSymbol("test").setListType(Symbol.SymbolDO.ListType.ListType_E).build();
        when(screenerViewDrm.isbCompareLogicViewSwitch()).thenReturn(true);
        when(symbolGqlService.querySymbol(anyString())).thenReturn(Collections.singletonList(symbolDO));

        when(viewCacheEntrySdk.getValueAll(DS_LOGIC_VIEW)).thenReturn(new HashMap<>());
        mergeFrameJob.execute(context);
        when(viewCacheEntrySdk.getValueAll(DS_LOGIC_VIEW)).thenReturn(null);
        try {
            mergeFrameJob.execute(context);
        } catch (Exception e) {

        }
    }

    @Test
    public void testExecuteWithScreenerTriggerLogicViewCompare4() {

        Value value1 = new StringValue("test");
        Map<String, Value> valueMap = new HashMap<>();
        valueMap.put("indicator1", value1);
        IndicatorFrame frame1 = new IndicatorFrame(valueMap);

        Map<String, Map<String, ViewCell>> viewMap = new HashMap<>();
        ViewCell viewCell = ViewCell.builder()
                .value(value1)
                .viewIndex(new ViewIndex()).build();
        Map<String, ViewCell> viewCellMap = new HashMap<>();
        viewCellMap.put("indicator1", viewCell);
        viewCellMap.put("indicator2", viewCell);
        viewMap.put("test", viewCellMap);

        when(viewCacheEntrySdk.getValueAll(DS_LOGIC_VIEW)).thenReturn(viewMap);

        // test no data
        when(screenerViewDrm.isbCompareLogicViewSwitch()).thenReturn(true);
        Symbol.SymbolDO symbolDO = Symbol.SymbolDO.newBuilder().setSymbol("test").setListType(Symbol.SymbolDO.ListType.ListType_E).build();
        Symbol.SymbolDO symbolDO2 = Symbol.SymbolDO.newBuilder().setSymbol("test2").setListType(Symbol.SymbolDO.ListType.ListType_E).build();
        List<Symbol.SymbolDO> symbolDOS = Lists.newArrayList(symbolDO2, symbolDO);
        when(symbolGqlService.querySymbol(anyString())).thenReturn(symbolDOS);
        when(screenerViewDrm.isScreenerTriggerLogicView()).thenReturn(true);
        List<String> whightMap = new ArrayList<>();

        // test ok value
        jobDescriptor.setExtProps(new HashMap<String, Object>() {{
            put("columns", new HashMap<String, List<String>>() {{
                put("dataType1", Arrays.asList("indicator1", "indicator2"));
                put("dataType2", Arrays.asList("indicator3", "indicator4"));
            }});
        }});

        viewCellMap.clear();
        viewCellMap.put("indicator1", viewCell);
        viewCellMap.put("indicator2", viewCell);
        viewCellMap.put("indicator3", viewCell);
        viewCellMap.put("indicator4", viewCell);
        viewMap.put("test", viewCellMap);

        when(viewCacheEntrySdk.getValueAll(DS_LOGIC_VIEW)).thenReturn(viewMap);
        whightMap.add("dataType2");
        whightMap.add("dataType1");
        when(screenerViewDrm.getViewDataTypeWhiteList()).thenReturn(whightMap);

        valueMap.clear();
        valueMap.put("indicator1", value1);
        valueMap.put("indicator2", value1);
        frame1 = new IndicatorFrame(valueMap);

        valueMap.clear();
        valueMap.put("indicator3", value1);
        valueMap.put("indicator4", value1);

        Map<String, String> nameMap = new HashMap<>();
        nameMap.put("Indicator1", "indicator1");
        when(screenerViewDrm.getViewFieldNameMap()).thenReturn(nameMap);

        IndicatorFrame frame2 = new IndicatorFrame(valueMap);
        when(indicatorFrameService.getFrame("dataType1", symbolDO.getSymbol())).thenReturn(frame1);
        when(indicatorFrameService.getFrame("dataType2", symbolDO.getSymbol())).thenReturn(frame2);
        mergeFrameJob.execute(context);
    }

    @Test
    public void testExecuteWithScreenerTriggerLogicViewCompare5() {

        Value value1 = new StringValue("test");
        Map<String, Value> valueMap = new HashMap<>();
        valueMap.put("indicator1", value1);
        IndicatorFrame frame1 = new IndicatorFrame(valueMap);

        Map<String, Map<String, ViewCell>> viewMap = new HashMap<>();
        ViewCell viewCell = ViewCell.builder()
                .value(value1)
                .viewIndex(new ViewIndex()).build();
        Map<String, ViewCell> viewCellMap = new HashMap<>();
        viewCellMap.put("indicator1", viewCell);
        viewCellMap.put("indicator2", viewCell);
        viewMap.put("test", viewCellMap);

        when(viewCacheEntrySdk.getValueAll(DS_LOGIC_VIEW)).thenReturn(viewMap);

        // test no data
        when(screenerViewDrm.isbCompareLogicViewSwitch()).thenReturn(true);
        Symbol.SymbolDO symbolDO = Symbol.SymbolDO.newBuilder().setSymbol("test").setListType(Symbol.SymbolDO.ListType.ListType_E).build();
        Symbol.SymbolDO symbolDO2 = Symbol.SymbolDO.newBuilder().setSymbol("test2").setListType(Symbol.SymbolDO.ListType.ListType_E).build();
        List<Symbol.SymbolDO> symbolDOS = Lists.newArrayList(symbolDO2, symbolDO);
        when(symbolGqlService.querySymbol(anyString())).thenReturn(symbolDOS);
        when(screenerViewDrm.isScreenerTriggerLogicView()).thenReturn(true);
        List<String> whightMap = new ArrayList<>();

        // test ok value
        jobDescriptor.setExtProps(new HashMap<String, Object>() {{
            put("columns", new HashMap<String, List<String>>() {{
                put("dataType1", Arrays.asList("indicator1", "indicator2"));
                put("dataType2", Arrays.asList("indicator3", "indicator4"));
                put("dataType3", Arrays.asList("indicator3", "indicator4"));
            }});
        }});

        viewCellMap.clear();
        viewCellMap.put("indicator1", viewCell);
        viewCellMap.put("indicator2", viewCell);
        viewCellMap.put("indicator3", viewCell);
        viewCellMap.put("indicator4", viewCell);
        viewMap.put("test", viewCellMap);

        when(viewCacheEntrySdk.getValueAll(DS_LOGIC_VIEW)).thenReturn(viewMap);
        whightMap.add("dataType2");
        whightMap.add("dataType1");
        when(screenerViewDrm.getViewDataTypeWhiteList()).thenReturn(whightMap);

        valueMap.clear();
        valueMap.put("indicator1", value1);
        valueMap.put("indicator2", value1);
        frame1 = new IndicatorFrame(valueMap);

        valueMap.clear();
        valueMap.put("indicator3", value1);
        valueMap.put("indicator4", value1);

        Map<String, String> nameMap = new HashMap<>();
        nameMap.put("Indicator1", "indicator1");
        when(screenerViewDrm.getViewFieldNameMap()).thenReturn(nameMap);

        IndicatorFrame frame2 = new IndicatorFrame(valueMap);
        when(indicatorFrameService.getFrame("dataType1", symbolDO.getSymbol())).thenReturn(frame1);
        when(indicatorFrameService.getFrame("dataType2", symbolDO.getSymbol())).thenReturn(frame2);
        mergeFrameJob.execute(context);
    }

}
