/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod;

import com.alipay.sofa.boot.autoconfigure.mobilegw.AlipayMobilegwAutoConfiguration;
import com.alipay.sofa.boot.autoconfigure.sofamq.AlipaySofaMQAutoConfiguration;
import com.alipay.sofa.runtime.spring.reader.VelocityXmlBeanDefinitionReader;
import com.alipay.sofa.trigger.common.annotations.TriggerScan;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.annotation.ImportResource;

/**
 * @author: guolei.sgl (<EMAIL>) 2019/12/16 10:50 PM
 * @since:
 **/
@SpringBootApplication(scanBasePackages = {"com.alipay.screenerquote.scannerprod"}, exclude = {ThymeleafAutoConfiguration.class, AlipayMobilegwAutoConfiguration.class, AlipaySofaMQAutoConfiguration.class, DataSourceAutoConfiguration.class, DataSourceTransactionManagerAutoConfiguration.class, HibernateJpaAutoConfiguration.class})
@ImportResource(locations = "classpath*:spring/*.xml", reader = VelocityXmlBeanDefinitionReader.class)
@TriggerScan(scanPackages = "com.alipay.screenerquote.scannerprod")
public class ModuleBootstrapApplication {

    public static void main(String[] args) {
        SpringApplicationBuilder builder = new SpringApplicationBuilder(ModuleBootstrapApplication.class).web(WebApplicationType.NONE);
        builder.build().run(args);
    }
}
