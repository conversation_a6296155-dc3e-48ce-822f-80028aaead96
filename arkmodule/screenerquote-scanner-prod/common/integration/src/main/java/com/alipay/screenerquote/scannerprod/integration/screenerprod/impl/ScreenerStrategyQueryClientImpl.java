/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.integration.screenerprod.impl;

import com.alipay.quot.commons.facade.result.Result;
import com.alipay.screenerquote.prod.api.ScreenerModuleFacade;
import com.alipay.screenerquote.prod.model.ScreenerStrategyRequestDTO;
import com.alipay.screenerquote.prod.model.ScreenerStrategyResultDTO;
import com.alipay.screenerquote.scannerprod.integration.screenerprod.ScreenerStrategyQueryClient;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version ScreenerStrategyEngineClientImpl.java, v 0.1 2025年02月24日 20:05 lzt
 */
@Setter
public class ScreenerStrategyQueryClientImpl implements ScreenerStrategyQueryClient {

    /**
     * screenerModuleFacade
     */
    private ScreenerModuleFacade screenerModuleFacade;

    /**
     * 查询选股策略
     */
    @Override
    public Result<ScreenerStrategyResultDTO> queryByStrategy(ScreenerStrategyRequestDTO request){
        return screenerModuleFacade.queryByStrategy(request);
    }
}