spring.application.name=screenerquote-scanner-prod

logging.level.com.alipay.screenerquote.scannerprod=INFO
module.logging.path=/home/<USER>/logs
loggingRoot=${logging.path}
log_root=${logging.path}
logging.config=classpath:log4j2-spring.xml

sofa_runtime_spring_major_version=4
com.alipay.sofa.ignore.unresolvable.placeholders=true

sofa.boot.ddcs.enableAutoRegister=true
com.alipay.sofa.antscheduler.enableMultiConnections=true
com.alipay.sofa.boot.skip-jvm-serialize = true

sofa.trigger.jvm.scan-packages = com.alipay.screenerquote.prod
sofa.trigger.useDeepCopy=false

sofamq_endpoint = ${sofamq.endpoint}
sofamq_schema_registry = ${sofamq.schema.registry}
sofamq.endpoint=antvip://sofamqnamesrv-pool.${sofa.domain.name}?ldc=true
sofamq.schema.registry=antvip://openmeta-pool.${sofa.domain.name}
domain.name=${sofa.domain.name}
inner.domain=${domain.name}