/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.service;

import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerPortfolio;
import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerWriteConfig;

/**
 * <AUTHOR>
 * @version ScannerWriteService.java, v 0.1 2025年02月14日 16:48 lzt
 */
public interface ScannerWriteService {

    void write(ScannerWriteConfig writeConfig, ScannerPortfolio portfolio);

    /**
     * 写入策略返回的股票组合
     * @param portfolio
     */
    void writePortfolio(ScannerPortfolio portfolio);
}