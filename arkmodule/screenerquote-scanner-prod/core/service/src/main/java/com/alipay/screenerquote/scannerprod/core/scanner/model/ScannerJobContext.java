/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.model;

import com.alipay.screenerquote.scannerprod.core.scanner.scheduler.model.ScannerTaskItem;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version ScannerJobContext.java, v 0.1 2025年02月24日 16:48 lzt
 */
@Getter
@Setter
@Builder
@ToString
public class ScannerJobContext implements Serializable {
    private static final long serialVersionUID = -2782710110866769540L;

    /**
     * job descriptor
     */
    private final ScannerJobDescriptor jobDescriptor;

    /**
     * task item
     */
    private final ScannerTaskItem scannerTaskItem;
}