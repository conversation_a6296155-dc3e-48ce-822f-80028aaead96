/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.nimitzquote.prod.common.dal.mapper.quotstoremultenv.nimitz;

import com.alipay.nimitzquote.prod.common.dal.model.quotstore.meta.nimitz.NimitzDatasetDimPO;
import com.alipay.quote.log.dal.DalMonitorInterceptor.DB;
import com.alipay.quote.log.dal.DalMonitorInterceptor.DalMonitor;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 数据集维度表 Mapper
 *
 * <AUTHOR>
 * @version $Id: NimitzDatasetDimMapper.java, v 0.1 2021年02月03日 3:07 PM haoda Exp $
 */
@DalMonitor(db = DB.MYSQL, dataSource = "quotstore")
public interface NimitzDatasetDimMapper {

    String COLUMNS = "id,gmt_create,gmt_modified,dataset_code,dim_code,dim_code_key,dim_order,is_valid";

    /**
     * 获取所有
     * @return
     */
    @Select("SELECT " + COLUMNS + " FROM nimitz_dataset_dim WHERE is_valid = 1")
    List<NimitzDatasetDimPO> getAllValid();
}