/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.service.impl;


import com.alipay.quote.log.logger.QuoteLog;
import com.alipay.screenerquote.scannerprod.core.scanner.model.ScannerJobDescriptor;
import com.alipay.screenerquote.scannerprod.log.DedicateLog;
import com.alipay.sofa.runtime.api.annotation.SofaReference;
import com.alipay.tradequote.meta.service.impl.trading.TradingService;
import org.apache.commons.collections4.CollectionUtils;
import org.quartz.CronExpression;
import org.slf4j.Logger;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL1_SCANNER;
import static com.alipay.screenerquote.scannerprod.log.LoggerConstant.BIZ_LEVEL2_SCHED;

/**
 * <AUTHOR>
 * @version ScannerJobTrigger.java, v 0.1 2025年02月24日 19:16 lzt
 */
public class ScannerJobTrigger {

    private static final Logger LOGGER = DedicateLog.PROD_SCHED_LOGGER;

    /**
     * trading service
     */
    @SofaReference
    private TradingService tradingService;

    public boolean isTriggered(ScannerJobDescriptor descriptor, Date triggerTime) {

        //判断isStop是否为true
        if (descriptor.isStop()) {
            return false;
        }

        //判断cron表达式列表
        if (CollectionUtils.isEmpty(descriptor.getCronList())) {
            //add log
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SCHED)
                    .ext("任务未配置任何cron表达式,descriptor:{0}", descriptor)
                    .build()
                    .warn();
            return false;
        }

        List<String> cronList = descriptor.getCronList();
        boolean isMatchAnyCron = cronList.stream().anyMatch(cron -> isCronTrigger(cron, triggerTime));
        if (!isMatchAnyCron) {
            return false;
        }

        //判断是否是交易日
        if (descriptor.isOnlyTradingDay()) {
            LocalDate localDate = triggerTime.toInstant()
                    .atZone(ZoneId.systemDefault()) // 使用系统默认时区
                    .toLocalDate();
            //使用沪深交易日判断
            boolean tradingDay = tradingService.isTradingDay(1L, localDate);
            if (!tradingDay) {
                return false;
            }
        }

        //返回判断结果
        return true;
    }


    private boolean isCronTrigger(String cron, Date expectedTriggerTime) {

        try {
            CronExpression expr = new CronExpression(cron);
            return expr.isSatisfiedBy(expectedTriggerTime);
        } catch (Exception e) {
            QuoteLog.Builder.newBuilder(LOGGER, BIZ_LEVEL1_SCANNER, BIZ_LEVEL2_SCHED)
                    .ext("检查任务是否可运行cronExpr异常,{0},{1}", cron, expectedTriggerTime)
                    .bizSuccess(false)
                    .throwable(e)
                    .build()
                    .error();
        }
        return false;
    }
}