/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.sched;

import com.alipay.logicview.sdk.integration.drm.LogicViewDrm;
import com.alipay.logicview.sdk.model.scheduler.ScheDescriptor;
import com.alipay.logicview.sdk.service.entry.LogicViewCacheSdkService;
import com.alipay.logicview.sdk.service.entry.LogicViewSDKRegister;
import com.alipay.sofa.runtime.api.healthcheck.Result;
import com.alipay.tradequote.core.falcon.screener.config.ScreenConfigService;
import com.alipay.tradequote.core.falcon.screener.view.LogicViewDatasourceImpl;
import com.alipay.tradequote.core.falcon.service.screener.config.ScreenerConfigService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.quartz.CronExpression;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class ScreenerStartupTest {
    @InjectMocks
    ScreenerStartup screenerStartup;

    @Mock
    ScreenerJobManager jobManager;

    @Mock
    ScreenerConfigService screenerConfigService;

    @Mock
    ScreenConfigService screenConfigService;

    @Mock
    LogicViewDrm logicViewDrm;

    @Mock
    LogicViewCacheSdkService logicViewCacheSdkService;

    @Mock
    LogicViewDatasourceImpl logicViewDatasource;

    @Mock
    LogicViewSDKRegister logicViewSDKRegister;


    @Before
    public void init() {
        screenerStartup = new ScreenerStartup();
        screenerStartup.setScreenJobManager(jobManager);
        screenerStartup.setScreenConfigService(screenConfigService);
        screenerStartup.setLogicViewDrm(logicViewDrm);
        screenerStartup.setLogicViewCacheSdkService(logicViewCacheSdkService);
        screenerStartup.setLogicViewSDKRegister(logicViewSDKRegister);
        screenerStartup.setLogicViewDatasource(logicViewDatasource);
    }

    @Test
    public void test1() throws Exception {
        assertEquals("ScreenerStartup", screenerStartup.getId());
        screenerStartup.setAppName("unknown");
        screenerStartup.startup();
        assertEquals(Result.healthy(), screenerStartup.isHealthy());
        verify(jobManager, times(0)).initExecute();
    }

    @Test
    public void test2() throws Exception {
        screenerStartup.setAppName("screenerquote");
        screenerStartup.startup();
        assertEquals(Result.healthy(), screenerStartup.isHealthy());
        verify(jobManager, times(2)).initExecute();
    }

    @Test
    public void test3() throws Exception {
        doThrow(new RuntimeException()).when(jobManager).initExecute();
        screenerStartup.setAppName("screenerquote");
        screenerStartup.startup();
        assertFalse(screenerStartup.isHealthy().isHealthy());
        verify(jobManager, times(1)).initExecute();
    }

    @Test
    public void screenerViewTest() throws Exception{
        screenerStartup.setAppName("screenerquote");
        when(screenConfigService.isOpenDefaultSched()).thenReturn(false);
        when(screenConfigService.isCacheStartUpLoad()).thenReturn(true);
        when(logicViewDrm.getViewSchedulerTypes()).thenReturn(descriptorList());
        screenerStartup.startup();
    }

    public List<ScheDescriptor> descriptorList() throws ParseException {
        List<ScheDescriptor> descriptorList = new ArrayList<>();
        ScheDescriptor descriptor = new ScheDescriptor();
        descriptor.setCode("test");
        descriptor.setDesc("test");
        descriptor.setStop(false);
        CronExpression cron = new CronExpression("0/1 * * * * ?");
        descriptor.setCronExpr(cron.getCronExpression());
        descriptorList.add(descriptor);
        return descriptorList;
    }

}
