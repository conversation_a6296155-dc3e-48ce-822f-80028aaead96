/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.log;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class LoggerConstant {
    /**
     * disable
     */
    private LoggerConstant() {
    }

    /**
     * 调度服务
     */
    public static final String BIZ_LEVEL1_SCANNER = "scanner";

    /**
     * zsearch
     */
    public static final String BIZ_LEVEL1_ZSEARCH = "zsearch";

    /**
     * ant scheduler
     */
    public static final String BIZ_LEVEL2_SCHED = "sched";

    /**
     * drm
     */
    public static final String BIZ_LEVEL2_DRM = "drm";

    /**
     * service
     */
    public static final String BIZ_LEVEL2_SERVICE = "service";


}
