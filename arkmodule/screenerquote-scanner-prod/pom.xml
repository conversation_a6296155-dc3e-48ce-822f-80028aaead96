<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <modules>
        <module>biz/service-impl</module>
        <module>common/service/facade</module>
        <module>bootstrap</module>
        <module>core/model</module>
        <module>core/service</module>
        <module>common/integration</module>
    </modules>

    <parent>
        <groupId>com.alipay.tradequote</groupId>
        <artifactId>tradequote-parent</artifactId>
        <version>1.0.0-20230621</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
 

    <groupId>com.alipay.scannerprod</groupId>
    <artifactId>screenerquote-scanner-prod</artifactId>
    <version>1.0.0</version>

    <properties>
        <maven.build.timestamp.format>yyyyMMddHHmmss</maven.build.timestamp.format>
        <surefire.version>2.22.2</surefire.version>
        <sofa.serverless.trigger.version>1.2.11</sofa.serverless.trigger.version>
        <serverlesstest.test>1.0-SNAPSHOT</serverlesstest.test>
        <testng.version>6.14.3</testng.version>
        <screenerquote.scannerprod.version>1.0.0</screenerquote.scannerprod.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alipay.scannerprod</groupId>
                <artifactId>screenerquote-scanner-prod-common-service-facade</artifactId>
                <version>1.0.0.20250220</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.scannerprod</groupId>
                <artifactId>screenerquote-scanner-prod-biz-service-impl</artifactId>
                <version>${screenerquote.scannerprod.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.scannerprod</groupId>
                <artifactId>screenerquote-scanner-prod-bootstrap</artifactId>
                <version>${screenerquote.scannerprod.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.scannerprod</groupId>
                <artifactId>screenerquote-scanner-prod-core-model</artifactId>
                <version>${screenerquote.scannerprod.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.scannerprod</groupId>
                <artifactId>screenerquote-scanner-prod-core-service</artifactId>
                <version>${screenerquote.scannerprod.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.scannerprod</groupId>
                <artifactId>screenerquote-scanner-prod-common-integration</artifactId>
                <version>${screenerquote.scannerprod.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.screenerquote-prod</groupId>
                <artifactId>screenerquote-prod-common-service-facade</artifactId>
                <version>1.0.0.20241212</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!--Ant Financial Services Group Main Site Must Dependency healthcheck-sofa-boot-starter when deployed online/offline -->
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>healthcheck-sofa-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
            <scope>provided</scope>
        </dependency>
        <!--
       为了关闭xml标签强校验
       @link http://gitlab.alipay-inc.com/alipay-SOFALite2/middleware-alipay-starters-parent/issues/102
       -->
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>runtime-alipay-sofa-boot-starter</artifactId>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <version>1.4</version>
                <configuration>
                    <timestampFormat>yyyyMMddHHmmss</timestampFormat>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>create-timestamp</goal>
                        </goals>
                    </execution>
                </executions>
                <inherited>false</inherited>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.8.1</version>
                <configuration>
                    <argLine>-Xmx1024m -XX:PermSize=256m -XX:MaxPermSize=512m -Dfile.encoding=UTF-8</argLine>
                    <skipTests>false</skipTests>
                    <includes>
                        <!-- 这里需要根据自己的需要指定要跑的单元测试 -->
                        <include>**/*Test*.java</include>
                    </includes>
                    <testFailureIgnore>true</testFailureIgnore>
                </configuration>
            </plugin>
            <!--plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.0.2</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin-->
        </plugins>
    </build>

</project>
