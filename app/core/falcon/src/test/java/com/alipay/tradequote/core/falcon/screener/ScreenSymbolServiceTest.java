/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener;

import com.alipay.quot.commons.facade.model.SymbolDTO;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.IndicatorDataWrapper;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ScreenCache;
import com.alipay.tradequote.core.falcon.screener.util.SymbolGQl;
import com.alipay.tradequote.meta.service.SymbolGqlService;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
public class ScreenSymbolServiceTest extends AbstractScreenerTest {

    @InjectMocks
    ScreenSymbolServiceImpl screenSymbolService = new ScreenSymbolServiceImpl();

    @Mock
    ScreenCache screenCache;

    @Mock
    SymbolGqlService symbolGqlService;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        SymbolGQl symbolGQl = new SymbolGQl();
        symbolGQl.init();
        screenSymbolService.setScreenCache(screenCache);
        screenSymbolService.setSymbolGqlService(symbolGqlService);
        screenSymbolService.setSymbolGQl(symbolGQl);
    }

    @Test
    public void test1() {
        when(screenCache.get(any())).thenReturn(null);
        when(symbolGqlService.querySymbol(any())).thenReturn(Lists.newArrayList());
        List<SymbolDTO> result = screenSymbolService.query("mkt=SH,SZ");
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void test2() throws Exception {
        List<SymbolDTO> symbols = getObjs("symbol_list_2.csv");
        IndicatorDataWrapper wrapper = new IndicatorDataWrapper(symbols);
        when(screenCache.get(any())).thenReturn(wrapper);
        when(symbolGqlService.querySymbol(any())).thenReturn(Lists.newArrayList());
        List<SymbolDTO> result = screenSymbolService.query("mkt=SH,SZ");
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }
}