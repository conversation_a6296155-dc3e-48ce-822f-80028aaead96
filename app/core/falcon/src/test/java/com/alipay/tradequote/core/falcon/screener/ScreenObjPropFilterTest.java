/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener;

import com.alipay.quot.commons.facade.model.ScreenObjPropDTO;
import com.alipay.quot.commons.facade.model.SymbolDTO;
import com.alipay.tradequote.core.falcon.screener.util.ScreenObjPropFilter;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 */
public class ScreenObjPropFilterTest extends AbstractScreenerTest {

    /**
     * getter
     */
    final Function<SymbolDTO, String> getMarket = SymbolDTO::getMarket;
    final Function<SymbolDTO, String> getType = SymbolDTO::getType;
    final Function<SymbolDTO, String> getSubType= SymbolDTO::getSubType;
    final Function<SymbolDTO, String> getListType = SymbolDTO::getListType;

    @Test
    public void test1() throws Exception {
        List<SymbolDTO> symbols = getObjs("symbol_list_2.csv");
        ScreenObjPropDTO prop = new ScreenObjPropDTO();
        prop.setMarkets(Lists.newArrayList("SH"));
        ScreenObjPropFilter filter = new ScreenObjPropFilter(prop, new HashSet<>());
        List<SymbolDTO> filtered = symbols.stream().filter(filter).collect(Collectors.toList());
        assertNotNull(filtered);
        for (SymbolDTO dto : filtered) {
            assertEquals("SH", dto.getMarket());
        }
    }

    @Test
    public void test2() throws Exception {
        List<SymbolDTO> symbols = getObjs("symbol_list_2.csv");
        ScreenObjPropDTO prop = new ScreenObjPropDTO();
        prop.setMarkets(Lists.newArrayList("SZ"));
        ScreenObjPropFilter filter = new ScreenObjPropFilter(prop, new HashSet<>());
        List<SymbolDTO> filtered = symbols.stream().filter(filter).collect(Collectors.toList());
        assertNotNull(filtered);
        for (SymbolDTO dto : filtered) {
            assertEquals("SZ", dto.getMarket());
        }
    }

    @Test
    public void test3() throws Exception {
        List<SymbolDTO> symbols = getObjs("symbol_list_2.csv");
        ScreenObjPropDTO prop = new ScreenObjPropDTO();
        prop.setMarkets(Lists.newArrayList("SZ"));
        prop.setTypes(Lists.newArrayList("ES"));
        ScreenObjPropFilter filter = new ScreenObjPropFilter(prop, new HashSet<>());
        List<SymbolDTO> filtered = symbols.stream().filter(filter).collect(Collectors.toList());
        assertNotNull(filtered);
        for (SymbolDTO dto : filtered) {
            assertEquals("SZ", dto.getMarket());
            assertEquals("ES", dto.getType());
        }
    }

    @Test
    public void test4() throws Exception {
        List<SymbolDTO> symbols = getObjs("symbol_list_2.csv");
        ScreenObjPropDTO prop = new ScreenObjPropDTO();
        prop.setMarkets(Lists.newArrayList("SZ"));
        prop.setTypes(Lists.newArrayList("ES"));
        prop.setSubTypes(Lists.newArrayList("ASH"));
        ScreenObjPropFilter filter = new ScreenObjPropFilter(prop, Sets.newHashSet("List_D"));
        List<SymbolDTO> filtered = symbols.stream().filter(filter).collect(Collectors.toList());
        assertNotNull(filtered);
        for (SymbolDTO dto : filtered) {
            assertEquals("SZ", dto.getMarket());
            assertEquals("ES", dto.getType());
            assertEquals("ASH", dto.getSubType());
        }
    }

    @Test
    public void test5() throws Exception {
        testObjFilter("SH,SZ", "ES", "ASH", "ListType_D,ListType_F");
        testObjFilter("SH,SZ", null, null, "ListType_D,ListType_F");
        testObjFilter("SH,SZ", "ES,MRI", null, "ListType_D,ListType_F");
        testObjFilter("SH,SZ", "ES,MRI", "ASH", "ListType_D,ListType_F");
        testObjFilter("SH", "ES,MRI", "ASH", "ListType_D,ListType_F");
        testObjFilter("SH", "ES,MRI,UNKNOWN", "ASH", "ListType_D,ListType_F");
    }

    /**
     * run test
     */
    public void testObjFilter(String marketsRaw, String typesRaw, String subTypesRaw, String listTypeRaw) throws Exception {
        List<String> markets = split(marketsRaw);
        List<String> types = split(typesRaw);
        List<String> subTypes = split(subTypesRaw);
        List<String> listTypes = split(listTypeRaw);
        ScreenObjPropDTO prop = new ScreenObjPropDTO();
        prop.setMarkets(markets);
        prop.setTypes(types);
        prop.setSubTypes(subTypes);
        ScreenObjPropFilter filter = new ScreenObjPropFilter(prop, new HashSet<>(listTypes));

        List<SymbolDTO> symbols = getObjs("symbol_list_2.csv");
        List<SymbolDTO> filtered = symbols.stream().filter(filter).collect(Collectors.toList());
        assertNotNull(filtered);
        asserter(filtered, markets, getMarket);
        asserter(filtered, types, getType);
        asserter(filtered, subTypes, getSubType);
        asserter(filtered, listTypes, getListType);
    }


    /**
     * splitter
     */
    @SuppressWarnings("UnstableApiUsage")
    public List<String> split(String raw) {
        if (raw == null || raw.trim().isEmpty()) {
            return new ArrayList<>();
        }

        return Splitter.on(",").trimResults().omitEmptyStrings().splitToList(raw);
    }

    /**
     * assert
     */
    public void asserter(List<SymbolDTO> data, Collection<String> expected, Function<SymbolDTO, String> fieldGetter) {
        if (expected == null || expected.isEmpty()) {
            return;
        }

        for (SymbolDTO dto : data) {
            assertTrue(expected.contains(fieldGetter.apply(dto)));
        }
    }
}
