package com.alipay.tradequote.core.falcon.screener.view.cache;

import com.alipay.quote.nerve.qsql.lib.viewCell.ViewCell;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ObjIdKeyWrapper;
import com.alipay.tradequote.core.falcon.screener.indicator.cache.ScreenCacheKey;
import com.alipay.tradequote.core.falcon.screener.view.sched.IndicatorViewCacheSync;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class ViewCacheTest {

    @Mock
    private IndicatorViewCacheSync indicatorViewCacheSync;

    @InjectMocks
    private ViewCacheImpl viewCache;

    private ScreenCacheKey mockKey;
    private ConcurrentHashMap<String, ViewCell> mockValue;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        mockKey = new ObjIdKeyWrapper("objId", "objId");
        mockValue = new ConcurrentHashMap<>();
        when(indicatorViewCacheSync.syncIndicatorViewCache(any(ScreenCacheKey.class))).thenReturn(mockValue);
        viewCache.initAsync(); // Initialize the async cache
    }

    @Test
    public void testGetShouldReturnExistingValue() {
        viewCache.put(mockKey, mockValue);
        CompletableFuture<ConcurrentHashMap<String, ViewCell>> result = viewCache.get(mockKey);

        assertDoesNotThrow(() -> result.join());
        assertEquals(mockValue, result.join());

        CompletableFuture<ConcurrentHashMap<String, ViewCell>> result2 = viewCache.get(mockKey);

        assertDoesNotThrow(() -> result2.join());
        assertEquals(mockValue, result2.join());
    }

    @Test
    public void testPutAllShouldAddAllEntries() {
        Map<ScreenCacheKey, ConcurrentHashMap<String, ViewCell>> data = new HashMap<>();
        data.put(mockKey, mockValue);

        viewCache.putAll(data);
        CompletableFuture<ConcurrentHashMap<String, ViewCell>> result = viewCache.get(mockKey);

        assertDoesNotThrow(() -> result.join());
        assertEquals(mockValue, result.join());
    }

    @Test
    public void testGetAllShouldReturnAllValues() {
        List<ScreenCacheKey> keys = new ArrayList<>();
        keys.add(mockKey);

        viewCache.put(mockKey, mockValue);
        CompletableFuture<Map<ScreenCacheKey, ConcurrentHashMap<String, ViewCell>>> result = viewCache.getAll(keys);

        assertDoesNotThrow(() -> result.join());
        assertTrue(result.join().containsKey(mockKey));
        assertEquals(mockValue, result.join().get(mockKey));
    }

    @Test
    public void testPutShouldStoreValue() {
        viewCache.put(mockKey, mockValue);
        CompletableFuture<ConcurrentHashMap<String, ViewCell>> result = viewCache.get(mockKey);

        assertDoesNotThrow(() -> result.join());
        assertEquals(mockValue, result.join());
    }

    @Test
    public void testViewKeySetShouldReturnAllKeys() {
        viewCache.put(mockKey, mockValue);
        Set<ScreenCacheKey> keySet = viewCache.viewKeySet();

        assertNotNull(keySet);
        assertTrue(keySet.contains(mockKey));
    }
}

