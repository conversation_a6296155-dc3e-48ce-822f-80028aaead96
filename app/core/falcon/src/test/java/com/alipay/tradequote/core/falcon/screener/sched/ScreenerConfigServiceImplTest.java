package com.alipay.tradequote.core.falcon.screener.sched;

import com.alipay.tradequote.core.falcon.service.screener.config.ScreenerConfigServiceImpl;
import com.alipay.tradequote.core.falcon.service.screener.config.ScreenerDrm;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class ScreenerConfigServiceImplTest {

    @InjectMocks
    private ScreenerConfigServiceImpl screenerConfigServiceImpl;

    @Mock
    ScreenerDrm screenerDrm;


    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        screenerConfigServiceImpl = new ScreenerConfigServiceImpl();
    }

    @Test
    public void testGetStrategy() {
        screenerConfigServiceImpl.isCacheStartUpLoad();
    }
}
