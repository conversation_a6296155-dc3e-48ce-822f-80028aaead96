/*
 * Ant Group
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.core.scanner.model;

import com.alipay.quot.commons.facade.model.querycond.PageCond;
import com.alipay.quot.commons.facade.model.querycond.RangeCond;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version ScannerPortfolioBO.java, v 0.1 2025年02月25日 16:36 lzt
 */
@Getter
@Setter
@ToString
@Builder
public class ScannerPortfolioBO implements Serializable {

    private static final long serialVersionUID = 1493408269931657529L;

    /**
     * 策略Id
     */
    private String strategyId;

    /**
     * 起始标的
     */
    private String srcId;

    /**
     * queryRange
     */
    private RangeCond rangeCond;

    /**
     * pageCond
     */
    private PageCond pageCond;

    /**
     * 是否倒序，默认为true
     */
    private Boolean reversed;
}