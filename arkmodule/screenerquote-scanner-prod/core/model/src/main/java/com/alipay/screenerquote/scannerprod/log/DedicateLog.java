/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2020 All Rights Reserved.
 */
package com.alipay.screenerquote.scannerprod.log;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 日志定义
 *
 * <AUTHOR>
 * @version $Id: DedicateLog.java, v 0.1 2023-05-17 10:55 AM huyao Exp $$
 */
public class DedicateLog {

    /**
     * 模块biz层服务查询日志
     */
    public static final Logger PROD_BIZ_SERVICE_LOGGER = LoggerFactory
            .getLogger("SCREENERQUOTE-SCANNER-PROD-BIZ-SERVICE");


    /**
     * 模块调度层日志
     */
    public static final Logger PROD_SCHED_LOGGER = LoggerFactory
            .getLogger("SCREENERQUOTE-SCANNER-PROD-SCHED");

    /**
     * 模块zsearch服务日志
     */
    public static final Logger PROD_ZSEARCH_LOGGER = LoggerFactory
            .getLogger("SCREENERQUOTE-SCANNER-PROD-ZSEARCH");


}