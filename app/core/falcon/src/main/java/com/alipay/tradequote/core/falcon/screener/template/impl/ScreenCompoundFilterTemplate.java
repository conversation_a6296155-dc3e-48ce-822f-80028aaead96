/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.template.impl;

import com.alibaba.common.lang.StringUtil;
import com.alipay.quot.commons.facade.model.querycond.SortCond;
import com.alipay.quot.commons.models.ScreenerProto;
import com.alipay.quot.commons.models.ScreenerProto.*;
import com.alipay.quot.commons.models.querycond.SortCondOuterClass;
import com.alipay.quot.commons.screener.ScreenConvert;
import com.alipay.quote.hulk.thread.DefaultCoordinator;
import com.alipay.quote.log.logger.QuoteLog;
import com.alipay.tradequote.core.falcon.log.DedicateLog;
import com.alipay.tradequote.core.falcon.screener.config.ScreenConfigService;
import com.alipay.tradequote.core.falcon.screener.model.template.*;
import com.alipay.tradequote.core.falcon.screener.model.template.ScreenObj;
import com.alipay.tradequote.core.falcon.screener.template.ScreenTemplate;
import com.alipay.tradequote.core.falcon.screener.util.ScreenUtil;
import com.google.common.collect.ImmutableList;
import lombok.Setter;
import org.slf4j.Logger;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.alipay.quot.commons.screener.ScreenConvert.pbToDTO;
import static com.alipay.tradequote.core.falcon.screener.util.Constants.SCREENER;
import static com.alipay.tradequote.core.falcon.screener.util.Constants.TEMPLATE;

/**
 * <AUTHOR>
 */
@Setter
public class ScreenCompoundFilterTemplate implements ScreenTemplate {
    /**
     * logger
     */
    private static final Logger LOGGER = DedicateLog.BIZ_FALCON_SCREENER;

    /**
     * scene
     */
    private static final String SCENE = "CompoundFilterTemplate";

    /**
     * objFilter
     */
    private ObjFilter objFilter;

    /**
     * sort
     */
    private IndicatorSort indicatorSort;

    /**
     * collect
     */
    private SimpleCollect simpleCollect;

    /**
     * compound filter
     */
    private CompoundFilter compoundFilter;

    /**
     * filter
     */
    private CompoundZSearchFilter compoundZSearchFilter;

    /**
     * join
     */
    private FilterResultJoin filterResultJoin;

    /**
     * thread-pool
     */
    private DefaultCoordinator screenerFilterCoordinator;

    /**
     * service
     */
    private ScreenConfigService screenConfigService;


    @Override
    public ScreenResponse screen(ScreenCompoundFilterRequest request) {
        long start = System.currentTimeMillis();
        Context context = Context.builder().build();
        FromRequest fromRequest = FromRequest.builder()
                .objFilter(request.getObjFilter())
                .build();
        FromResult fromResult = objFilter.execute(context, fromRequest);
        QuoteLog.Builder.newBuilder(LOGGER, SCREENER, TEMPLATE)
                .bizUseTime(System.currentTimeMillis() - start)
                .bizSuccess(true)
                .bizScene(SCENE)
                .ext("from executed")
                .build()
                .info();

        final int totalObjCount = fromResult.getObjs().size();
        CompoundFilterResult filterResult = filter(fromResult.getObjs(), request.getFiltersList());
        QuoteLog.Builder.newBuilder(LOGGER, SCREENER, TEMPLATE)
                .bizUseTime(System.currentTimeMillis() - start)
                .bizSuccess(true)
                .bizScene(SCENE)
                .ext("filter executed")
                .build()
                .info();

        // 涉及到内存和zsearch两边数据不一样，需要对每个筛选出来的标的进行补充和对齐数据
        JoinRequest joinRequest = JoinRequest.builder()
                .filterResult(filterResult)
                .request(request)
                .build();
        JoinResult joinResult = filterResultJoin.execute(joinRequest);
        QuoteLog.Builder.newBuilder(LOGGER, SCREENER, TEMPLATE)
                .bizUseTime(System.currentTimeMillis() - start)
                .bizSuccess(true)
                .bizScene(SCENE)
                .ext("debug, packResult, {0}", joinResult)
                .build()
                .info();

        List<SortCond> sortConds = new ArrayList<>();
        for (SortCondOuterClass.SortCond sortCond : request.getSortCondsList()) {
            sortConds.add(ScreenConvert.pbToDTO(sortCond));
        }
        SortRequest sortRequest = SortRequest.builder()
                .objs(joinResult.getJoinedResult())
                .sortConds(sortConds)
                .pageCond(ScreenConvert.pbToDTO(request.getPageCond()))
                .percentCond(request.hasPercentCond() ? pbToDTO(request.getPercentCond()) : null)
                .build();
        SortResult sortResult = indicatorSort.execute(context, sortRequest);
        QuoteLog.Builder.newBuilder(LOGGER, SCREENER, TEMPLATE)
                .bizUseTime(System.currentTimeMillis() - start)
                .bizSuccess(true)
                .bizScene(SCENE)
                .ext("sort executed")
                .build()
                .info();

        Set<String> indicators = ScreenUtil.getRequestIndicators(request);
        CollectRequest collectRequest = CollectRequest.builder()
                .objs(sortResult.getObjs())
                .requestIndicators(indicators)
                .build();
        CollectResult collectResult = simpleCollect.execute(context, collectRequest);
        QuoteLog.Builder.newBuilder(LOGGER, SCREENER, TEMPLATE)
                .bizUseTime(System.currentTimeMillis() - start)
                .bizSuccess(true)
                .bizScene(SCENE)
                .ext("collect executed")
                .build()
                .info();

        return ScreenerProto.ScreenResponse.newBuilder()
                .addAllObjs(collectResult.getScreenObjs())
                .setTotalCount(sortResult.getTotalCount())
                .setTotalObjCount(totalObjCount)
                .build();
    }


    /**
     * contains
     */
    private boolean contains(ScreenObj obj, List<ScreenObj> objs) {
        return objs.stream().anyMatch(o -> StringUtil.equals(obj.getObjId(), o.getObjId()));
    }

    /**
     * @return filter callable
     */
    private Callable<CompoundFilterResult> makeFilterCallable(List<ScreenObj> objs, ScreenCompoundFilter filter) {
        if (isZSearchCompoundFilter(filter)) {
            return () -> compoundZSearchFilter.screen(ImmutableList.copyOf(objs), filter);
        } else {
            return () -> compoundFilter.screen(ImmutableList.copyOf(objs), filter);
        }
    }

    /**
     * 执行组合条件筛选
     */
    private CompoundFilterResult filter(List<ScreenObj> objs, List<ScreenCompoundFilter> filters) {
        List<ScreenCompoundFilter> compoundFilters = ScreenUtil.rearrangeFilters(filters, screenConfigService.getZSearchIndicators());

        long start = System.currentTimeMillis();
        if (compoundFilters.isEmpty()) {
            return CompoundFilterResult.builder()
                    .objs(objs)
                    .filterIndicators(new HashSet<>())
                    .build();
        }

        // 支一个compound filter，直接执行
        if (compoundFilters.size() == 1) {
            ScreenCompoundFilter filter = compoundFilters.get(0);
            if (isZSearchCompoundFilter(filter)) {
                return compoundZSearchFilter.screen(objs, filter);
            } else {
                return compoundFilter.screen(objs, filter);
            }
        }

        try {
            // 对每个子条件进行多线程计算然后求并集
            List<Callable<CompoundFilterResult>> callables = compoundFilters
                    .stream()
                    .map(filter -> makeFilterCallable(objs, filter))
                    .collect(Collectors.toList());
            List<Future<CompoundFilterResult>> futures = screenerFilterCoordinator.executor().invokeAll(callables);
            CompoundFilterResult seed = futures.get(0).get();
            Stream<ScreenObj> seedStream = Optional.ofNullable(seed.getObjs()).orElse(new ArrayList<>()).stream();
            Set<String> seedIndicators = Optional.ofNullable(seed.getFilterIndicators()).orElse(new HashSet<>());
            for (int i = 1; i < futures.size(); i++) {
                CompoundFilterResult result = futures.get(i).get();
                List<ScreenObj> subObjs = Optional.ofNullable(result.getObjs()).orElse(new ArrayList<>());
                seedStream = seedStream.filter(obj -> contains(obj, subObjs));
                Optional.ofNullable(result.getFilterIndicators()).ifPresent(seedIndicators::addAll);
            }

            List<ScreenObj> combined = seedStream.collect(Collectors.toList());
            return CompoundFilterResult.builder()
                    .objs(combined)
                    .filterIndicators(seedIndicators)
                    .build();
        } catch (InterruptedException | ExecutionException e) {
            QuoteLog.Builder.newBuilder(LOGGER, SCREENER, TEMPLATE)
                    .bizUseTime(System.currentTimeMillis() - start)
                    .bizSuccess(false)
                    .bizScene(SCENE)
                    .ext("filter执行异常")
                    .throwable(e)
                    .build()
                    .error();
        }

        return CompoundFilterResult.builder()
                .objs(new ArrayList<>())
                .filterIndicators(new HashSet<>())
                .build();
    }

    /**
     * 复合条件（CompoundFilter) 中如果有Condition包含zsearch的指标那么就返回true
     */
    private boolean isZSearchCompoundFilter(ScreenCompoundFilter cf) {
        return ScreenUtil.hasIndicators(cf, screenConfigService.getZSearchIndicators());
    }
}
