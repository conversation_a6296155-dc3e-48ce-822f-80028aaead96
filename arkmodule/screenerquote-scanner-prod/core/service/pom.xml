<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>screenerquote-scanner-prod</artifactId>
        <groupId>com.alipay.scannerprod</groupId>
        <version>1.0.0</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>screenerquote-scanner-prod-core-service</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>commons-push-model</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>commons-model</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>quote-hulk</artifactId>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>sofa-middleware-log</artifactId>
                    <groupId>com.alipay.sofa.common.log</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>quote-log</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.alipay.scannerprod</groupId>
            <artifactId>screenerquote-scanner-prod-core-model</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.scannerprod</groupId>
            <artifactId>screenerquote-scanner-prod-common-integration</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.tradequote</groupId>
            <artifactId>tradequote-core-falcon</artifactId>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>scheduler-alipay-sofa-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.alipay.tradequote</groupId>
            <artifactId>tradequote-common-service-facade</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>quote-nerve</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>quote-search</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.quote</groupId>
            <artifactId>commons-utils</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofa-serverless-trigger-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofamq-alipay-sofa-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.quotstore</groupId>
            <artifactId>bus-sdk-v2</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.alipay.quotstore</groupId>
            <artifactId>bus-client-v2</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.zeromq</groupId>
            <artifactId>jeromq</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.avro</groupId>
            <artifactId>avro</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofamq-client-all</artifactId>
            <!-- 参见 Release Note https://yuque.antfin.com/middleware/sofamq/release_notes sofamq-client-all^3.1.5 -->
            <scope>provided</scope>
        </dependency>

    </dependencies>

</project>