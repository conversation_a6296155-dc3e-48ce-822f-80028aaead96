/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tradequote.core.falcon.screener.template.impl;

import com.alibaba.common.lang.StringUtil;
import com.alipay.logicview.sdk.model.request.IndicatorViewQueryRequest;
import com.alipay.logicview.sdk.model.response.IndicatorViewQueryResponse;
import com.alipay.logicview.sdk.service.entry.IndicatorViewZSearchQueryService;
import com.alipay.quot.commons.facade.model.querycond.SortCond;
import com.alipay.quot.commons.models.ScreenerProto;
import com.alipay.quote.log.logger.QuoteLog;
import com.alipay.quote.nerve.qsql.ast.AstBuilder3;
import com.alipay.quote.nerve.qsql.ast.Node;
import com.alipay.quote.nerve.qsql.pushdown.ZSearchQueryTranslator;
import com.alipay.sofa.runtime.api.annotation.SofaReference;
import com.alipay.tradequote.core.falcon.log.DedicateLog;
import com.alipay.tradequote.core.falcon.screener.model.template.CompoundFilterResult;
import com.alipay.tradequote.core.falcon.screener.model.template.ScreenObj;
import com.alipay.tradequote.core.falcon.screener.util.ScreenUtil;
import com.alipay.zsearch.core.query.QueryBuilder;
import lombok.Setter;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.List;

import static com.alipay.tradequote.core.falcon.log.BizConstants.SCREENER;
import static com.alipay.tradequote.core.falcon.screener.util.Constants.TEMPLATE;


/**
 * <AUTHOR>
 */
@Setter
public class CompoundZSearchFilter {
    /**
     * logger
     */
    private static final Logger LOGGER = DedicateLog.BIZ_FALCON_SCREENER;

    /**
     * logger
     */
    private static final Logger LOGGER_DEBUG = DedicateLog.BIZ_FALCON_SCREENER_DEBUG;

    /**
     * constant
     */
    private static final String SCENE = "CompoundSearchFilter";

    /**
     * constant
     */
    private static final String SCENE_DEBUG = "CompoundSearchFilter_Debug";

    /**
     * size
     */
    private static final int LIMIT = 5000;

    /**
     * service
     */
    @SofaReference
    private IndicatorViewZSearchQueryService indicatorViewZSearchQueryService;


    /**
     * screen
     */
    public CompoundFilterResult screen(List<ScreenObj> objs, ScreenerProto.ScreenCompoundFilter filter) {
        try {
            AstBuilder3 ast = new AstBuilder3();
            Node node = ast.build(filter);
            ZSearchQueryTranslator translator = new ZSearchQueryTranslator();
            QueryBuilder qb = translator.translate(node);
            QuoteLog.Builder.newBuilder(LOGGER, SCREENER, TEMPLATE)
                    .bizScene(SCENE)
                    .bizSuccess(true)
                    .ext("filter from zsearch, {0}, {1}, {2}", node, qb, filter)
                    .build()
                    .info();

            IndicatorViewQueryRequest request = new IndicatorViewQueryRequest();
            request.setQueryAst(node);
            if (filter.hasPageCond() && filter.getPageCond().getLimit() > 0) {
                request.setLimit(filter.getPageCond().getLimit());
            } else {
                request.setLimit(LIMIT);
            }
            if (filter.hasSortCond() && StringUtil.isNotBlank(filter.getSortCond().getField())) {
                SortCond sortCond = new SortCond();
                sortCond.setField(filter.getSortCond().getField());
                sortCond.setDesc(filter.getSortCond().getDesc());
                request.setSortCond(sortCond);
            } else {
                request.setSortCond(null);
            }

            IndicatorViewQueryResponse response = indicatorViewZSearchQueryService.queryByDsl(request);
            List<ScreenObj> screenedObjs = ScreenUtil.convertToScreenObjList(response, true);
            QuoteLog.Builder.newBuilder(LOGGER_DEBUG, SCREENER, TEMPLATE)
                    .bizScene(SCENE_DEBUG)
                    .bizSuccess(true)
                    .ext("filter from zsearch result, {0}", screenedObjs)
                    .build()
                    .info();
            return CompoundFilterResult
                    .builder()
                    .objs(screenedObjs)
                    .filterIndicators(ScreenUtil.getIndicatorsFromCompoundFilter(filter))
                    .build();
        } catch (Exception e) {
            QuoteLog.Builder.newBuilder(LOGGER, SCREENER, TEMPLATE)
                    .bizScene(SCENE)
                    .bizSuccess(false)
                    .ext("从zsearch筛选出现异常,{0}", filter)
                    .throwable(e)
                    .build()
                    .error();

            return CompoundFilterResult
                    .builder()
                    .objs(new ArrayList<>())
                    .filterIndicators(ScreenUtil.getIndicatorsFromCompoundFilter(filter))
                    .build();
        }
    }
}
